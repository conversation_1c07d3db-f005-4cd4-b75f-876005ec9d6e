# Great Nigeria Library 🇳🇬

> **Awakening a Giant. Reversing a National Nightmare.**

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/greatnigeria/library)
[![Coverage](https://img.shields.io/badge/coverage-85%25-yellow.svg)](https://codecov.io/gh/greatnigeria/library)
[![Go Version](https://img.shields.io/badge/go-1.21+-blue.svg)](https://golang.org/dl/)
[![React Version](https://img.shields.io/badge/react-18.2+-blue.svg)](https://reactjs.org/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Contributors](https://img.shields.io/badge/contributors-welcome-orange.svg)](CONTRIBUTING.md)

![Great Nigeria Library Banner](docs/assets/banner.png)

## Vision Statement

The Great Nigeria Library is more than a digital platform—it's a movement to transform Nigeria's educational landscape through technology, community, and Nigerian excellence. We're building the world's most comprehensive repository of Nigerian knowledge, culture, and educational resources, designed to serve millions of learners across Africa and the diaspora.

## Who Is This For?

### 📚 **Students & Learners**
- Primary, secondary, and tertiary students seeking quality educational content
- Self-directed learners pursuing knowledge in Nigerian history, culture, and academics
- Diaspora Nigerians wanting to stay connected to their heritage

### 👩‍🏫 **Educators & Content Creators**
- Teachers looking for comprehensive Nigerian-focused curriculum resources
- Authors and content creators wanting to share knowledge with a broad audience
- Educational institutions seeking to digitize and expand their reach

### 💻 **Developers & Contributors**
- Full-stack developers interested in educational technology
- Open-source contributors passionate about African tech innovation
- Technical professionals wanting to give back to Nigerian education

### 🏛️ **Institutions & Organizations**
- Educational institutions seeking digital transformation
- Cultural organizations preserving Nigerian heritage
- Government agencies promoting educational access

## Technical Philosophy

### 🎯 **Scalability-First Design**
Built from the ground up to serve millions of concurrent users across Africa and beyond. Our microservices architecture ensures each component can scale independently.

### 🔒 **Security & Privacy**
Zero-trust security model with end-to-end encryption, robust authentication, and GDPR compliance. Your data is yours, period.

### 🌍 **Offline-First Experience**
Progressive Web App (PWA) capabilities ensure learning continues even with intermittent connectivity—critical for the African context.

### 🎨 **Culturally Responsive Design**
Every interface element, color scheme, and interaction pattern is designed with Nigerian and African users in mind.

### 🚀 **Performance Obsessed**
Sub-second load times, aggressive caching, and CDN distribution ensure lightning-fast access regardless of location.

## Quick Start Guide

### For Developers

#### Prerequisites
- **Go 1.21+** ([Download](https://golang.org/dl/))
- **Node.js 18+** ([Download](https://nodejs.org/))
- **PostgreSQL 15+** ([Download](https://postgresql.org/download/))
- **Redis 7+** ([Download](https://redis.io/download))
- **Git** ([Download](https://git-scm.com/downloads))

#### 1. Clone & Setup
```bash
# Clone the repository
git clone https://github.com/greatnigeria/library.git
cd library

# Install backend dependencies
go mod download

# Install frontend dependencies
cd frontend
npm install
cd ..
```

#### 2. Database Setup
```bash
# Start PostgreSQL and Redis (using Docker)
docker-compose up -d postgres redis

# Run migrations
go run cmd/migrate/main.go up

# Populate with sample data
go run scripts/populate_sample_data.go
```

#### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit with your configuration
nano .env
```

#### 4. Start Services
```bash
# Terminal 1: Start API Gateway
go run cmd/api-gateway/main.go

# Terminal 2: Start Auth Service
go run cmd/auth-service/main.go

# Terminal 3: Start Content Service
go run cmd/content-service/main.go

# Terminal 4: Start Frontend
cd frontend && npm start
```

#### 5. Verification
Open your browser to `http://localhost:3000` and you should see:
- ✅ Login/Registration working
- ✅ Book content loading
- ✅ Real-time features functional
- ✅ Community features accessible

**Having issues?** Check our [Troubleshooting Guide](docs/02_development_guide.md#troubleshooting).

### For Content Contributors

#### Adding Books & Chapters
```bash
# Use our content importer
go run cmd/content-importer/main.go --file your-book.json --dry-run

# Or use the web interface
# Navigate to /admin/content-import after login
```

#### Contributing to Celebrate Nigeria
```bash
# Populate celebration data
go run scripts/populate_celebrate_nigeria.go

# Submit new entries via web interface at /celebrate/submit
```

## Platform Features

### 🎓 **Core Learning Platform**
- **Interactive Book Reader**: PDF, EPUB, and web-native formats
- **Progress Tracking**: Detailed analytics and milestone celebrations
- **Adaptive Learning**: AI-powered personalization based on learning style
- **Assessment Engine**: Quizzes, assignments, and peer review

### 🎭 **Celebrate Nigeria**
- **Digital Heritage**: Comprehensive database of Nigerian excellence
- **People, Places, Events**: Interactive exploration of Nigerian culture
- **Community Curation**: User-generated content with moderation
- **Educational Integration**: Seamlessly woven into learning materials

### 👥 **Community Features**
- **Discussion Forums**: Topic-based discussions with expert moderation
- **Study Groups**: Virtual and location-based learning communities
- **Mentorship**: Connect learners with educators and professionals
- **Livestream Events**: Real-time lectures, cultural events, and Q&As

### 💰 **Creator Economy**
- **Content Monetization**: Multiple revenue streams for educators
- **Virtual Gifts**: Support creators during livestreams
- **Subscription Tiers**: Flexible access models
- **Creator Analytics**: Detailed insights into content performance

### 📱 **Mobile & Offline**
- **Progressive Web App**: Native app experience in any browser
- **Offline Reading**: Download content for offline access
- **Sync Across Devices**: Seamless experience across all platforms
- **Push Notifications**: Stay engaged with personalized alerts

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Web     │    │   Mobile PWA    │    │   Admin Panel   │
│   Application   │    │   Application   │    │   Application   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │       API Gateway        │
                    │   (Auth, Rate Limiting)  │
                    └─────────────┬─────────────┘
                                 │
            ┌────────────────────┼────────────────────┐
            │                   │                    │
    ┌───────▼───────┐  ┌────────▼────────┐  ┌───────▼───────┐
    │ Auth Service   │  │ Content Service │  │ Social Service │
    │               │  │                │  │               │
    └───────────────┘  └─────────────────┘  └───────────────┘
            │                   │                    │
            └────────────────────┼────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     PostgreSQL Cluster   │
                    │     (Multi-Master)      │
                    └───────────────────────────┘
```

## Contributing

We welcome contributions from developers, educators, content creators, and community members! 

### How to Contribute
1. **📖 Read**: [Development Guide](docs/02_development_guide.md)
2. **🔍 Find**: Check [open issues](https://github.com/greatnigeria/library/issues)
3. **💬 Discuss**: Join our [Discord community](https://discord.gg/greatnigeria)
4. **🛠️ Build**: Follow our [coding standards](docs/02_development_guide.md#coding-standards)
5. **📝 Document**: Update docs with your changes
6. **🚀 Submit**: Create a pull request with clear description

### Priority Areas
- **🌍 Localization**: Nigerian languages and regional dialects
- **📚 Content**: Educational materials and cultural content
- **♿ Accessibility**: Ensuring platform works for all users
- **📱 Mobile**: Native mobile app development
- **🤖 AI/ML**: Intelligent content recommendations

## Community & Support

### 💬 **Get Help**
- **Documentation**: [docs/](docs/)
- **Troubleshooting**: [Development Guide](docs/02_development_guide.md#troubleshooting)
- **Community Forum**: [discussions](https://github.com/greatnigeria/library/discussions)
- **Discord**: [Join our server](https://discord.gg/greatnigeria)

### 📢 **Stay Updated**
- **GitHub**: Watch this repository for updates
- **Twitter**: [@GreatNigeriaLib](https://twitter.com/GreatNigeriaLib)
- **Newsletter**: [Subscribe here](https://greatnigeria.net/newsletter)
- **Blog**: [greatnigeria.net/blog](https://greatnigeria.net/blog)

### 🤝 **Partner With Us**
- **Educational Institutions**: [<EMAIL>](mailto:<EMAIL>)
- **Content Creators**: [<EMAIL>](mailto:<EMAIL>)
- **Technology Partners**: [<EMAIL>](mailto:<EMAIL>)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- **Nigerian Educational Community**: For inspiring this vision
- **Open Source Contributors**: For building the tools we stand on
- **Cultural Heritage Keepers**: For preserving the stories we share
- **African Tech Ecosystem**: For proving African solutions can be world-class

---

<div align="center">

**Built with ❤️ in Nigeria for the world**

*Awakening a Giant. Reversing a National Nightmare.*

[Website](https://greatnigeria.net) • [Documentation](docs/) • [Community](https://discord.gg/greatnigeria) • [Support](mailto:<EMAIL>)

</div>