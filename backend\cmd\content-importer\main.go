package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"database/sql"
	_ "github.com/lib/pq"
)

// Chapter represents a book chapter with sections
type Chapter struct {
	Number   string             `json:"Number"`
	Title    string             `json:"Title"`
	Sections map[string]Section `json:"Sections"`
}

// Section represents a book section
type Section struct {
	Number string `json:"Number"`
	Title  string `json:"Title"`
}

func main() {
	// Parse command line flags
	bookNumber := flag.String("book", "1", "Book number (1, 2, or 3)")
	chaptersFile := flag.String("chapters-file", "", "JSON file containing chapters data")
	createForumTopics := flag.Bool("create-forum-topics", false, "Create forum topics for sections")
	dryRun := flag.Bool("dry-run", false, "Dry run (don't make any changes)")
	dbConnStr := flag.String("db", "postgres://postgres:postgres@localhost/great_nigeria?sslmode=disable", "Database connection string")
	flag.Parse()

	// Validate book number
	if *bookNumber != "1" && *bookNumber != "2" && *bookNumber != "3" {
		log.Fatalf("Invalid book number: %s. Must be 1, 2, or 3.", *bookNumber)
	}

	// Read chapters data from file
	if *chaptersFile == "" {
		log.Fatalf("Chapters file is required")
	}

	chaptersData, err := ioutil.ReadFile(*chaptersFile)
	if err != nil {
		log.Fatalf("Failed to read chapters file: %v", err)
	}

	// Parse chapters data
	chapters := make(map[string]Chapter)
	err = json.Unmarshal(chaptersData, &chapters)
	if err != nil {
		log.Fatalf("Failed to parse chapters data: %v", err)
	}

	fmt.Printf("Found %d chapters in Book %s\n", len(chapters), *bookNumber)

	// Connect to database (unless dry run)
	var db *sql.DB
	if !*dryRun {
		db, err = sql.Open("postgres", *dbConnStr)
		if err != nil {
			log.Fatalf("Failed to connect to database: %v", err)
		}
		defer db.Close()

		// Test database connection
		err = db.Ping()
		if err != nil {
			log.Fatalf("Failed to ping database: %v", err)
		}
		fmt.Println("Connected to database")
	}

	// Get or create book
	bookID, err := getOrCreateBook(db, *bookNumber, *dryRun)
	if err != nil {
		log.Fatalf("Failed to get or create book: %v", err)
	}

	// Import chapters and sections
	for _, chapterNum := range sortedKeys(chapters) {
		chapter := chapters[chapterNum]
		
		// Create chapter
		chapterID, err := createChapter(db, bookID, chapter, *dryRun)
		if err != nil {
			log.Fatalf("Failed to create chapter %s: %v", chapterNum, err)
		}

		// Create sections
		for _, sectionNum := range sortedKeys(chapter.Sections) {
			section := chapter.Sections[sectionNum]
			
			// Create section
			sectionID, err := createSection(db, bookID, chapterID, section, *dryRun)
			if err != nil {
				log.Fatalf("Failed to create section %s: %v", sectionNum, err)
			}

			// Create forum topics if requested
			if *createForumTopics {
				err = createForumTopics(db, sectionID, section, *dryRun)
				if err != nil {
					log.Printf("Failed to create forum topics for section %s: %v", sectionNum, err)
				}
			}
		}
	}

	fmt.Println("Content import completed successfully!")
}

// getOrCreateBook gets or creates a book in the database
func getOrCreateBook(db *sql.DB, bookNumber string, dryRun bool) (int, error) {
	if dryRun {
		fmt.Printf("Would get or create Book %s\n", bookNumber)
		return 1, nil
	}

	// Check if book exists
	var bookID int
	err := db.QueryRow("SELECT id FROM books WHERE book_number = $1", bookNumber).Scan(&bookID)
	if err == nil {
		fmt.Printf("Found existing Book %s (ID: %d)\n", bookNumber, bookID)
		return bookID, nil
	}

	// Create book if it doesn't exist
	bookTitle := fmt.Sprintf("Great Nigeria Book %s", bookNumber)
	bookDescription := fmt.Sprintf("Book %s of the Great Nigeria series", bookNumber)
	accessLevel := "free"
	isPremium := false

	if bookNumber == "2" {
		accessLevel = "points"
	} else if bookNumber == "3" {
		accessLevel = "premium"
		isPremium = true
	}

	err = db.QueryRow(`
		INSERT INTO books (title, description, book_number, access_level, is_premium, published, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, true, $6, $6)
		RETURNING id
	`, bookTitle, bookDescription, bookNumber, accessLevel, isPremium, time.Now()).Scan(&bookID)

	if err != nil {
		return 0, fmt.Errorf("failed to create book: %w", err)
	}

	fmt.Printf("Created Book %s (ID: %d)\n", bookNumber, bookID)
	return bookID, nil
}

// createChapter creates a chapter in the database
func createChapter(db *sql.DB, bookID int, chapter Chapter, dryRun bool) (int, error) {
	if dryRun {
		fmt.Printf("Would create Chapter %s: %s\n", chapter.Number, chapter.Title)
		return 1, nil
	}

	// Convert chapter number to integer
	chapterNum, err := strconv.Atoi(chapter.Number)
	if err != nil {
		return 0, fmt.Errorf("invalid chapter number: %w", err)
	}

	// Create chapter
	var chapterID int
	err = db.QueryRow(`
		INSERT INTO book_chapters (book_id, title, number, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $4)
		RETURNING id
	`, bookID, chapter.Title, chapterNum, time.Now()).Scan(&chapterID)

	if err != nil {
		return 0, fmt.Errorf("failed to create chapter: %w", err)
	}

	fmt.Printf("Created Chapter %s: %s (ID: %d)\n", chapter.Number, chapter.Title, chapterID)
	return chapterID, nil
}

// createSection creates a section in the database
func createSection(db *sql.DB, bookID, chapterID int, section Section, dryRun bool) (int, error) {
	if dryRun {
		fmt.Printf("Would create Section %s: %s\n", section.Number, section.Title)
		return 1, nil
	}

	// Create section
	var sectionID int
	err := db.QueryRow(`
		INSERT INTO book_sections (book_id, chapter_id, title, number, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $5)
		RETURNING id
	`, bookID, chapterID, section.Title, section.Number, time.Now()).Scan(&sectionID)

	if err != nil {
		return 0, fmt.Errorf("failed to create section: %w", err)
	}

	// Generate placeholder content
	content := generatePlaceholderContent(section.Title, section.Number)
	
	// Update section with content
	_, err = db.Exec("UPDATE book_sections SET content = $1 WHERE id = $2", content, sectionID)
	if err != nil {
		return 0, fmt.Errorf("failed to update section content: %w", err)
	}

	fmt.Printf("Created Section %s: %s (ID: %d)\n", section.Number, section.Title, sectionID)
	return sectionID, nil
}

// createForumTopics creates forum topics for a section
func createForumTopics(db *sql.DB, sectionID int, section Section, dryRun bool) error {
	if dryRun {
		fmt.Printf("Would create forum topics for Section %s: %s\n", section.Number, section.Title)
		return nil
	}

	// Create default forum topics
	topics := []struct {
		Number string
		Title  string
	}{
		{fmt.Sprintf("%s.1", section.Number), fmt.Sprintf("Discussion: %s", section.Title)},
		{fmt.Sprintf("%s.2", section.Number), fmt.Sprintf("Questions about %s", section.Title)},
		{fmt.Sprintf("%s.3", section.Number), fmt.Sprintf("Experiences related to %s", section.Title)},
	}

	for _, topic := range topics {
		_, err := db.Exec(`
			INSERT INTO forum_topics (section_id, topic_number, title, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $4)
		`, sectionID, topic.Number, topic.Title, time.Now())

		if err != nil {
			return fmt.Errorf("failed to create forum topic: %w", err)
		}

		fmt.Printf("Created Forum Topic %s: %s for Section ID %d\n", topic.Number, topic.Title, sectionID)
	}

	// Update section to indicate it has forum topics
	_, err := db.Exec("UPDATE book_sections SET has_forum_topics = true WHERE id = $1", sectionID)
	if err != nil {
		return fmt.Errorf("failed to update section for forum topics: %w", err)
	}

	return nil
}

// generatePlaceholderContent generates placeholder content for a section
func generatePlaceholderContent(title, number string) string {
	return fmt.Sprintf(`# %s %s

## Introduction

This section explores %s, a critical component of the Great Nigeria platform. The content provides a comprehensive analysis of the key issues, challenges, and opportunities related to this topic.

## Key Points

1. **Historical Context**: Understanding the historical background of this issue in Nigeria
2. **Current Challenges**: Analyzing the present-day manifestations and impacts
3. **Systemic Connections**: Exploring how this issue connects to other aspects of Nigerian society
4. **Case Studies**: Examining real-world examples from different regions of Nigeria
5. **Potential Solutions**: Discussing approaches that have shown promise in addressing this issue

## Detailed Analysis

### Historical Context

The historical roots of this issue can be traced back to several key developments in Nigeria's past. Colonial policies established patterns that continue to influence current dynamics. Post-independence decisions further shaped the trajectory of this issue, creating both challenges and opportunities for reform.

### Current Challenges

Today, this issue manifests in various ways across different regions and communities in Nigeria. Urban and rural areas experience distinct variations of the challenge, though common patterns can be identified. The impact on vulnerable populations is particularly significant, with women, youth, and marginalized communities often bearing a disproportionate burden.

### Systemic Connections

This issue does not exist in isolation but is deeply connected to other aspects of Nigerian society. Economic factors both influence and are influenced by this challenge. Governance structures play a critical role in either perpetuating or addressing the problem. Social and cultural dynamics further complicate potential solutions, requiring nuanced approaches that respect diverse perspectives.

### Case Studies

#### Northern Nigeria Example

In northern communities, this issue has unique characteristics shaped by regional history, economic patterns, and cultural contexts. Local initiatives have demonstrated promising approaches to addressing the challenge, though scaling remains difficult.

#### Southern Nigeria Example

Southern regions face their own variations of this challenge, with different stakeholders and power dynamics at play. Urban centers in particular have developed innovative responses that offer lessons for other contexts.

### Potential Solutions

Based on both Nigerian experiences and relevant international examples, several approaches show promise for addressing this issue:

1. Community-based initiatives that leverage local knowledge and relationships
2. Policy reforms that address structural barriers and create enabling environments
3. Educational interventions that build awareness and capacity
4. Technological innovations that overcome resource constraints
5. Cross-sector collaborations that bring diverse stakeholders together

## Conclusion

This section has provided an overview of %s, highlighting both challenges and opportunities. The analysis demonstrates that while the issues are complex and deeply rooted, pathways for positive change exist. The next sections will build on this foundation to explore specific strategies for implementation.

---

*Note: This content will be expanded with specific examples, data, and detailed analysis in the final version.*
`, number, title, title, title)
}

// sortedKeys returns the sorted keys of a map
func sortedKeys(m interface{}) []string {
	var keys []string
	
	switch v := m.(type) {
	case map[string]Chapter:
		for k := range v {
			keys = append(keys, k)
		}
	case map[string]Section:
		for k := range v {
			keys = append(keys, k)
		}
	}
	
	// Simple string sort (this works for chapter numbers like "1", "2", "3")
	// For more complex sorting (like "1.1", "1.2", etc.), a custom sort would be needed
	for i := 0; i < len(keys)-1; i++ {
		for j := i + 1; j < len(keys); j++ {
			if strings.Compare(keys[i], keys[j]) > 0 {
				keys[i], keys[j] = keys[j], keys[i]
			}
		}
	}
	
	return keys
}
