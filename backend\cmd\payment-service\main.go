package main

import (
        "log"
        "os"

        "github.com/gin-gonic/gin"
        "github.com/greatnigeria/internal/payment/handlers"
        "github.com/greatnigeria/internal/payment/repository"
        "github.com/greatnigeria/internal/payment/service"
        "github.com/greatnigeria/pkg/common/config"
        "github.com/greatnigeria/pkg/common/database"
        "github.com/greatnigeria/pkg/common/logger"
        "github.com/greatnigeria/pkg/common/middleware"
        "github.com/joho/godotenv"
)

func main() {
        // Load environment variables
        if err := godotenv.Load(); err != nil {
                log.Println("Warning: .env file not found")
        }

        // Initialize logger
        logger := logger.NewLogger()
        logger.Info("Starting Great Nigeria Payment Service")

        // Load configuration
        cfg, err := config.LoadConfig()
        if err != nil {
                logger.Fatal("Failed to load configuration: " + err.Error())
        }

        // Connect to database
        db, err := database.NewDatabase(cfg)
        if err != nil {
                logger.Fatal("Failed to connect to database: " + err.Error())
        }

        // Initialize repositories
        paymentRepo := repository.NewPaymentRepository(db)
        planRepo := repository.NewPlanRepository(db)
        subscriptionRepo := repository.NewSubscriptionRepository(db)
        receiptRepo := repository.NewReceiptRepository(db)
        refundRepo := repository.NewRefundRepository(db)

        // Set storage paths for receipts
        storageBasePath := os.Getenv("STORAGE_PATH")
        if storageBasePath == "" {
                storageBasePath = "./storage"
        }
        
        publicBaseURL := os.Getenv("PUBLIC_BASE_URL")
        if publicBaseURL == "" {
                publicBaseURL = "https://greatnigeria.org"
        }

        // Initialize services
        paymentService := service.NewPaymentService(paymentRepo, logger)
        planService := service.NewPlanService(planRepo, logger)
        subscriptionService := service.NewSubscriptionService(subscriptionRepo, logger)
        receiptService := service.NewReceiptService(
                receiptRepo,
                paymentRepo,
                subscriptionRepo,
                refundRepo,
                storageBasePath,
                publicBaseURL,
        )

        // Initialize handlers
        paymentHandler := handlers.NewPaymentHandler(paymentService, receiptService, logger)
        planHandler := handlers.NewPlanHandler(planService, logger)
        subscriptionHandler := handlers.NewSubscriptionHandler(subscriptionService, logger)
        receiptHandler := handlers.NewReceiptHandler(receiptService)

        // Set up Gin router
        router := gin.Default()
        router.Use(gin.Recovery())
        router.Use(middleware.RequestLogger())

        // All routes are protected - require authentication
        router.Use(middleware.JWTAuth())

        // Define API routes
        router.POST("/payments/intent", paymentHandler.CreatePaymentIntent)
        router.POST("/payments/process", paymentHandler.ProcessPayment)
        router.GET("/payments/transactions", paymentHandler.GetTransactions)
        router.GET("/payments/transaction/:id", paymentHandler.GetTransaction)

        router.GET("/payments/plans", planHandler.ListPlans)
        router.GET("/payments/plans/:id", planHandler.GetPlan)

        router.POST("/payments/subscription", subscriptionHandler.CreateSubscription)
        router.GET("/payments/subscription", subscriptionHandler.GetSubscription)
        router.DELETE("/payments/subscription/:id", subscriptionHandler.CancelSubscription)
        
        // Receipt routes
        receiptRoutes := router.Group("/receipts")
        {
            // Public routes
            receiptRoutes.GET("/view/:number", receiptHandler.GetReceiptByNumber)
            
            // User routes
            receiptRoutes.GET("", receiptHandler.GetUserReceipts)
            receiptRoutes.GET("/:id", receiptHandler.GetReceiptByID)
            receiptRoutes.GET("/:id/download", receiptHandler.DownloadReceiptPDF)
            receiptRoutes.POST("/:id/email", receiptHandler.EmailReceiptToUser)
            receiptRoutes.POST("/:id/customization", receiptHandler.CreateReceiptCustomization)
            
            // Generate receipts
            receiptRoutes.POST("/payment/:paymentId", receiptHandler.GenerateReceiptForPayment)
            receiptRoutes.POST("/subscription/:subscriptionId", receiptHandler.GenerateReceiptForSubscription)
            receiptRoutes.POST("/refund/:refundId", receiptHandler.GenerateReceiptForRefund)
        }

        // Webhook routes - no authentication required
        router.POST("/webhook/paystack", paymentHandler.PaystackWebhook)
        router.POST("/webhook/flutterwave", paymentHandler.FlutterwaveWebhook)
        router.POST("/webhook/squad", paymentHandler.SquadWebhook)

        // Admin routes
        adminRoutes := router.Group("/admin")
        adminRoutes.Use(middleware.AdminAuth())
        {
                adminRoutes.POST("/payments/plans", planHandler.CreatePlan)
                adminRoutes.PUT("/payments/plans/:id", planHandler.UpdatePlan)
                adminRoutes.DELETE("/payments/plans/:id", planHandler.DeletePlan)
                adminRoutes.GET("/payments/all", paymentHandler.GetAllTransactions)
                
                // Receipt template administration
                adminRoutes.POST("/receipts/templates", receiptHandler.CreateReceiptTemplate)
        }

        // Start server
        port := os.Getenv("PAYMENT_SERVICE_PORT")
        if port == "" {
                port = "8005" // Default port for payment service
        }

        logger.Info("Payment Service starting on port " + port)
        if err := router.Run("0.0.0.0:" + port); err != nil {
                logger.Fatal("Failed to start Payment Service: " + err.Error())
        }
}
