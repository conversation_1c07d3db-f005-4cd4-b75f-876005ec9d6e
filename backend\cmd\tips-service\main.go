package main

import (
	"fmt"
	"os"

	"github.com/gin-gonic/gin"
	"github.com/greatnigeria/internal/tips/handlers"
	"github.com/greatnigeria/internal/tips/models"
	"github.com/greatnigeria/internal/tips/repository"
	"github.com/greatnigeria/internal/tips/service"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Initialize logger
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})
	logger.SetOutput(os.Stdout)
	logger.SetLevel(logrus.InfoLevel)

	// Connect to database
	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		os.Getenv("DB_HOST"),
		os.Getenv("DB_USER"),
		os.<PERSON>env("DB_PASSWORD"),
		os.Getenv("DB_NAME"),
		os.Getenv("DB_PORT"),
	)
	
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		logger.WithError(err).Fatal("Failed to connect to database")
	}
	
	// Auto migrate models
	err = db.AutoMigrate(&models.Tip{}, &models.TipRule{}, &models.UserTip{}, &models.TipFeedback{})
	if err != nil {
		logger.WithError(err).Fatal("Failed to migrate database")
	}
	
	// Initialize repository
	tipsRepo := repository.NewTipsRepository(db)
	
	// Initialize service
	tipsService := service.NewTipsService(tipsRepo, logger)
	
	// Initialize handler
	tipsHandler := handlers.NewTipsHandler(tipsService, logger)
	
	// Initialize router
	router := gin.Default()
	
	// Register routes
	api := router.Group("/api")
	tipsHandler.RegisterRoutes(api)
	
	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8084" // Default port for tips service
	}
	
	logger.Info("Starting tips service on port " + port)
	if err := router.Run(":" + port); err != nil {
		logger.WithError(err).Fatal("Failed to start server")
	}
}
