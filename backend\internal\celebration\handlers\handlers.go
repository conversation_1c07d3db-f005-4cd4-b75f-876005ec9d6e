package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/greatnigeria/internal/celebration/models"
	"github.com/greatnigeria/internal/celebration/service"
)

// CelebrationHandlers handles API endpoints for the Celebrate Nigeria feature
type CelebrationHandlers struct {
	service *service.CelebrationService
}

// NewCelebrationHandlers creates a new handlers instance
func NewCelebrationHandlers(service *service.CelebrationService) *CelebrationHandlers {
	return &CelebrationHandlers{
		service: service,
	}
}

// RegisterRoutes registers all the endpoints for the Celebrate Nigeria feature
func (h *CelebrationHandlers) RegisterRoutes(router *gin.RouterGroup) {
	celebrateGroup := router.Group("/celebrate")
	{
		// Category endpoints
		celebrateGroup.GET("/categories", h.ListCategories)
		celebrateGroup.GET("/categories/:slug", h.GetCategory)
		celebrateGroup.POST("/categories", h.CreateCategory)
		celebrateGroup.PUT("/categories/:id", h.UpdateCategory)
		celebrateGroup.DELETE("/categories/:id", h.DeleteCategory)

		// Entry endpoints
		celebrateGroup.GET("/entries", h.ListEntries)
		celebrateGroup.GET("/entries/featured", h.ListFeaturedEntries)
		celebrateGroup.GET("/entries/category/:slug", h.ListCategorizedEntries)
		celebrateGroup.GET("/entries/search", h.SearchEntries)

		// Person endpoints
		celebrateGroup.GET("/people/:slug", h.GetPerson)
		celebrateGroup.POST("/people", h.CreatePerson)

		// Place endpoints
		celebrateGroup.GET("/places/:slug", h.GetPlace)
		celebrateGroup.POST("/places", h.CreatePlace)

		// Event endpoints
		celebrateGroup.GET("/events/:slug", h.GetEvent)
		celebrateGroup.POST("/events", h.CreateEvent)

		// Comment endpoints
		celebrateGroup.GET("/entries/:id/comments", h.ListEntryComments)
		celebrateGroup.POST("/entries/:id/comments", h.CreateEntryComment)

		// Media endpoints
		celebrateGroup.POST("/entries/:id/media", h.AddEntryMedia)

		// Fact endpoints
		celebrateGroup.POST("/entries/:id/facts", h.AddEntryFact)

		// Submission endpoints
		celebrateGroup.GET("/submissions", h.ListPendingSubmissions)
		celebrateGroup.POST("/submissions", h.CreateEntrySubmission)
		celebrateGroup.PUT("/submissions/:id/review", h.ReviewSubmission)
		celebrateGroup.POST("/submissions/:id/vote", h.VoteForSubmission)
		celebrateGroup.GET("/submit", h.RenderSubmissionForm)              // New route for submission form
		celebrateGroup.GET("/admin/submissions", h.RenderAdminSubmissions) // New route for admin review page

		// Voting endpoints
		celebrateGroup.POST("/entries/:id/vote", h.VoteForEntry)
		celebrateGroup.DELETE("/entries/:id/vote", h.DeleteVoteForEntry)
		celebrateGroup.GET("/entries/:id/votes", h.GetEntryVotes)
	}
}

// ListCategories handles GET /celebrate/categories
func (h *CelebrationHandlers) ListCategories(c *gin.Context) {
	parentSlug := c.Query("parent")

	categories, err := h.service.ListCategories(c.Request.Context(), parentSlug)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, categories)
}

// GetCategory handles GET /celebrate/categories/:slug
func (h *CelebrationHandlers) GetCategory(c *gin.Context) {
	slug := c.Param("slug")

	category, err := h.service.GetCategoryBySlug(c.Request.Context(), slug)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if category == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	c.JSON(http.StatusOK, category)
}

// categoryInput is the request payload for creating or updating a category
type categoryInput struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Slug        string `json:"slug"`
	ParentID    *int64 `json:"parent_id"`
	IconSVG     string `json:"icon_svg"`
	Visible     bool   `json:"visible"`
	SortOrder   int    `json:"sort_order"`
}

// CreateCategory handles POST /celebrate/categories
func (h *CelebrationHandlers) CreateCategory(c *gin.Context) {
	var input categoryInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category, err := h.service.CreateCategory(
		c.Request.Context(),
		input.Name,
		input.Description,
		input.Slug,
		input.ParentID,
		input.IconSVG,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// UpdateCategory handles PUT /celebrate/categories/:id
func (h *CelebrationHandlers) UpdateCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var input categoryInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category, err := h.service.UpdateCategory(
		c.Request.Context(),
		id,
		input.Name,
		input.Description,
		input.Slug,
		input.ParentID,
		input.IconSVG,
		input.Visible,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, category)
}

// DeleteCategory handles DELETE /celebrate/categories/:id
func (h *CelebrationHandlers) DeleteCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	if err := h.service.DeleteCategory(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Category deleted successfully"})
}

// ListEntries handles GET /celebrate/entries
func (h *CelebrationHandlers) ListEntries(c *gin.Context) {
	entryType := c.Query("type") // 'person', 'place', 'event'

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 20
	}

	// Cap page size to prevent overload
	if pageSize > 100 {
		pageSize = 100
	}

	entries, total, err := h.service.ListEntries(c.Request.Context(), entryType, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":        entries,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
	})
}

// ListFeaturedEntries handles GET /celebrate/entries/featured
func (h *CelebrationHandlers) ListFeaturedEntries(c *gin.Context) {
	entryType := c.Query("type") // 'person', 'place', 'event'

	limitStr := c.DefaultQuery("limit", "6")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		limit = 6
	}

	// Cap limit to prevent overload
	if limit > 20 {
		limit = 20
	}

	entries, err := h.service.ListFeaturedEntries(c.Request.Context(), entryType, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, entries)
}

// ListCategorizedEntries handles GET /celebrate/entries/category/:slug
func (h *CelebrationHandlers) ListCategorizedEntries(c *gin.Context) {
	categorySlug := c.Param("slug")
	entryType := c.Query("type") // 'person', 'place', 'event'

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 20
	}

	// Cap page size to prevent overload
	if pageSize > 100 {
		pageSize = 100
	}

	entries, total, err := h.service.ListCategorizedEntries(c.Request.Context(), categorySlug, entryType, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":        entries,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
	})
}

// SearchEntries handles GET /celebrate/entries/search
func (h *CelebrationHandlers) SearchEntries(c *gin.Context) {
	// Get search parameters from query string
	query := c.Query("q")
	entryType := c.Query("type") // 'person', 'place', 'event'
	categorySlug := c.Query("category")
	sortBy := c.Query("sort") // 'relevance', 'newest', 'oldest', 'az', 'za', 'popular'

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 20
	}

	// Cap page size to prevent overload
	if pageSize > 100 {
		pageSize = 100
	}

	// Prepare search parameters
	params := service.SearchEntriesParams{
		Query:        query,
		EntryType:    entryType,
		CategorySlug: categorySlug,
		SortBy:       sortBy,
		Page:         page,
		PageSize:     pageSize,
	}

	// Perform search
	entries, total, totalPages, err := h.service.SearchEntries(c.Request.Context(), params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// For API requests, return JSON
	if c.GetHeader("Accept") == "application/json" || c.Query("format") == "json" {
		c.JSON(http.StatusOK, gin.H{
			"data":        entries,
			"total":       total,
			"page":        page,
			"page_size":   pageSize,
			"total_pages": totalPages,
			"query":       query,
			"type":        entryType,
			"category":    categorySlug,
			"sort":        sortBy,
		})
		return
	}

	// For HTML requests, render the search page
	// Get all categories for the filter dropdown
	categories, err := h.service.ListCategories(c.Request.Context(), "")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Render the search page
	c.HTML(http.StatusOK, "celebrate-search.html", gin.H{
		"Title":        "Search - Celebrate Nigeria",
		"Query":        query,
		"Type":         entryType,
		"CategorySlug": categorySlug,
		"Sort":         sortBy,
		"Results":      entries,
		"Total":        total,
		"Page":         page,
		"PageSize":     pageSize,
		"TotalPages":   totalPages,
		"Categories":   categories,
		// Add helper functions for the template
		"add": func(a, b int) int {
			return a + b
		},
		"sub": func(a, b int) int {
			return a - b
		},
	})
}

// GetPerson handles GET /celebrate/people/:slug
func (h *CelebrationHandlers) GetPerson(c *gin.Context) {
	slug := c.Param("slug")

	person, err := h.service.GetPersonBySlug(c.Request.Context(), slug)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if person == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Person not found"})
		return
	}

	c.JSON(http.StatusOK, person)
}

// personInput is the request payload for creating a person
type personInput struct {
	Title         string  `json:"title" binding:"required"`
	Slug          string  `json:"slug"`
	ShortDesc     string  `json:"short_desc" binding:"required"`
	FullDesc      string  `json:"full_desc"`
	PrimaryImage  string  `json:"primary_image_url"`
	Location      string  `json:"location"`
	CategoryIDs   []int64 `json:"category_ids" binding:"required"`
	BirthDate     string  `json:"birth_date"`
	DeathDate     string  `json:"death_date"`
	Profession    string  `json:"profession"`
	Achievements  string  `json:"achievements"`
	Contributions string  `json:"contributions"`
	Education     string  `json:"education"`
	RelatedLinks  string  `json:"related_links"`
}

// CreatePerson handles POST /celebrate/people
func (h *CelebrationHandlers) CreatePerson(c *gin.Context) {
	var input personInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse user ID from token or session
	// This is a placeholder; in a real implementation you would get this from authentication middleware
	var userID int64 = 0 // 0 means admin user; would be a real user ID for non-admin submissions

	// Convert string fields to pointers when needed
	var fullDescPtr, primaryImagePtr, locationPtr *string
	var professionPtr, achievementsPtr, contributionsPtr, educationPtr, relatedLinksPtr *string

	if input.FullDesc != "" {
		fullDescPtr = &input.FullDesc
	}
	if input.PrimaryImage != "" {
		primaryImagePtr = &input.PrimaryImage
	}
	if input.Location != "" {
		locationPtr = &input.Location
	}
	if input.Profession != "" {
		professionPtr = &input.Profession
	}
	if input.Achievements != "" {
		achievementsPtr = &input.Achievements
	}
	if input.Contributions != "" {
		contributionsPtr = &input.Contributions
	}
	if input.Education != "" {
		educationPtr = &input.Education
	}
	if input.RelatedLinks != "" {
		relatedLinksPtr = &input.RelatedLinks
	}

	person := &models.PersonEntry{
		CelebrationEntry: models.CelebrationEntry{
			Title:           input.Title,
			Slug:            input.Slug,
			ShortDesc:       input.ShortDesc,
			FullDesc:        fullDescPtr,
			PrimaryImageURL: primaryImagePtr,
			Location:        locationPtr,
			Status:          "approved", // Auto-approve for admin; would be "pending" for user submissions
		},
		Profession:    professionPtr,
		Achievements:  achievementsPtr,
		Contributions: contributionsPtr,
		Education:     educationPtr,
		RelatedLinks:  relatedLinksPtr,
	}

	// Handle birth and death dates
	// In a real implementation, you would properly parse and validate these dates

	if err := h.service.CreatePersonEntry(c.Request.Context(), person, input.CategoryIDs, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, person)
}

// GetPlace handles GET /celebrate/places/:slug
func (h *CelebrationHandlers) GetPlace(c *gin.Context) {
	slug := c.Param("slug")

	place, err := h.service.GetPlaceBySlug(c.Request.Context(), slug)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if place == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Place not found"})
		return
	}

	c.JSON(http.StatusOK, place)
}

// placeInput is the request payload for creating a place
type placeInput struct {
	Title         string  `json:"title" binding:"required"`
	Slug          string  `json:"slug"`
	ShortDesc     string  `json:"short_desc" binding:"required"`
	FullDesc      string  `json:"full_desc"`
	PrimaryImage  string  `json:"primary_image_url"`
	Location      string  `json:"location"`
	CategoryIDs   []int64 `json:"category_ids" binding:"required"`
	PlaceType     string  `json:"place_type" binding:"required"`
	Latitude      float64 `json:"latitude"`
	Longitude     float64 `json:"longitude"`
	Address       string  `json:"address"`
	VisitingHours string  `json:"visiting_hours"`
	VisitingFees  string  `json:"visiting_fees"`
	Accessibility string  `json:"accessibility"`
	History       string  `json:"history"`
}

// CreatePlace handles POST /celebrate/places
func (h *CelebrationHandlers) CreatePlace(c *gin.Context) {
	var input placeInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse user ID from token or session
	// This is a placeholder; in a real implementation you would get this from authentication middleware
	var userID int64 = 0 // 0 means admin user; would be a real user ID for non-admin submissions

	// Convert string fields to pointers when needed
	var fullDescPtr, primaryImagePtr, locationPtr *string
	var placeTypePtr, addressPtr, visitingHoursPtr, visitingFeesPtr, accessibilityPtr, historyPtr *string

	if input.FullDesc != "" {
		fullDescPtr = &input.FullDesc
	}
	if input.PrimaryImage != "" {
		primaryImagePtr = &input.PrimaryImage
	}
	if input.Location != "" {
		locationPtr = &input.Location
	}
	if input.PlaceType != "" {
		placeTypePtr = &input.PlaceType
	}
	if input.Address != "" {
		addressPtr = &input.Address
	}
	if input.VisitingHours != "" {
		visitingHoursPtr = &input.VisitingHours
	}
	if input.VisitingFees != "" {
		visitingFeesPtr = &input.VisitingFees
	}
	if input.Accessibility != "" {
		accessibilityPtr = &input.Accessibility
	}
	if input.History != "" {
		historyPtr = &input.History
	}

	place := &models.PlaceEntry{
		CelebrationEntry: models.CelebrationEntry{
			Title:           input.Title,
			Slug:            input.Slug,
			ShortDesc:       input.ShortDesc,
			FullDesc:        fullDescPtr,
			PrimaryImageURL: primaryImagePtr,
			Location:        locationPtr,
			Status:          "approved", // Auto-approve for admin; would be "pending" for user submissions
		},
		PlaceType:     placeTypePtr,
		Latitude:      input.Latitude,
		Longitude:     input.Longitude,
		Address:       addressPtr,
		VisitingHours: visitingHoursPtr,
		VisitingFees:  visitingFeesPtr,
		Accessibility: accessibilityPtr,
		History:       historyPtr,
	}

	if err := h.service.CreatePlaceEntry(c.Request.Context(), place, input.CategoryIDs, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, place)
}

// GetEvent handles GET /celebrate/events/:slug
func (h *CelebrationHandlers) GetEvent(c *gin.Context) {
	slug := c.Param("slug")

	event, err := h.service.GetEventBySlug(c.Request.Context(), slug)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if event == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Event not found"})
		return
	}

	c.JSON(http.StatusOK, event)
}

// eventInput is the request payload for creating an event
type eventInput struct {
	Title             string  `json:"title" binding:"required"`
	Slug              string  `json:"slug"`
	ShortDesc         string  `json:"short_desc" binding:"required"`
	FullDesc          string  `json:"full_desc"`
	PrimaryImage      string  `json:"primary_image_url"`
	Location          string  `json:"location"`
	CategoryIDs       []int64 `json:"category_ids" binding:"required"`
	EventType         string  `json:"event_type" binding:"required"`
	StartDate         string  `json:"start_date"`
	EndDate           string  `json:"end_date"`
	IsRecurring       bool    `json:"is_recurring"`
	RecurrencePattern string  `json:"recurrence_pattern"`
	Organizer         string  `json:"organizer"`
	ContactInfo       string  `json:"contact_info"`
	EventHistory      string  `json:"event_history"`
}

// CreateEvent handles POST /celebrate/events
func (h *CelebrationHandlers) CreateEvent(c *gin.Context) {
	var input eventInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse user ID from token or session
	// This is a placeholder; in a real implementation you would get this from authentication middleware
	var userID int64 = 0 // 0 means admin user; would be a real user ID for non-admin submissions

	// Convert string fields to pointers when needed
	var fullDescPtr, primaryImagePtr, locationPtr *string
	var eventTypePtr, recurrencePatternPtr, organizerPtr, contactInfoPtr, eventHistoryPtr *string

	if input.FullDesc != "" {
		fullDescPtr = &input.FullDesc
	}
	if input.PrimaryImage != "" {
		primaryImagePtr = &input.PrimaryImage
	}
	if input.Location != "" {
		locationPtr = &input.Location
	}
	if input.EventType != "" {
		eventTypePtr = &input.EventType
	}
	if input.RecurrencePattern != "" {
		recurrencePatternPtr = &input.RecurrencePattern
	}
	if input.Organizer != "" {
		organizerPtr = &input.Organizer
	}
	if input.ContactInfo != "" {
		contactInfoPtr = &input.ContactInfo
	}
	if input.EventHistory != "" {
		eventHistoryPtr = &input.EventHistory
	}

	event := &models.EventEntry{
		CelebrationEntry: models.CelebrationEntry{
			Title:           input.Title,
			Slug:            input.Slug,
			ShortDesc:       input.ShortDesc,
			FullDesc:        fullDescPtr,
			PrimaryImageURL: primaryImagePtr,
			Location:        locationPtr,
			Status:          "approved", // Auto-approve for admin; would be "pending" for user submissions
		},
		EventType:         eventTypePtr,
		IsRecurring:       input.IsRecurring,
		RecurrencePattern: recurrencePatternPtr,
		Organizer:         organizerPtr,
		ContactInfo:       contactInfoPtr,
		EventHistory:      eventHistoryPtr,
	}

	// Handle start and end dates
	// In a real implementation, you would properly parse and validate these dates

	if err := h.service.CreateEventEntry(c.Request.Context(), event, input.CategoryIDs, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, event)
}

// ListEntryComments handles GET /celebrate/entries/:id/comments
func (h *CelebrationHandlers) ListEntryComments(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 20
	}

	// Cap page size to prevent overload
	if pageSize > 100 {
		pageSize = 100
	}

	comments, total, err := h.service.ListEntryComments(c.Request.Context(), id, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":        comments,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
	})
}

// commentInput is the request payload for creating a comment
type commentInput struct {
	Content  string `json:"content" binding:"required"`
	ParentID *int64 `json:"parent_id"`
}

// CreateEntryComment handles POST /celebrate/entries/:id/comments
func (h *CelebrationHandlers) CreateEntryComment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	var input commentInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse user ID from token or session
	// This is a placeholder; in a real implementation you would get this from authentication middleware
	var userID int64 = 1 // Would be a real user ID in a real implementation

	comment, err := h.service.CreateEntryComment(c.Request.Context(), id, userID, input.Content, input.ParentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, comment)
}

// mediaInput is the request payload for adding media
type mediaInput struct {
	MediaType string `json:"media_type" binding:"required"`
	URL       string `json:"url" binding:"required"`
	Caption   string `json:"caption"`
	SortOrder int    `json:"sort_order"`
}

// AddEntryMedia handles POST /celebrate/entries/:id/media
func (h *CelebrationHandlers) AddEntryMedia(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	var input mediaInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	media, err := h.service.CreateEntryMedia(c.Request.Context(), id, input.MediaType, input.URL, input.Caption)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, media)
}

// factInput is the request payload for adding a fact
type factInput struct {
	Label     string `json:"label" binding:"required"`
	Value     string `json:"value" binding:"required"`
	SortOrder int    `json:"sort_order"`
}

// AddEntryFact handles POST /celebrate/entries/:id/facts
func (h *CelebrationHandlers) AddEntryFact(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	var input factInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	fact, err := h.service.CreateEntryFact(c.Request.Context(), id, input.Label, input.Value, input.SortOrder)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, fact)
}

// ListPendingSubmissions handles GET /celebrate/submissions
func (h *CelebrationHandlers) ListPendingSubmissions(c *gin.Context) {
	entryType := c.Query("type") // 'person', 'place', 'event'

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 20
	}

	// Cap page size to prevent overload
	if pageSize > 100 {
		pageSize = 100
	}

	submissions, total, err := h.service.ListPendingSubmissions(c.Request.Context(), entryType, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":        submissions,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
	})
}

// submissionInput is the request payload for creating a submission
type submissionInput struct {
	EntryType     string `json:"entry_type" binding:"required"`
	TargetEntryID *int64 `json:"target_entry_id"`
	Title         string `json:"title" binding:"required"`
	Content       string `json:"content" binding:"required"`
}

// CreateEntrySubmission handles POST /celebrate/submissions
func (h *CelebrationHandlers) CreateEntrySubmission(c *gin.Context) {
	var input submissionInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse user ID from token or session
	// This is a placeholder; in a real implementation you would get this from authentication middleware
	var userID int64 = 1 // Would be a real user ID in a real implementation

	submission, err := h.service.CreateEntrySubmission(
		c.Request.Context(),
		userID,
		input.EntryType,
		input.Title,
		input.Content,
		input.TargetEntryID,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, submission)
}

// reviewInput is the request payload for reviewing a submission
type reviewInput struct {
	Status     string `json:"status" binding:"required"`
	AdminNotes string `json:"admin_notes"`
}

// ReviewSubmission handles PUT /celebrate/submissions/:id/review
func (h *CelebrationHandlers) ReviewSubmission(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid submission ID"})
		return
	}

	var input reviewInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse admin ID from token or session
	// This is a placeholder; in a real implementation you would get this from authentication middleware
	var adminID int64 = 1 // Would be a real admin ID in a real implementation

	if err := h.service.ReviewSubmission(c.Request.Context(), id, adminID, input.Status, input.AdminNotes); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Submission reviewed successfully"})
}

// voteInput is the request payload for voting on a submission
type voteInput struct {
	VoteType string `json:"vote_type" binding:"required"`
}

// VoteForSubmission handles POST /celebrate/submissions/:id/vote
func (h *CelebrationHandlers) VoteForSubmission(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid submission ID"})
		return
	}

	var input voteInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse user ID from token or session
	// This is a placeholder; in a real implementation you would get this from authentication middleware
	var userID int64 = 1 // Would be a real user ID in a real implementation

	if err := h.service.VoteForSubmission(c.Request.Context(), id, userID, input.VoteType); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Vote recorded successfully"})
}

// VoteForEntry handles POST /celebrate/entries/:id/vote
func (h *CelebrationHandlers) VoteForEntry(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	var input voteInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse user ID from token or session
	// This is a placeholder; in a real implementation you would get this from authentication middleware
	var userID int64 = 1 // Would be a real user ID in a real implementation

	if err := h.service.VoteForEntry(c.Request.Context(), id, userID, input.VoteType); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Vote recorded successfully"})
}

// DeleteVoteForEntry handles DELETE /celebrate/entries/:id/vote
func (h *CelebrationHandlers) DeleteVoteForEntry(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	// Parse user ID from token or session
	// This is a placeholder; in a real implementation you would get this from authentication middleware
	var userID int64 = 1 // Would be a real user ID in a real implementation

	if err := h.service.DeleteVoteForEntry(c.Request.Context(), id, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Vote removed successfully"})
}

// GetEntryVotes handles GET /celebrate/entries/:id/votes
func (h *CelebrationHandlers) GetEntryVotes(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	upvotes, downvotes, err := h.service.GetEntryVoteCounts(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Get user's vote if authenticated
	var userVote string
	// This is a placeholder; in a real implementation you would get the user ID from authentication middleware
	var userID int64 = 1 // Would be a real user ID in a real implementation

	userVote, err = h.service.GetUserVoteForEntry(c.Request.Context(), id, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"upvotes":   upvotes,
		"downvotes": downvotes,
		"total":     upvotes - downvotes,
		"user_vote": userVote,
	})
}

// RenderSubmissionForm handles GET /celebrate/submit
func (h *CelebrationHandlers) RenderSubmissionForm(c *gin.Context) {
	// Check if user is authenticated
	// This is a placeholder; in a real implementation you would check if the user is authenticated
	isAuthenticated := true

	if !isAuthenticated {
		// Redirect to login page
		c.Redirect(http.StatusFound, "/auth/login?redirect=/celebrate/submit")
		return
	}

	// Render the submission form template
	c.HTML(http.StatusOK, "celebrate-submission.html", gin.H{
		"title": "Submit to Celebrate Nigeria",
	})
}

// RenderAdminSubmissions handles GET /celebrate/admin/submissions
func (h *CelebrationHandlers) RenderAdminSubmissions(c *gin.Context) {
	// Check if user is admin
	// This is a placeholder; in a real implementation you would check if the user is an admin
	isAdmin := true

	if !isAdmin {
		// Redirect to home page or show access denied
		c.Redirect(http.StatusFound, "/")
		return
	}

	// Render the admin submissions template
	c.HTML(http.StatusOK, "celebrate-admin-submissions.html", gin.H{
		"title": "Review Submissions - Celebrate Nigeria Admin",
	})
}
