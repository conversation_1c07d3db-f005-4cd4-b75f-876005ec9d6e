package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/greatnigeria/internal/celebration/service"
)

// ModerationHandlers handles moderation-related requests
type ModerationHandlers struct {
	service           service.CelebrationService
	moderationService service.ModerationService
}

// NewModerationHandlers creates a new moderation handlers instance
func NewModerationHandlers(service service.CelebrationService, moderationService service.ModerationService) *ModerationHandlers {
	return &ModerationHandlers{
		service:           service,
		moderationService: moderationService,
	}
}

// RegisterRoutes registers moderation routes
func (h *ModerationHandlers) RegisterRoutes(router *gin.RouterGroup) {
	moderation := router.Group("/celebrate/moderation")
	{
		// Flag endpoints
		moderation.POST("/flags", h.FlagEntry)
		moderation.GET("/flags", h.GetFlaggedEntries)
		moderation.GET("/flags/:id", h.GetFlagByID)
		moderation.PUT("/flags/:id/review", h.ReviewFlag)

		// Moderation queue endpoints
		moderation.GET("/queue", h.GetModerationQueue)
		moderation.GET("/queue/:id", h.GetModerationQueueItem)
		moderation.PUT("/queue/:id/process", h.ProcessModerationQueueItem)

		// Admin dashboard
		moderation.GET("/dashboard", h.RenderModerationDashboard)
	}
}

// FlagEntryRequest represents a request to flag an entry
type FlagEntryRequest struct {
	EntryID int64  `json:"entryId" binding:"required"`
	Reason  string `json:"reason" binding:"required"`
}

// FlagEntry handles POST /celebrate/moderation/flags
func (h *ModerationHandlers) FlagEntry(c *gin.Context) {
	var req FlagEntryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	// In a real implementation, this would come from authentication middleware
	userID := int64(1) // Placeholder

	// Flag the entry
	err := h.moderationService.FlagEntry(c.Request.Context(), req.EntryID, userID, req.Reason)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Entry flagged successfully"})
}

// GetFlaggedEntries handles GET /celebrate/moderation/flags
func (h *ModerationHandlers) GetFlaggedEntries(c *gin.Context) {
	// Parse query parameters
	status := c.Query("status")
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Check if user has moderator privileges
	// In a real implementation, this would come from authentication middleware
	userID := int64(1) // Placeholder

	hasMod, err := h.moderationService.HasModeratorPrivileges(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if !hasMod {
		c.JSON(http.StatusForbidden, gin.H{"error": "You do not have permission to access this resource"})
		return
	}

	// Get flagged entries
	flags, total, err := h.moderationService.GetFlaggedEntries(c.Request.Context(), status, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"flags": flags,
		"pagination": gin.H{
			"page":       page,
			"pageSize":   pageSize,
			"total":      total,
			"totalPages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetFlagByID handles GET /celebrate/moderation/flags/:id
func (h *ModerationHandlers) GetFlagByID(c *gin.Context) {
	// Parse flag ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid flag ID"})
		return
	}

	// Check if user has moderator privileges
	// In a real implementation, this would come from authentication middleware
	userID := int64(1) // Placeholder

	hasMod, err := h.moderationService.HasModeratorPrivileges(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if !hasMod {
		c.JSON(http.StatusForbidden, gin.H{"error": "You do not have permission to access this resource"})
		return
	}

	// Get flag
	flag, err := h.moderationService.GetFlagByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if flag == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Flag not found"})
		return
	}

	c.JSON(http.StatusOK, flag)
}

// ReviewFlagRequest represents a request to review a flag
type ReviewFlagRequest struct {
	Decision string `json:"decision" binding:"required,oneof=approved rejected hidden"`
	Notes    string `json:"notes"`
}

// ReviewFlag handles PUT /celebrate/moderation/flags/:id/review
func (h *ModerationHandlers) ReviewFlag(c *gin.Context) {
	// Parse flag ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid flag ID"})
		return
	}

	var req ReviewFlagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	// In a real implementation, this would come from authentication middleware
	userID := int64(1) // Placeholder

	// Review the flag
	err = h.moderationService.ReviewFlaggedEntry(c.Request.Context(), id, userID, req.Decision, req.Notes)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Flag reviewed successfully"})
}

// GetModerationQueue handles GET /celebrate/moderation/queue
func (h *ModerationHandlers) GetModerationQueue(c *gin.Context) {
	// Parse query parameters
	status := c.Query("status")
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Check if user has moderator privileges
	// In a real implementation, this would come from authentication middleware
	userID := int64(1) // Placeholder

	hasMod, err := h.moderationService.HasModeratorPrivileges(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if !hasMod {
		c.JSON(http.StatusForbidden, gin.H{"error": "You do not have permission to access this resource"})
		return
	}

	// Get moderation queue items
	items, total, err := h.moderationService.GetModerationQueueItems(c.Request.Context(), status, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"items": items,
		"pagination": gin.H{
			"page":       page,
			"pageSize":   pageSize,
			"total":      total,
			"totalPages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetModerationQueueItem handles GET /celebrate/moderation/queue/:id
func (h *ModerationHandlers) GetModerationQueueItem(c *gin.Context) {
	// Parse item ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid queue item ID"})
		return
	}

	// Check if user has moderator privileges
	// In a real implementation, this would come from authentication middleware
	userID := int64(1) // Placeholder

	hasMod, err := h.moderationService.HasModeratorPrivileges(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if !hasMod {
		c.JSON(http.StatusForbidden, gin.H{"error": "You do not have permission to access this resource"})
		return
	}

	// Get queue item
	item, err := h.moderationService.GetModerationQueueItemByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if item == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Queue item not found"})
		return
	}

	c.JSON(http.StatusOK, item)
}

// ProcessModerationQueueItemRequest represents a request to process a moderation queue item
type ProcessModerationQueueItemRequest struct {
	Decision string `json:"decision" binding:"required,oneof=approved rejected hidden"`
	Notes    string `json:"notes"`
}

// ProcessModerationQueueItem handles PUT /celebrate/moderation/queue/:id/process
func (h *ModerationHandlers) ProcessModerationQueueItem(c *gin.Context) {
	// Parse item ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid queue item ID"})
		return
	}

	var req ProcessModerationQueueItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	// In a real implementation, this would come from authentication middleware
	userID := int64(1) // Placeholder

	// Process the queue item
	err = h.moderationService.ProcessModerationQueueItem(c.Request.Context(), id, userID, req.Decision, req.Notes)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Queue item processed successfully"})
}

// RenderModerationDashboard handles GET /celebrate/moderation/dashboard
func (h *ModerationHandlers) RenderModerationDashboard(c *gin.Context) {
	// Check if user has moderator privileges
	// In a real implementation, this would come from authentication middleware
	userID := int64(1) // Placeholder

	hasMod, err := h.moderationService.HasModeratorPrivileges(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if !hasMod {
		c.Redirect(http.StatusFound, "/")
		return
	}

	// Render the moderation dashboard template
	c.HTML(http.StatusOK, "celebrate-moderation-dashboard.html", gin.H{
		"title": "Moderation Dashboard - Celebrate Nigeria",
	})
}
