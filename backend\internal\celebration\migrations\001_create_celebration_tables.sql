-- Categories Table
CREATE TABLE IF NOT EXISTS categories (
    id BIGSERIAL PRIMARY KEY,
    slug VARCHAR(255) NOT NULL,
    name VARCHA<PERSON>(255) NOT NULL,
    description TEXT,
    parent_id BIGINT REFERENCES categories(id),
    image_url VARCHAR(255),
    icon_svg TEXT,
    sort_order INT DEFAULT 0,
    visible BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT categories_slug_unique UNIQUE (slug)
);

-- Tags Table
CREATE TABLE IF NOT EXISTS tags (
    id BIGSERIAL PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT tags_name_unique UNIQUE (name),
    CONSTRAINT tags_slug_unique UNIQUE (slug)
);

-- Celebration Entries Table
CREATE TABLE IF NOT EXISTS celebration_entries (
    id BIGSERIAL PRIMARY KEY,
    entry_type VARCHAR(50) NOT NULL, -- 'person', 'place', 'event'
    slug VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    short_desc VARCHAR(500) NOT NULL,
    full_desc TEXT,
    primary_image_url VARCHAR(255),
    location VARCHAR(255),
    featured_rank INT DEFAULT 0,
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    status VARCHAR(50) DEFAULT 'pending',
    submitted_by BIGINT,
    approved_by BIGINT,
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT celebration_entries_slug_unique UNIQUE (slug)
);

-- Person Entries Table (extends Celebration Entries)
CREATE TABLE IF NOT EXISTS person_entries (
    id BIGSERIAL PRIMARY KEY,
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    birth_date TIMESTAMP WITH TIME ZONE,
    death_date TIMESTAMP WITH TIME ZONE,
    profession VARCHAR(255),
    achievements TEXT,
    contributions TEXT,
    education TEXT,
    related_links TEXT
);

-- Place Entries Table (extends Celebration Entries)
CREATE TABLE IF NOT EXISTS place_entries (
    id BIGSERIAL PRIMARY KEY,
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    place_type VARCHAR(100),
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    address TEXT,
    visiting_hours VARCHAR(255),
    visiting_fees VARCHAR(255),
    accessibility TEXT,
    history TEXT
);

-- Event Entries Table (extends Celebration Entries)
CREATE TABLE IF NOT EXISTS event_entries (
    id BIGSERIAL PRIMARY KEY,
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    event_type VARCHAR(100),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern VARCHAR(100),
    organizer VARCHAR(255),
    contact_info TEXT,
    event_history TEXT
);

-- Entry Categories Junction Table
CREATE TABLE IF NOT EXISTS entry_categories (
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    category_id BIGINT NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    PRIMARY KEY (celebration_entry_id, category_id)
);

-- Entry Tags Junction Table
CREATE TABLE IF NOT EXISTS entry_tags (
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    tag_id BIGINT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (celebration_entry_id, tag_id)
);

-- Entry Media Table
CREATE TABLE IF NOT EXISTS entry_media (
    id BIGSERIAL PRIMARY KEY,
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    media_type VARCHAR(50) NOT NULL, -- 'image', 'video', 'audio', 'document'
    url VARCHAR(500) NOT NULL,
    caption TEXT,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Entry Facts Table
CREATE TABLE IF NOT EXISTS entry_facts (
    id BIGSERIAL PRIMARY KEY,
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    label VARCHAR(255) NOT NULL,
    value TEXT NOT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Entry Comments Table
CREATE TABLE IF NOT EXISTS entry_comments (
    id BIGSERIAL PRIMARY KEY,
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'approved',
    likes INT DEFAULT 0,
    parent_id BIGINT REFERENCES entry_comments(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Entry Votes Table
CREATE TABLE IF NOT EXISTS entry_votes (
    id BIGSERIAL PRIMARY KEY,
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    vote_type VARCHAR(50) NOT NULL, -- 'support', 'oppose'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT entry_votes_user_entry_unique UNIQUE (celebration_entry_id, user_id)
);

-- Entry Revisions Table
CREATE TABLE IF NOT EXISTS entry_revisions (
    id BIGSERIAL PRIMARY KEY,
    celebration_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    revision_data TEXT NOT NULL, -- JSON of the entry at this revision
    change_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Entry Relationships Table
CREATE TABLE IF NOT EXISTS entry_relationships (
    id BIGSERIAL PRIMARY KEY,
    source_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    target_entry_id BIGINT NOT NULL REFERENCES celebration_entries(id) ON DELETE CASCADE,
    relation_type VARCHAR(100) NOT NULL, -- 'related', 'parent-child', 'influenced', etc.
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT entry_relationships_source_target_unique UNIQUE (source_entry_id, target_entry_id, relation_type)
);

-- Entry Submissions Table
CREATE TABLE IF NOT EXISTS entry_submissions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    entry_type VARCHAR(50) NOT NULL, -- 'person', 'place', 'event'
    target_entry_id BIGINT REFERENCES celebration_entries(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL, -- JSON of the submission data
    status VARCHAR(50) DEFAULT 'pending',
    admin_notes TEXT,
    reviewed_by BIGINT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    vote_count INT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_celebration_entries_entry_type ON celebration_entries(entry_type);
CREATE INDEX IF NOT EXISTS idx_celebration_entries_status ON celebration_entries(status);
CREATE INDEX IF NOT EXISTS idx_celebration_entries_featured_rank ON celebration_entries(featured_rank);
CREATE INDEX IF NOT EXISTS idx_entry_comments_celebration_entry_id ON entry_comments(celebration_entry_id);
CREATE INDEX IF NOT EXISTS idx_entry_comments_user_id ON entry_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_entry_votes_celebration_entry_id ON entry_votes(celebration_entry_id);
CREATE INDEX IF NOT EXISTS idx_entry_votes_user_id ON entry_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_entry_submissions_status ON entry_submissions(status);
CREATE INDEX IF NOT EXISTS idx_entry_submissions_user_id ON entry_submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_entry_media_celebration_entry_id ON entry_media(celebration_entry_id);
CREATE INDEX IF NOT EXISTS idx_entry_facts_celebration_entry_id ON entry_facts(celebration_entry_id);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);

-- Full-text search indexes for searching entries
ALTER TABLE celebration_entries ADD COLUMN IF NOT EXISTS search_vector tsvector;
CREATE INDEX IF NOT EXISTS idx_celebration_entries_search_vector ON celebration_entries USING gin(search_vector);

-- Create or replace trigger function to update search vector
CREATE OR REPLACE FUNCTION celebration_entries_search_trigger() RETURNS trigger AS $$
BEGIN
  NEW.search_vector :=
    setweight(to_tsvector('english', coalesce(NEW.title, '')), 'A') ||
    setweight(to_tsvector('english', coalesce(NEW.short_desc, '')), 'B') ||
    setweight(to_tsvector('english', coalesce(NEW.full_desc, '')), 'C') ||
    setweight(to_tsvector('english', coalesce(NEW.location, '')), 'D');
  RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- Create trigger to update search vector on insert or update
DROP TRIGGER IF EXISTS celebration_entries_search_update ON celebration_entries;
CREATE TRIGGER celebration_entries_search_update
BEFORE INSERT OR UPDATE ON celebration_entries
FOR EACH ROW EXECUTE FUNCTION celebration_entries_search_trigger();

-- Update existing entries (if any)
UPDATE celebration_entries SET updated_at = updated_at WHERE id > 0;

-- Initial category data
INSERT INTO categories (slug, name, description, sort_order, visible)
VALUES 
('people', 'People', 'Notable Nigerians who have made significant contributions to various fields.', 1, true),
('places', 'Places', 'Important locations and landmarks throughout Nigeria.', 2, true),
('events', 'Events', 'Significant festivals, celebrations, and historical events in Nigeria.', 3, true)
ON CONFLICT (slug) DO NOTHING;

-- Get IDs of root categories
DO $$
DECLARE
    people_id BIGINT;
    places_id BIGINT;
    events_id BIGINT;
BEGIN
    SELECT id INTO people_id FROM categories WHERE slug = 'people';
    SELECT id INTO places_id FROM categories WHERE slug = 'places';
    SELECT id INTO events_id FROM categories WHERE slug = 'events';
    
    -- Insert subcategories for People
    INSERT INTO categories (slug, name, description, parent_id, sort_order, visible)
    VALUES 
    ('arts-literature', 'Arts & Literature', 'Writers, artists, musicians, and cultural contributors', people_id, 1, true),
    ('science-technology', 'Science & Technology', 'Innovators, researchers, and tech pioneers', people_id, 2, true),
    ('business-philanthropy', 'Business & Philanthropy', 'Entrepreneurs, business leaders, and philanthropists', people_id, 3, true),
    ('activism-rights', 'Activism & Human Rights', 'Change-makers and advocates for justice', people_id, 4, true),
    ('sports', 'Sports', 'Athletes, coaches, and sports administrators', people_id, 5, true),
    ('governance-leadership', 'Governance & Leadership', 'Impactful leaders and public servants', people_id, 6, true)
    ON CONFLICT (slug) DO NOTHING;
    
    -- Insert subcategories for Places
    INSERT INTO categories (slug, name, description, parent_id, sort_order, visible)
    VALUES 
    ('natural-wonders', 'Natural Wonders', 'Spectacular landscapes and natural formations', places_id, 1, true),
    ('historic-sites', 'Historic Sites', 'Monuments and locations of historical significance', places_id, 2, true),
    ('cultural-venues', 'Cultural Venues', 'Museums, theaters, and cultural institutions', places_id, 3, true),
    ('modern-landmarks', 'Modern Landmarks', 'Contemporary architectural achievements', places_id, 4, true)
    ON CONFLICT (slug) DO NOTHING;
    
    -- Insert subcategories for Events
    INSERT INTO categories (slug, name, description, parent_id, sort_order, visible)
    VALUES 
    ('festivals-carnivals', 'Festivals & Carnivals', 'Cultural celebrations and traditional festivals', events_id, 1, true),
    ('conferences-summits', 'Conferences & Summits', 'Significant gatherings and intellectual events', events_id, 2, true),
    ('sporting-events', 'Sporting Events', 'Major competitions and athletic achievements', events_id, 3, true),
    ('memorials-commemorations', 'Memorials & Commemorations', 'Events honoring historical moments and figures', events_id, 4, true)
    ON CONFLICT (slug) DO NOTHING;
END $$;