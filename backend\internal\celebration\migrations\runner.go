package migrations

import (
	"embed"
	"fmt"
	"io/fs"
	"sort"
	"strconv"
	"strings"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"
)

//go:embed *.sql
var sqlMigrations embed.FS

// Runner handles database migrations for the celebration service
type Runner struct {
	db     *sqlx.DB
	logger *logrus.Logger
}

// NewRunner creates a new migration runner
func NewRunner(db *sqlx.DB, logger *logrus.Logger) *Runner {
	return &Runner{
		db:     db,
		logger: logger,
	}
}

// Run applies all pending migrations
func (r *Runner) Run() error {
	// Create migrations table if it doesn't exist
	if err := r.createMigrationsTable(); err != nil {
		return fmt.Errorf("failed to create migrations table: %w", err)
	}

	// Get applied migrations
	appliedMigrations, err := r.getAppliedMigrations()
	if err != nil {
		return fmt.Errorf("failed to get applied migrations: %w", err)
	}

	// Get all migration files
	migrationFiles, err := r.getMigrationFiles()
	if err != nil {
		return fmt.Errorf("failed to get migration files: %w", err)
	}

	// Sort migration files by version number
	sort.Strings(migrationFiles)

	// Apply pending migrations
	for _, file := range migrationFiles {
		version, _ := r.parseVersionFromFilename(file)
		if _, ok := appliedMigrations[version]; !ok {
			r.logger.Infof("Applying migration: %s", file)
			if err := r.applyMigration(file); err != nil {
				return fmt.Errorf("failed to apply migration %s: %w", file, err)
			}
			r.logger.Infof("Migration applied: %s", file)
		} else {
			r.logger.Debugf("Skipping already applied migration: %s", file)
		}
	}

	return nil
}

// createMigrationsTable creates the table to track applied migrations
func (r *Runner) createMigrationsTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS celebration_migrations (
		version INT PRIMARY KEY,
		name VARCHAR(255) NOT NULL,
		applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
	);
	`
	_, err := r.db.Exec(query)
	return err
}

// getAppliedMigrations returns a map of already applied migrations
func (r *Runner) getAppliedMigrations() (map[int]bool, error) {
	result := make(map[int]bool)
	
	rows, err := r.db.Query("SELECT version FROM celebration_migrations")
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	for rows.Next() {
		var version int
		if err := rows.Scan(&version); err != nil {
			return nil, err
		}
		result[version] = true
	}
	
	return result, nil
}

// getMigrationFiles returns a list of SQL migration files
func (r *Runner) getMigrationFiles() ([]string, error) {
	var files []string
	
	entries, err := fs.ReadDir(sqlMigrations, ".")
	if err != nil {
		return nil, err
	}
	
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".sql") {
			files = append(files, entry.Name())
		}
	}
	
	return files, nil
}

// parseVersionFromFilename extracts the version number from a filename
func (r *Runner) parseVersionFromFilename(filename string) (int, error) {
	parts := strings.Split(filename, "_")
	if len(parts) < 2 {
		return 0, fmt.Errorf("invalid migration filename format: %s", filename)
	}
	
	version, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, fmt.Errorf("invalid migration version in filename %s: %w", filename, err)
	}
	
	return version, nil
}

// applyMigration executes a migration file and records it as applied
func (r *Runner) applyMigration(filename string) error {
	// Read migration file
	content, err := fs.ReadFile(sqlMigrations, filename)
	if err != nil {
		return fmt.Errorf("failed to read migration file: %w", err)
	}
	
	// Start a transaction
	tx, err := r.db.Begin()
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	
	// Execute migration
	_, err = tx.Exec(string(content))
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to execute migration: %w", err)
	}
	
	// Record the migration
	version, err := r.parseVersionFromFilename(filename)
	if err != nil {
		tx.Rollback()
		return err
	}
	
	_, err = tx.Exec(
		"INSERT INTO celebration_migrations (version, name) VALUES ($1, $2)",
		version, filename,
	)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to record migration: %w", err)
	}
	
	// Commit the transaction
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	
	return nil
}