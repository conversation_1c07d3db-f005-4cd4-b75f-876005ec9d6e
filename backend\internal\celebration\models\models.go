package models

import (
        "database/sql"
        "time"
)

// Category represents a category for celebration entries
type Category struct {
        ID          int64       `json:"id" db:"id"`
        Name        string      `json:"name" db:"name"`
        Description *string     `json:"description,omitempty" db:"description"`
        Slug        string      `json:"slug" db:"slug"`
        ParentID    *int64      `json:"parent_id,omitempty" db:"parent_id"`
        ImageURL    *string     `json:"image_url,omitempty" db:"image_url"`
        IconSVG     *string     `json:"icon_svg,omitempty" db:"icon_svg"`
        SortOrder   int         `json:"sort_order" db:"sort_order"`
        Visible     bool        `json:"visible" db:"visible"`
        CreatedAt   time.Time   `json:"created_at" db:"created_at"`
        UpdatedAt   time.Time   `json:"updated_at" db:"updated_at"`
        // Not stored in DB, populated by service
        SubCategories []Category `json:"sub_categories,omitempty" db:"-"`
}

// CelebrationEntry is the base model for all entry types
type CelebrationEntry struct {
        ID              int64      `json:"id" db:"id"`
        EntryType       string     `json:"entry_type" db:"entry_type"`
        Slug            string     `json:"slug" db:"slug"`
        Title           string     `json:"title" db:"title"`
        ShortDesc       string     `json:"short_desc" db:"short_desc"`
        FullDesc        *string    `json:"full_desc,omitempty" db:"full_desc"`
        PrimaryImageURL *string    `json:"primary_image_url,omitempty" db:"primary_image_url"`
        Location        *string    `json:"location,omitempty" db:"location"`
        FeaturedRank    int        `json:"featured_rank" db:"featured_rank"`
        ViewCount       int64      `json:"view_count" db:"view_count"`
        LikeCount       int64      `json:"like_count" db:"like_count"`
        Status          string     `json:"status" db:"status"`
        SearchVector    string     `json:"-" db:"search_vector"`
        SubmittedBy     *int64     `json:"submitted_by,omitempty" db:"submitted_by"`
        ApprovedBy      *int64     `json:"approved_by,omitempty" db:"approved_by"`
        ApprovedAt      *time.Time `json:"approved_at,omitempty" db:"approved_at"`
        CreatedAt       time.Time  `json:"created_at" db:"created_at"`
        UpdatedAt       time.Time  `json:"updated_at" db:"updated_at"`
        // Relationships - not stored in DB, populated by repository
        Categories []Category   `json:"categories,omitempty" db:"-"`
        Media      []EntryMedia `json:"media,omitempty" db:"-"`
        Facts      []EntryFact  `json:"facts,omitempty" db:"-"`
}

// PersonEntry extends CelebrationEntry with person-specific fields
type PersonEntry struct {
        PersonID      int64        `json:"person_id" db:"id"`
        CelebrationEntry
        BirthDate     *time.Time   `json:"birth_date,omitempty" db:"birth_date"`
        DeathDate     *time.Time   `json:"death_date,omitempty" db:"death_date"`
        Profession    *string      `json:"profession,omitempty" db:"profession"`
        Achievements  *string      `json:"achievements,omitempty" db:"achievements"`
        Contributions *string      `json:"contributions,omitempty" db:"contributions"`
        Education     *string      `json:"education,omitempty" db:"education"`
        RelatedLinks  *string      `json:"related_links,omitempty" db:"related_links"`
}

// PlaceEntry extends CelebrationEntry with place-specific fields
type PlaceEntry struct {
        PlaceID       int64        `json:"place_id" db:"id"`
        CelebrationEntry
        PlaceType     *string      `json:"place_type,omitempty" db:"place_type"`
        Latitude      float64      `json:"latitude,omitempty" db:"latitude"`
        Longitude     float64      `json:"longitude,omitempty" db:"longitude"`
        Address       *string      `json:"address,omitempty" db:"address"`
        VisitingHours *string      `json:"visiting_hours,omitempty" db:"visiting_hours"`
        VisitingFees  *string      `json:"visiting_fees,omitempty" db:"visiting_fees"`
        Accessibility *string      `json:"accessibility,omitempty" db:"accessibility"`
        History       *string      `json:"history,omitempty" db:"history"`
}

// EventEntry extends CelebrationEntry with event-specific fields
type EventEntry struct {
        EventID           int64        `json:"event_id" db:"id"`
        CelebrationEntry
        EventType         *string      `json:"event_type,omitempty" db:"event_type"`
        StartDate         *time.Time   `json:"start_date,omitempty" db:"start_date"`
        EndDate           *time.Time   `json:"end_date,omitempty" db:"end_date"`
        IsRecurring       bool         `json:"is_recurring" db:"is_recurring"`
        RecurrencePattern *string      `json:"recurrence_pattern,omitempty" db:"recurrence_pattern"`
        Organizer         *string      `json:"organizer,omitempty" db:"organizer"`
        ContactInfo       *string      `json:"contact_info,omitempty" db:"contact_info"`
        EventHistory      *string      `json:"event_history,omitempty" db:"event_history"`
}

// EntryMedia represents media items associated with entries
type EntryMedia struct {
        ID                 int64     `json:"id" db:"id"`
        CelebrationEntryID int64     `json:"celebration_entry_id" db:"celebration_entry_id"`
        MediaType          string    `json:"media_type" db:"media_type"`
        URL                string    `json:"url" db:"url"`
        Caption            *string   `json:"caption,omitempty" db:"caption"`
        SortOrder          int       `json:"sort_order" db:"sort_order"`
        CreatedAt          time.Time `json:"created_at" db:"created_at"`
        UpdatedAt          time.Time `json:"updated_at" db:"updated_at"`
}

// EntryFact represents key facts about an entry
type EntryFact struct {
        ID                 int64     `json:"id" db:"id"`
        CelebrationEntryID int64     `json:"celebration_entry_id" db:"celebration_entry_id"`
        Label              string    `json:"label" db:"label"`
        Value              string    `json:"value" db:"value"`
        SortOrder          int       `json:"sort_order" db:"sort_order"`
        CreatedAt          time.Time `json:"created_at" db:"created_at"`
        UpdatedAt          time.Time `json:"updated_at" db:"updated_at"`
}

// EntryComment represents user comments on an entry
type EntryComment struct {
        ID                 int64       `json:"id" db:"id"`
        CelebrationEntryID int64       `json:"celebration_entry_id" db:"celebration_entry_id"`
        UserID             int64       `json:"user_id" db:"user_id"`
        Content            string      `json:"content" db:"content"`
        Status             string      `json:"status" db:"status"`
        Likes              int         `json:"likes" db:"likes"`
        ParentID           *int64      `json:"parent_id,omitempty" db:"parent_id"`
        CreatedAt          time.Time   `json:"created_at" db:"created_at"`
        UpdatedAt          time.Time   `json:"updated_at" db:"updated_at"`
}

// EntrySubmission represents a user submission for a new entry or modification
type EntrySubmission struct {
        ID            int64        `json:"id" db:"id"`
        UserID        int64        `json:"user_id" db:"user_id"`
        EntryType     string       `json:"entry_type" db:"entry_type"`
        TargetEntryID *int64       `json:"target_entry_id,omitempty" db:"target_entry_id"`
        Title         string       `json:"title" db:"title"`
        Content       string       `json:"content" db:"content"`
        Status        string       `json:"status" db:"status"`
        AdminNotes    *string      `json:"admin_notes,omitempty" db:"admin_notes"`
        ReviewedBy    *int64       `json:"reviewed_by,omitempty" db:"reviewed_by"`
        ReviewedAt    sql.NullTime `json:"reviewed_at,omitempty" db:"reviewed_at"`
        VoteCount     int          `json:"vote_count" db:"vote_count"`
        CreatedAt     time.Time    `json:"created_at" db:"created_at"`
        UpdatedAt     time.Time    `json:"updated_at" db:"updated_at"`
}