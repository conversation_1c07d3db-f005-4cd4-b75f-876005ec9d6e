package models

import (
	"time"
)

// EntryFlag represents a flag on an entry
type EntryFlag struct {
	ID          int64      `json:"id" db:"id"`
	EntryID     int64      `json:"entry_id" db:"entry_id"`
	UserID      int64      `json:"user_id" db:"user_id"`
	Reason      string     `json:"reason" db:"reason"`
	Status      string     `json:"status" db:"status"` // pending, approved, rejected, hidden
	ModeratorID *int64     `json:"moderator_id,omitempty" db:"moderator_id"`
	ModeratedAt time.Time  `json:"moderated_at,omitempty" db:"moderated_at"`
	Notes       string     `json:"notes,omitempty" db:"notes"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at" db:"updated_at"`
	// Relationships - not stored in DB, populated by repository
	Entry       *CelebrationEntry `json:"entry,omitempty" db:"-"`
	User        *User             `json:"user,omitempty" db:"-"`
	Moderator   *User             `json:"moderator,omitempty" db:"-"`
}

// EntryModerationQueue represents an entry in the moderation queue
type EntryModerationQueue struct {
	ID          int64      `json:"id" db:"id"`
	EntryID     int64      `json:"entry_id" db:"entry_id"`
	UserID      int64      `json:"user_id" db:"user_id"`
	Reason      string     `json:"reason" db:"reason"`
	Status      string     `json:"status" db:"status"` // pending, in_review, approved, rejected, hidden
	Priority    int        `json:"priority" db:"priority"` // 1-5 (5 highest)
	AssignedTo  *int64     `json:"assigned_to,omitempty" db:"assigned_to"`
	ModeratorID *int64     `json:"moderator_id,omitempty" db:"moderator_id"`
	ModeratedAt time.Time  `json:"moderated_at,omitempty" db:"moderated_at"`
	Notes       string     `json:"notes,omitempty" db:"notes"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at" db:"updated_at"`
	// Relationships - not stored in DB, populated by repository
	Entry       *CelebrationEntry `json:"entry,omitempty" db:"-"`
	User        *User             `json:"user,omitempty" db:"-"`
	Moderator   *User             `json:"moderator,omitempty" db:"-"`
}

// EntryFilterResult represents the result of content filtering on an entry
type EntryFilterResult struct {
	ID             int64     `json:"id" db:"id"`
	EntryID        int64     `json:"entry_id" db:"entry_id"`
	TriggeredRules string    `json:"triggered_rules" db:"triggered_rules"` // JSON array of rule IDs
	Action         string    `json:"action" db:"action"` // none, flag, hide, reject
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time `json:"updated_at" db:"updated_at"`
	// Relationships - not stored in DB, populated by repository
	Entry          *CelebrationEntry `json:"entry,omitempty" db:"-"`
}

// User represents a simplified user model for moderation purposes
type User struct {
	ID        int64     `json:"id" db:"id"`
	Username  string    `json:"username" db:"username"`
	Email     string    `json:"email" db:"email"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}
