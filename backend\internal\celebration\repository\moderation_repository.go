package repository

import (
	"context"
	"database/sql"
	"errors"

	"github.com/greatnigeria/internal/celebration/models"
)

// CreateEntryFlag creates a new flag for an entry
func (r *CelebrationRepository) CreateEntryFlag(ctx context.Context, flag *models.EntryFlag) error {
	query := `
		INSERT INTO entry_flags (
			entry_id, user_id, reason, status, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6
		) RETURNING id
	`

	err := r.db.QueryRowxContext(
		ctx,
		query,
		flag.EntryID,
		flag.UserID,
		flag.Reason,
		flag.Status,
		flag.CreatedAt,
		flag.UpdatedAt,
	).Scan(&flag.ID)

	return err
}

// GetFlagByID retrieves a flag by its ID
func (r *CelebrationRepository) GetFlagByID(ctx context.Context, id int64) (*models.EntryFlag, error) {
	var flag models.EntryFlag
	err := r.db.GetContext(ctx, &flag, `
		SELECT * FROM entry_flags WHERE id = $1
	`, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &flag, nil
}

// GetFlagsByEntryID retrieves flags for an entry
func (r *CelebrationRepository) GetFlagsByEntryID(ctx context.Context, entryID int64) ([]models.EntryFlag, error) {
	var flags []models.EntryFlag
	err := r.db.SelectContext(ctx, &flags, `
		SELECT * FROM entry_flags WHERE entry_id = $1 ORDER BY created_at DESC
	`, entryID)
	if err != nil {
		return nil, err
	}
	return flags, nil
}

// GetFlaggedEntries retrieves flagged entries
func (r *CelebrationRepository) GetFlaggedEntries(ctx context.Context, status string, page, pageSize int) ([]models.EntryFlag, int64, error) {
	var flags []models.EntryFlag
	var count int64
	offset := (page - 1) * pageSize

	// Get total count
	var countQuery string
	var countArgs []interface{}

	if status == "" {
		countQuery = `SELECT COUNT(*) FROM entry_flags`
		countArgs = []interface{}{}
	} else {
		countQuery = `SELECT COUNT(*) FROM entry_flags WHERE status = $1`
		countArgs = []interface{}{status}
	}

	err := r.db.GetContext(ctx, &count, countQuery, countArgs...)
	if err != nil {
		return nil, 0, err
	}

	// Get flags
	var query string
	var args []interface{}

	if status == "" {
		query = `
			SELECT * FROM entry_flags
			ORDER BY created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{pageSize, offset}
	} else {
		query = `
			SELECT * FROM entry_flags
			WHERE status = $1
			ORDER BY created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{status, pageSize, offset}
	}

	err = r.db.SelectContext(ctx, &flags, query, args...)
	if err != nil {
		return nil, 0, err
	}

	// Get entry details for each flag
	for i := range flags {
		entry, err := r.FindEntryByID(ctx, flags[i].EntryID)
		if err != nil {
			return nil, 0, err
		}
		flags[i].Entry = entry
	}

	return flags, count, nil
}

// UpdateEntryFlag updates a flag
func (r *CelebrationRepository) UpdateEntryFlag(ctx context.Context, flag *models.EntryFlag) error {
	query := `
		UPDATE entry_flags SET
			status = $1,
			moderator_id = $2,
			moderated_at = $3,
			notes = $4,
			updated_at = $5
		WHERE id = $6
	`

	_, err := r.db.ExecContext(
		ctx,
		query,
		flag.Status,
		flag.ModeratorID,
		flag.ModeratedAt,
		flag.Notes,
		flag.UpdatedAt,
		flag.ID,
	)

	return err
}

// CreateModerationQueueItem creates a new moderation queue item
func (r *CelebrationRepository) CreateModerationQueueItem(ctx context.Context, item *models.EntryModerationQueue) error {
	query := `
		INSERT INTO entry_moderation_queue (
			entry_id, user_id, reason, status, priority, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7
		) RETURNING id
	`

	err := r.db.QueryRowxContext(
		ctx,
		query,
		item.EntryID,
		item.UserID,
		item.Reason,
		item.Status,
		item.Priority,
		item.CreatedAt,
		item.UpdatedAt,
	).Scan(&item.ID)

	return err
}

// GetModerationQueueItemByID retrieves a moderation queue item by its ID
func (r *CelebrationRepository) GetModerationQueueItemByID(ctx context.Context, id int64) (*models.EntryModerationQueue, error) {
	var item models.EntryModerationQueue
	err := r.db.GetContext(ctx, &item, `
		SELECT * FROM entry_moderation_queue WHERE id = $1
	`, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

// GetModerationQueueItems retrieves moderation queue items
func (r *CelebrationRepository) GetModerationQueueItems(ctx context.Context, status string, page, pageSize int) ([]models.EntryModerationQueue, int64, error) {
	var items []models.EntryModerationQueue
	var count int64
	offset := (page - 1) * pageSize

	// Get total count
	var countQuery string
	var countArgs []interface{}

	if status == "" {
		countQuery = `SELECT COUNT(*) FROM entry_moderation_queue`
		countArgs = []interface{}{}
	} else {
		countQuery = `SELECT COUNT(*) FROM entry_moderation_queue WHERE status = $1`
		countArgs = []interface{}{status}
	}

	err := r.db.GetContext(ctx, &count, countQuery, countArgs...)
	if err != nil {
		return nil, 0, err
	}

	// Get items
	var query string
	var args []interface{}

	if status == "" {
		query = `
			SELECT * FROM entry_moderation_queue
			ORDER BY priority DESC, created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{pageSize, offset}
	} else {
		query = `
			SELECT * FROM entry_moderation_queue
			WHERE status = $1
			ORDER BY priority DESC, created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{status, pageSize, offset}
	}

	err = r.db.SelectContext(ctx, &items, query, args...)
	if err != nil {
		return nil, 0, err
	}

	// Get entry details for each item
	for i := range items {
		entry, err := r.FindEntryByID(ctx, items[i].EntryID)
		if err != nil {
			return nil, 0, err
		}
		items[i].Entry = entry
	}

	return items, count, nil
}

// UpdateModerationQueueItem updates a moderation queue item
func (r *CelebrationRepository) UpdateModerationQueueItem(ctx context.Context, item *models.EntryModerationQueue) error {
	query := `
		UPDATE entry_moderation_queue SET
			status = $1,
			assigned_to = $2,
			moderator_id = $3,
			moderated_at = $4,
			notes = $5,
			updated_at = $6
		WHERE id = $7
	`

	_, err := r.db.ExecContext(
		ctx,
		query,
		item.Status,
		item.AssignedTo,
		item.ModeratorID,
		item.ModeratedAt,
		item.Notes,
		item.UpdatedAt,
		item.ID,
	)

	return err
}

// CreateEntryFilterResult creates a new filter result for an entry
func (r *CelebrationRepository) CreateEntryFilterResult(ctx context.Context, result *models.EntryFilterResult) error {
	query := `
		INSERT INTO entry_filter_results (
			entry_id, triggered_rules, action, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5
		) RETURNING id
	`

	err := r.db.QueryRowxContext(
		ctx,
		query,
		result.EntryID,
		result.TriggeredRules,
		result.Action,
		result.CreatedAt,
		result.UpdatedAt,
	).Scan(&result.ID)

	return err
}

// GetFilterResultByEntryID retrieves a filter result for an entry
func (r *CelebrationRepository) GetFilterResultByEntryID(ctx context.Context, entryID int64) (*models.EntryFilterResult, error) {
	var result models.EntryFilterResult
	err := r.db.GetContext(ctx, &result, `
		SELECT * FROM entry_filter_results WHERE entry_id = $1
	`, entryID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &result, nil
}
