package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/greatnigeria/internal/celebration/models"
)

// ModerationService defines the interface for moderation operations
type ModerationService interface {
	// Flag content for moderation
	FlagEntry(ctx context.Context, entryID, userID int64, reason string) error

	// Get flagged entries
	GetFlaggedEntries(ctx context.Context, status string, page, pageSize int) ([]models.EntryFlag, int64, error)

	// Get flag by ID
	GetFlagByID(ctx context.Context, id int64) (*models.EntryFlag, error)

	// Review flagged content
	ReviewFlaggedEntry(ctx context.Context, flagID, moderatorID int64, decision, notes string) error

	// Add entry to moderation queue
	AddToModerationQueue(ctx context.Context, entryID, userID int64, reason string, priority int) error

	// Get moderation queue items
	GetModerationQueueItems(ctx context.Context, status string, page, pageSize int) ([]models.EntryModerationQueue, int64, error)

	// Get moderation queue item by ID
	GetModerationQueueItemByID(ctx context.Context, id int64) (*models.EntryModerationQueue, error)

	// Process moderation queue item
	ProcessModerationQueueItem(ctx context.Context, queueID, moderatorID int64, decision, notes string) error

	// Check if user has moderator privileges
	HasModeratorPrivileges(ctx context.Context, userID int64) (bool, error)

	// Apply content filter to entry
	FilterEntryContent(ctx context.Context, entryID int64) (*models.EntryFilterResult, error)
}

// CelebrationModerationService implements the ModerationService interface
type CelebrationModerationService struct {
	repo           CelebrationRepository
	discussionRepo DiscussionRepository
}

// NewModerationService creates a new moderation service
func NewModerationService(repo CelebrationRepository, discussionRepo DiscussionRepository) ModerationService {
	return &CelebrationModerationService{
		repo:           repo,
		discussionRepo: discussionRepo,
	}
}

// FlagEntry flags an entry for moderation
func (s *CelebrationModerationService) FlagEntry(ctx context.Context, entryID, userID int64, reason string) error {
	// Check if entry exists
	entry, err := s.repo.FindEntryByID(ctx, entryID)
	if err != nil {
		return err
	}
	if entry == nil {
		return errors.New("entry not found")
	}

	// Create flag
	flag := &models.EntryFlag{
		EntryID:   entryID,
		UserID:    userID,
		Reason:    reason,
		Status:    "pending",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Save flag
	if err := s.repo.CreateEntryFlag(ctx, flag); err != nil {
		return err
	}

	// Add to moderation queue
	return s.AddToModerationQueue(ctx, entryID, userID, reason, 3) // Default priority
}

// GetFlaggedEntries gets flagged entries
func (s *CelebrationModerationService) GetFlaggedEntries(ctx context.Context, status string, page, pageSize int) ([]models.EntryFlag, int64, error) {
	return s.repo.GetFlaggedEntries(ctx, status, page, pageSize)
}

// GetFlagByID gets a flag by ID
func (s *CelebrationModerationService) GetFlagByID(ctx context.Context, id int64) (*models.EntryFlag, error) {
	return s.repo.GetFlagByID(ctx, id)
}

// ReviewFlaggedEntry reviews a flagged entry
func (s *CelebrationModerationService) ReviewFlaggedEntry(ctx context.Context, flagID, moderatorID int64, decision, notes string) error {
	// Check if flag exists
	flag, err := s.repo.GetFlagByID(ctx, flagID)
	if err != nil {
		return err
	}
	if flag == nil {
		return errors.New("flag not found")
	}

	// Check if user has moderator privileges
	hasMod, err := s.HasModeratorPrivileges(ctx, moderatorID)
	if err != nil {
		return err
	}
	if !hasMod {
		return errors.New("user does not have moderator privileges")
	}

	// Update flag
	flag.Status = decision
	flag.ModeratorID = &moderatorID
	flag.ModeratedAt = time.Now()
	flag.Notes = notes
	flag.UpdatedAt = time.Now()

	// Save flag
	if err := s.repo.UpdateEntryFlag(ctx, flag); err != nil {
		return err
	}

	// If decision is to hide the entry, update entry status
	if decision == "hidden" {
		entry, err := s.repo.FindEntryByID(ctx, flag.EntryID)
		if err != nil {
			return err
		}
		if entry == nil {
			return errors.New("entry not found")
		}

		entry.Status = "hidden"
		entry.UpdatedAt = time.Now()

		if err := s.repo.UpdateEntry(ctx, entry); err != nil {
			return err
		}
	}

	return nil
}

// AddToModerationQueue adds an entry to the moderation queue
func (s *CelebrationModerationService) AddToModerationQueue(ctx context.Context, entryID, userID int64, reason string, priority int) error {
	// Check if entry exists
	entry, err := s.repo.FindEntryByID(ctx, entryID)
	if err != nil {
		return err
	}
	if entry == nil {
		return errors.New("entry not found")
	}

	// Validate priority
	if priority < 1 || priority > 5 {
		priority = 3 // Default to medium priority
	}

	// Create queue item
	queueItem := &models.EntryModerationQueue{
		EntryID:   entryID,
		UserID:    userID,
		Reason:    reason,
		Status:    "pending",
		Priority:  priority,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Save queue item
	return s.repo.CreateModerationQueueItem(ctx, queueItem)
}

// GetModerationQueueItems gets moderation queue items
func (s *CelebrationModerationService) GetModerationQueueItems(ctx context.Context, status string, page, pageSize int) ([]models.EntryModerationQueue, int64, error) {
	return s.repo.GetModerationQueueItems(ctx, status, page, pageSize)
}

// GetModerationQueueItemByID gets a moderation queue item by ID
func (s *CelebrationModerationService) GetModerationQueueItemByID(ctx context.Context, id int64) (*models.EntryModerationQueue, error) {
	return s.repo.GetModerationQueueItemByID(ctx, id)
}

// ProcessModerationQueueItem processes a moderation queue item
func (s *CelebrationModerationService) ProcessModerationQueueItem(ctx context.Context, queueID, moderatorID int64, decision, notes string) error {
	// Check if queue item exists
	queueItem, err := s.repo.GetModerationQueueItemByID(ctx, queueID)
	if err != nil {
		return err
	}
	if queueItem == nil {
		return errors.New("moderation queue item not found")
	}

	// Check if user has moderator privileges
	hasMod, err := s.HasModeratorPrivileges(ctx, moderatorID)
	if err != nil {
		return err
	}
	if !hasMod {
		return errors.New("user does not have moderator privileges")
	}

	// Update queue item
	queueItem.Status = decision
	queueItem.ModeratorID = &moderatorID
	queueItem.ModeratedAt = time.Now()
	queueItem.Notes = notes
	queueItem.UpdatedAt = time.Now()

	// Save queue item
	if err := s.repo.UpdateModerationQueueItem(ctx, queueItem); err != nil {
		return err
	}

	// Update entry status based on decision
	entry, err := s.repo.FindEntryByID(ctx, queueItem.EntryID)
	if err != nil {
		return err
	}
	if entry == nil {
		return errors.New("entry not found")
	}

	switch decision {
	case "approved":
		entry.Status = "published"
		entry.ApprovedBy = &moderatorID
		entry.ApprovedAt = &queueItem.ModeratedAt
	case "rejected":
		entry.Status = "rejected"
	case "hidden":
		entry.Status = "hidden"
	}

	entry.UpdatedAt = time.Now()

	return s.repo.UpdateEntry(ctx, entry)
}

// HasModeratorPrivileges checks if a user has moderator privileges
func (s *CelebrationModerationService) HasModeratorPrivileges(ctx context.Context, userID int64) (bool, error) {
	// Convert int64 to uint for discussion repository
	uintUserID := uint(userID)

	// Check if user is a moderator in the discussion system
	isModerator, err := s.discussionRepo.IsUserModerator(uintUserID)
	if err != nil {
		return false, fmt.Errorf("error checking moderator privileges: %w", err)
	}

	return isModerator, nil
}

// FilterEntryContent applies content filtering to an entry
func (s *CelebrationModerationService) FilterEntryContent(ctx context.Context, entryID int64) (*models.EntryFilterResult, error) {
	// Get entry
	entry, err := s.repo.FindEntryByID(ctx, entryID)
	if err != nil {
		return nil, err
	}
	if entry == nil {
		return nil, errors.New("entry not found")
	}

	// Get active moderation rules - not used in this simplified implementation
	_, err = s.discussionRepo.GetActiveRulesByType("entry")
	if err != nil {
		return nil, fmt.Errorf("error getting moderation rules: %w", err)
	}

	// Apply rules to entry content
	// This is a simplified implementation - in a real system, you would apply
	// each rule to the content and check for matches

	// For now, we'll just create a filter result with no triggered rules
	filterResult := &models.EntryFilterResult{
		EntryID:        entryID,
		TriggeredRules: "",
		Action:         "none",
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Save filter result
	if err := s.repo.CreateEntryFilterResult(ctx, filterResult); err != nil {
		return nil, err
	}

	return filterResult, nil
}

// CelebrationRepository defines the interface for accessing celebration data
type CelebrationRepository interface {
	FindEntryByID(ctx context.Context, id int64) (*models.CelebrationEntry, error)
	UpdateEntry(ctx context.Context, entry *models.CelebrationEntry) error
	CreateEntryFlag(ctx context.Context, flag *models.EntryFlag) error
	GetFlagByID(ctx context.Context, id int64) (*models.EntryFlag, error)
	GetFlagsByEntryID(ctx context.Context, entryID int64) ([]models.EntryFlag, error)
	GetFlaggedEntries(ctx context.Context, status string, page, pageSize int) ([]models.EntryFlag, int64, error)
	UpdateEntryFlag(ctx context.Context, flag *models.EntryFlag) error
	CreateModerationQueueItem(ctx context.Context, item *models.EntryModerationQueue) error
	GetModerationQueueItemByID(ctx context.Context, id int64) (*models.EntryModerationQueue, error)
	GetModerationQueueItems(ctx context.Context, status string, page, pageSize int) ([]models.EntryModerationQueue, int64, error)
	UpdateModerationQueueItem(ctx context.Context, item *models.EntryModerationQueue) error
	CreateEntryFilterResult(ctx context.Context, result *models.EntryFilterResult) error
	GetFilterResultByEntryID(ctx context.Context, entryID int64) (*models.EntryFilterResult, error)
}

// DiscussionRepository is an interface for accessing the discussion repository
type DiscussionRepository interface {
	IsUserModerator(userID uint) (bool, error)
	GetActiveRulesByType(appliesTo string) ([]string, error)
}
