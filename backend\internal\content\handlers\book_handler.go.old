package handlers

import (
        "net/http"
        "strconv"
        "fmt"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/greatnigeria/internal/content/models"
        "github.com/greatnigeria/internal/content/service"
)

// BookHandler defines handlers for book-related endpoints
type BookHandler struct {
        bookService service.BookService
}

// NewBookHandler creates a new book handler instance
func NewBookHandler(bookService service.BookService) *BookHandler {
        return &BookHandler{
                bookService: bookService,
        }
}

// GetBooks handles GET /api/v1/books
func (h *BookHandler) GetBooks(c *gin.Context) {
        // Get query parameters
        limit := c.DefaultQuery("limit", "10")
        offset := c.Default<PERSON>uery("offset", "0")
        
        // Convert to integers
        limitInt, _ := strconv.Atoi(limit)
        offsetInt, _ := strconv.Atoi(offset)
        
        // Get books from service
        books, err := h.bookService.GetBooks(limitInt, offsetInt)
        if err != nil {
                c.JSO<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to fetch books"})
                return
        }
        
        c.<PERSON>SO<PERSON>(http.StatusOK, books)
}

// GetBookByID handles GET /api/v1/books/:id
func (h *BookHandler) GetBookByID(c *gin.Context) {
        // Get book ID from URL
        idStr := c.Param("id")
        id, err := strconv.ParseUint(idStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid book ID"})
                return
        }
        
        // Get book from service
        book, err := h.bookService.GetBookByID(uint(id))
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": "Book not found"})
                return
        }
        
        c.JSON(http.StatusOK, book)
}

// GetBookChapters handles GET /api/v1/books/:id/chapters
func (h *BookHandler) GetBookChapters(c *gin.Context) {
        // Get book ID from URL
        idStr := c.Param("id")
        id, err := strconv.ParseUint(idStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid book ID"})
                return
        }
        
        // Get chapters from service
        chapters, err := h.bookService.GetChaptersByBookID(uint(id))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch chapters"})
                return
        }
        
        c.JSON(http.StatusOK, chapters)
}

// GetChapter handles GET /api/v1/books/:id/chapters/:chapterId
func (h *BookHandler) GetChapter(c *gin.Context) {
        // Get chapter ID from URL
        chapterIDStr := c.Param("chapterId")
        chapterID, err := strconv.ParseUint(chapterIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chapter ID"})
                return
        }
        
        // Get chapter from service
        chapter, err := h.bookService.GetChapterByID(uint(chapterID))
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": "Chapter not found"})
                return
        }
        
        c.JSON(http.StatusOK, chapter)
}

// GetSection handles GET /api/v1/books/:id/sections/:sectionId
func (h *BookHandler) GetSection(c *gin.Context) {
        // Get section ID from URL
        sectionIDStr := c.Param("sectionId")
        sectionID, err := strconv.ParseUint(sectionIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid section ID"})
                return
        }
        
        // Get section from service
        section, err := h.bookService.GetSectionByID(uint(sectionID))
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": "Section not found"})
                return
        }
        
        c.JSON(http.StatusOK, section)
}

// SaveProgress handles POST /api/v1/books/:id/progress
func (h *BookHandler) SaveProgress(c *gin.Context) {
        // Get book ID from URL
        idStr := c.Param("id")
        bookID, err := strconv.ParseUint(idStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid book ID"})
                return
        }
        
        // Parse request body
        var request struct {
                SectionID uint    `json:"section_id" binding:"required"`
                Progress  float64 `json:"progress" binding:"required,min=0,max=100"`
        }
        
        if err := c.ShouldBindJSON(&request); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Get user ID from context (set by auth middleware)
        userID, exists := c.Get("user_id")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Save progress
        err = h.bookService.SaveProgress(userID.(uint), uint(bookID), request.SectionID, request.Progress)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save progress"})
                return
        }
        
        c.JSON(http.StatusOK, gin.H{"message": "Progress saved successfully"})
}

// SearchBooks handles GET /api/v1/books/search
func (h *BookHandler) SearchBooks(c *gin.Context) {
        // Get query parameters
        query := c.Query("q")
        if query == "" {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
                return
        }
        
        limit := c.DefaultQuery("limit", "10")
        offset := c.DefaultQuery("offset", "0")
        
        // Convert to integers
        limitInt, _ := strconv.Atoi(limit)
        offsetInt, _ := strconv.Atoi(offset)
        
        // Get search results from service
        results, err := h.bookService.SearchBooks(query, limitInt, offsetInt)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search books"})
                return
        }
        
        c.JSON(http.StatusOK, results)
}

// GetRecommendations handles GET /api/v1/books/:id/recommendations
func (h *BookHandler) GetRecommendations(c *gin.Context) {
        // Get book ID from URL
        idStr := c.Param("id")
        bookID, err := strconv.ParseUint(idStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid book ID"})
                return
        }
        
        // Get user ID from context if available (for personalized recommendations)
        userID, exists := c.Get("user_id")
        
        // Get recommendations
        var recommendations interface{}
        if exists {
                // Get personalized recommendations if user is authenticated
                recommendations, err = h.bookService.GetPersonalizedRecommendations(userID.(uint), uint(bookID))
        } else {
                // Get general recommendations if user is not authenticated
                recommendations, err = h.bookService.GetRecommendations(uint(bookID))
        }
        
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recommendations"})
                return
        }
        
        c.JSON(http.StatusOK, recommendations)
}

// GetInteractiveElement handles GET /api/v1/books/interactive/:interactiveId
func (h *BookHandler) GetInteractiveElement(c *gin.Context) {
        // Get interactive element ID from URL
        idStr := c.Param("interactiveId")
        id, err := strconv.ParseUint(idStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interactive element ID"})
                return
        }
        
        // Get interactive element from service
        element, err := h.bookService.GetInteractiveElementByID(uint(id))
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": "Interactive element not found"})
                return
        }
        
        c.JSON(http.StatusOK, element)
}

// RegisterForumTopic handles POST /api/v1/books/:id/sections/:sectionId/forum
func (h *BookHandler) RegisterForumTopic(c *gin.Context) {
        // Get section ID from URL
        sectionIDStr := c.Param("sectionId")
        sectionID, err := strconv.ParseUint(sectionIDStr, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid section ID"})
                return
        }
        
        // Parse request body
        var request struct {
                Title   string `json:"title" binding:"required"`
                Content string `json:"content" binding:"required"`
        }
        
        if err := c.ShouldBindJSON(&request); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }
        
        // Get user ID from context (set by auth middleware)
        userID, exists := c.Get("user_id")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }
        
        // Create forum topic
        topic := models.ForumTopic{
                SectionID: uint(sectionID),
                UserID:    userID.(uint),
                Title:     request.Title,
                Content:   request.Content,
                CreatedAt: time.Now(),
        }
        
        // Save forum topic
        createdTopic, err := h.bookService.CreateForumTopic(topic)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create forum topic"})
                return
        }
        
        c.JSON(http.StatusCreated, createdTopic)
}

// RegisterRoutes registers the book-related routes
func (h *BookHandler) RegisterRoutes(router *gin.Engine, authMiddleware gin.HandlerFunc) {
        books := router.Group("/api/v1/books")
        {
                books.GET("", h.GetBooks)
                books.GET("/:id", h.GetBookByID)
                books.GET("/:id/chapters", h.GetBookChapters)
                books.GET("/:id/chapters/:chapterId", h.GetChapter)
                books.GET("/:id/sections/:sectionId", h.GetSection)
                books.GET("/search", h.SearchBooks)
                books.GET("/interactive/:interactiveId", h.GetInteractiveElement)
                
                // Protected routes
                authBooks := books.Group("")
                authBooks.Use(authMiddleware)
                {
                        authBooks.POST("/:id/progress", h.SaveProgress)
                        authBooks.POST("/:id/sections/:sectionId/forum", h.RegisterForumTopic)
                }
        }
}
