package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/greatnigeria/internal/courses/models"
	"github.com/greatnigeria/internal/courses/service"
)

// CoursesHandler handles API endpoints for courses
type CoursesHandler struct {
	coursesService service.CoursesService
}

// NewCoursesHandler creates a new courses handler
func NewCoursesHandler(coursesService service.CoursesService) *CoursesHandler {
	return &CoursesHandler{
		coursesService: coursesService,
	}
}

// RegisterRoutes registers all course-related routes
func (h *CoursesHandler) RegisterRoutes(router *gin.RouterGroup) {
	courses := router.Group("/courses")
	{
		// Course endpoints
		courses.GET("", h.GetCourses)
		courses.GET("/:id", h.GetCourseByID)
		courses.GET("/slug/:slug", h.GetCourseBySlug)
		courses.POST("", h.CreateCourse)
		courses.PUT("/:id", h.UpdateCourse)
		courses.DELETE("/:id", h.DeleteCourse)

		// Module endpoints
		courses.GET("/:id/modules", h.GetModulesByCourseID)
		courses.GET("/modules/:id", h.GetModuleByID)
		courses.POST("/:id/modules", h.CreateModule)
		courses.PUT("/modules/:id", h.UpdateModule)
		courses.DELETE("/modules/:id", h.DeleteModule)

		// Lesson endpoints
		courses.GET("/modules/:id/lessons", h.GetLessonsByModuleID)
		courses.GET("/lessons/:id", h.GetLessonByID)
		courses.POST("/modules/:id/lessons", h.CreateLesson)
		courses.PUT("/lessons/:id", h.UpdateLesson)
		courses.DELETE("/lessons/:id", h.DeleteLesson)

		// Quiz endpoints
		courses.GET("/lessons/:id/quiz", h.GetQuizByLessonID)
		courses.GET("/quizzes/:id", h.GetQuizByID)
		courses.POST("/lessons/:id/quiz", h.CreateQuiz)
		courses.PUT("/quizzes/:id", h.UpdateQuiz)
		courses.DELETE("/quizzes/:id", h.DeleteQuiz)

		// Question endpoints
		courses.GET("/quizzes/:id/questions", h.GetQuestionsByQuizID)
		courses.GET("/questions/:id", h.GetQuestionByID)
		courses.POST("/quizzes/:id/questions", h.CreateQuestion)
		courses.PUT("/questions/:id", h.UpdateQuestion)
		courses.DELETE("/questions/:id", h.DeleteQuestion)

		// Assignment endpoints
		courses.GET("/lessons/:id/assignment", h.GetAssignmentByLessonID)
		courses.GET("/assignments/:id", h.GetAssignmentByID)
		courses.POST("/lessons/:id/assignment", h.CreateAssignment)
		courses.PUT("/assignments/:id", h.UpdateAssignment)
		courses.DELETE("/assignments/:id", h.DeleteAssignment)

		// Enrollment endpoints
		courses.GET("/enrollments/user/:userId", h.GetEnrollmentsByUserID)
		courses.GET("/enrollments/course/:courseId", h.GetEnrollmentsByCourseID)
		courses.GET("/enrollments/user/:userId/course/:courseId", h.GetEnrollment)
		courses.POST("/enrollments", h.CreateEnrollment)
		courses.PUT("/enrollments/:id", h.UpdateEnrollment)
		courses.DELETE("/enrollments/:id", h.DeleteEnrollment)

		// Progress endpoints
		courses.GET("/progress/user/:userId/course/:courseId", h.GetProgressByUserAndCourse)
		courses.GET("/progress/user/:userId/lesson/:lessonId", h.GetProgressByUserAndLesson)
		courses.POST("/progress", h.CreateProgress)
		courses.PUT("/progress/:id", h.UpdateProgress)

		// Quiz attempt endpoints
		courses.GET("/quiz-attempts/user/:userId/quiz/:quizId", h.GetQuizAttemptsByUserAndQuiz)
		courses.GET("/quiz-attempts/:id", h.GetQuizAttemptByID)
		courses.POST("/quiz-attempts", h.CreateQuizAttempt)
		courses.PUT("/quiz-attempts/:id", h.UpdateQuizAttempt)

		// Assignment submission endpoints
		courses.GET("/submissions/user/:userId/assignment/:assignmentId", h.GetSubmissionsByUserAndAssignment)
		courses.GET("/submissions/:id", h.GetSubmissionByID)
		courses.POST("/submissions", h.CreateSubmission)
		courses.PUT("/submissions/:id", h.UpdateSubmission)

		// Review endpoints
		courses.GET("/reviews/course/:courseId", h.GetReviewsByCourseID)
		courses.GET("/reviews/user/:userId/course/:courseId", h.GetReviewByUserAndCourse)
		courses.POST("/reviews", h.CreateReview)
		courses.PUT("/reviews/:id", h.UpdateReview)
		courses.DELETE("/reviews/:id", h.DeleteReview)

		// Certificate endpoints
		courses.GET("/certificates/user/:userId", h.GetCertificatesByUserID)
		courses.GET("/certificates/user/:userId/course/:courseId", h.GetCertificateByUserAndCourse)
		courses.POST("/certificates", h.CreateCertificate)

		// Course completion endpoints
		courses.GET("/completion/user/:userId/course/:courseId", h.CheckCourseCompletion)
		courses.POST("/completion/user/:userId/course/:courseId", h.MarkCourseAsCompleted)
		courses.POST("/certificates/generate/user/:userId/course/:courseId", h.GenerateCourseCompletionCertificate)
	}
}

// GetCourses handles GET /courses
func (h *CoursesHandler) GetCourses(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	// Parse filters
	filters := make(map[string]interface{})

	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}

	if instructorID := c.Query("instructorId"); instructorID != "" {
		instructorIDInt, err := strconv.Atoi(instructorID)
		if err == nil {
			filters["instructor_id"] = uint(instructorIDInt)
		}
	}

	if isFree := c.Query("isFree"); isFree != "" {
		isFreeVal := isFree == "true"
		filters["is_free"] = isFreeVal
	}

	if isPublic := c.Query("isPublic"); isPublic != "" {
		isPublicVal := isPublic == "true"
		filters["is_public"] = isPublicVal
	}

	if isFeatured := c.Query("isFeatured"); isFeatured != "" {
		isFeaturedVal := isFeatured == "true"
		filters["is_featured"] = isFeaturedVal
	}

	// Get courses
	courses, total, err := h.coursesService.GetCourses(page, pageSize, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get courses"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": courses,
		"meta": gin.H{
			"page":       page,
			"pageSize":   pageSize,
			"total":      total,
			"totalPages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetCourseByID handles GET /courses/:id
func (h *CoursesHandler) GetCourseByID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Get course
	course, err := h.coursesService.GetCourseByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get course"})
		return
	}

	if course == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	c.JSON(http.StatusOK, course)
}

// GetCourseBySlug handles GET /courses/slug/:slug
func (h *CoursesHandler) GetCourseBySlug(c *gin.Context) {
	// Get slug
	slug := c.Param("slug")

	// Get course
	course, err := h.coursesService.GetCourseBySlug(slug)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get course"})
		return
	}

	if course == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	c.JSON(http.StatusOK, course)
}

// CreateCourse handles POST /courses
func (h *CoursesHandler) CreateCourse(c *gin.Context) {
	var course models.Course
	if err := c.ShouldBindJSON(&course); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Create course
	if err := h.coursesService.CreateCourse(&course); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create course"})
		return
	}

	c.JSON(http.StatusCreated, course)
}

// UpdateCourse handles PUT /courses/:id
func (h *CoursesHandler) UpdateCourse(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Get existing course
	existingCourse, err := h.coursesService.GetCourseByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get course"})
		return
	}

	if existingCourse == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	// Parse request body
	var course models.Course
	if err := c.ShouldBindJSON(&course); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID
	course.ID = uint(id)

	// Update course
	if err := h.coursesService.UpdateCourse(&course); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update course"})
		return
	}

	c.JSON(http.StatusOK, course)
}

// DeleteCourse handles DELETE /courses/:id
func (h *CoursesHandler) DeleteCourse(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Delete course
	if err := h.coursesService.DeleteCourse(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete course"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Course deleted successfully"})
}

// GetModulesByCourseID handles GET /courses/:id/modules
func (h *CoursesHandler) GetModulesByCourseID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Get modules
	modules, err := h.coursesService.GetModulesByCourseID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get modules"})
		return
	}

	c.JSON(http.StatusOK, modules)
}

// GetModuleByID handles GET /courses/modules/:id
func (h *CoursesHandler) GetModuleByID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid module ID"})
		return
	}

	// Get module
	module, err := h.coursesService.GetModuleByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get module"})
		return
	}

	if module == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Module not found"})
		return
	}

	c.JSON(http.StatusOK, module)
}

// CreateModule handles POST /courses/:id/modules
func (h *CoursesHandler) CreateModule(c *gin.Context) {
	// Parse course ID
	courseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Parse request body
	var module models.Module
	if err := c.ShouldBindJSON(&module); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set course ID
	module.CourseID = uint(courseID)

	// Create module
	if err := h.coursesService.CreateModule(&module); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create module"})
		return
	}

	c.JSON(http.StatusCreated, module)
}

// UpdateModule handles PUT /courses/modules/:id
func (h *CoursesHandler) UpdateModule(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid module ID"})
		return
	}

	// Get existing module
	existingModule, err := h.coursesService.GetModuleByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get module"})
		return
	}

	if existingModule == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Module not found"})
		return
	}

	// Parse request body
	var module models.Module
	if err := c.ShouldBindJSON(&module); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID and course ID
	module.ID = uint(id)
	module.CourseID = existingModule.CourseID

	// Update module
	if err := h.coursesService.UpdateModule(&module); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update module"})
		return
	}

	c.JSON(http.StatusOK, module)
}

// DeleteModule handles DELETE /courses/modules/:id
func (h *CoursesHandler) DeleteModule(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid module ID"})
		return
	}

	// Delete module
	if err := h.coursesService.DeleteModule(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete module"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Module deleted successfully"})
}

// GetLessonsByModuleID handles GET /courses/modules/:id/lessons
func (h *CoursesHandler) GetLessonsByModuleID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid module ID"})
		return
	}

	// Get lessons
	lessons, err := h.coursesService.GetLessonsByModuleID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get lessons"})
		return
	}

	c.JSON(http.StatusOK, lessons)
}

// GetLessonByID handles GET /courses/lessons/:id
func (h *CoursesHandler) GetLessonByID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	// Get lesson
	lesson, err := h.coursesService.GetLessonByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get lesson"})
		return
	}

	if lesson == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Lesson not found"})
		return
	}

	c.JSON(http.StatusOK, lesson)
}

// CreateLesson handles POST /courses/modules/:id/lessons
func (h *CoursesHandler) CreateLesson(c *gin.Context) {
	// Parse module ID
	moduleID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid module ID"})
		return
	}

	// Parse request body
	var lesson models.Lesson
	if err := c.ShouldBindJSON(&lesson); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set module ID
	lesson.ModuleID = uint(moduleID)

	// Create lesson
	if err := h.coursesService.CreateLesson(&lesson); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create lesson"})
		return
	}

	c.JSON(http.StatusCreated, lesson)
}

// UpdateLesson handles PUT /courses/lessons/:id
func (h *CoursesHandler) UpdateLesson(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	// Get existing lesson
	existingLesson, err := h.coursesService.GetLessonByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get lesson"})
		return
	}

	if existingLesson == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Lesson not found"})
		return
	}

	// Parse request body
	var lesson models.Lesson
	if err := c.ShouldBindJSON(&lesson); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID and module ID
	lesson.ID = uint(id)
	lesson.ModuleID = existingLesson.ModuleID

	// Update lesson
	if err := h.coursesService.UpdateLesson(&lesson); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update lesson"})
		return
	}

	c.JSON(http.StatusOK, lesson)
}

// DeleteLesson handles DELETE /courses/lessons/:id
func (h *CoursesHandler) DeleteLesson(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	// Delete lesson
	if err := h.coursesService.DeleteLesson(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete lesson"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Lesson deleted successfully"})
}

// GetQuizByLessonID handles GET /courses/lessons/:id/quiz
func (h *CoursesHandler) GetQuizByLessonID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	// Get quiz
	quiz, err := h.coursesService.GetQuizByLessonID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get quiz"})
		return
	}

	if quiz == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	c.JSON(http.StatusOK, quiz)
}

// GetQuizByID handles GET /courses/quizzes/:id
func (h *CoursesHandler) GetQuizByID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	// Get quiz
	quiz, err := h.coursesService.GetQuizByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get quiz"})
		return
	}

	if quiz == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	c.JSON(http.StatusOK, quiz)
}

// CreateQuiz handles POST /courses/lessons/:id/quiz
func (h *CoursesHandler) CreateQuiz(c *gin.Context) {
	// Parse lesson ID
	lessonID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	// Parse request body
	var quiz models.Quiz
	if err := c.ShouldBindJSON(&quiz); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set lesson ID
	quiz.LessonID = uint(lessonID)

	// Create quiz
	if err := h.coursesService.CreateQuiz(&quiz); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quiz"})
		return
	}

	c.JSON(http.StatusCreated, quiz)
}

// UpdateQuiz handles PUT /courses/quizzes/:id
func (h *CoursesHandler) UpdateQuiz(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	// Get existing quiz
	existingQuiz, err := h.coursesService.GetQuizByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get quiz"})
		return
	}

	if existingQuiz == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Parse request body
	var quiz models.Quiz
	if err := c.ShouldBindJSON(&quiz); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID and lesson ID
	quiz.ID = uint(id)
	quiz.LessonID = existingQuiz.LessonID

	// Update quiz
	if err := h.coursesService.UpdateQuiz(&quiz); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update quiz"})
		return
	}

	c.JSON(http.StatusOK, quiz)
}

// DeleteQuiz handles DELETE /courses/quizzes/:id
func (h *CoursesHandler) DeleteQuiz(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	// Delete quiz
	if err := h.coursesService.DeleteQuiz(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete quiz"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Quiz deleted successfully"})
}

// GetQuestionsByQuizID handles GET /courses/quizzes/:id/questions
func (h *CoursesHandler) GetQuestionsByQuizID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	// Get questions
	questions, err := h.coursesService.GetQuestionsByQuizID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get questions"})
		return
	}

	c.JSON(http.StatusOK, questions)
}

// GetQuestionByID handles GET /courses/questions/:id
func (h *CoursesHandler) GetQuestionByID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid question ID"})
		return
	}

	// Get question
	question, err := h.coursesService.GetQuestionByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get question"})
		return
	}

	if question == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
		return
	}

	c.JSON(http.StatusOK, question)
}

// CreateQuestion handles POST /courses/quizzes/:id/questions
func (h *CoursesHandler) CreateQuestion(c *gin.Context) {
	// Parse quiz ID
	quizID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	// Parse request body
	var question models.Question
	if err := c.ShouldBindJSON(&question); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set quiz ID
	question.QuizID = uint(quizID)

	// Create question
	if err := h.coursesService.CreateQuestion(&question); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create question"})
		return
	}

	c.JSON(http.StatusCreated, question)
}

// UpdateQuestion handles PUT /courses/questions/:id
func (h *CoursesHandler) UpdateQuestion(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid question ID"})
		return
	}

	// Get existing question
	existingQuestion, err := h.coursesService.GetQuestionByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get question"})
		return
	}

	if existingQuestion == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
		return
	}

	// Parse request body
	var question models.Question
	if err := c.ShouldBindJSON(&question); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID and quiz ID
	question.ID = uint(id)
	question.QuizID = existingQuestion.QuizID

	// Update question
	if err := h.coursesService.UpdateQuestion(&question); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update question"})
		return
	}

	c.JSON(http.StatusOK, question)
}

// DeleteQuestion handles DELETE /courses/questions/:id
func (h *CoursesHandler) DeleteQuestion(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid question ID"})
		return
	}

	// Delete question
	if err := h.coursesService.DeleteQuestion(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete question"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Question deleted successfully"})
}

// GetAssignmentByLessonID handles GET /courses/lessons/:id/assignment
func (h *CoursesHandler) GetAssignmentByLessonID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	// Get assignment
	assignment, err := h.coursesService.GetAssignmentByLessonID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get assignment"})
		return
	}

	if assignment == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Assignment not found"})
		return
	}

	c.JSON(http.StatusOK, assignment)
}

// GetAssignmentByID handles GET /courses/assignments/:id
func (h *CoursesHandler) GetAssignmentByID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid assignment ID"})
		return
	}

	// Get assignment
	assignment, err := h.coursesService.GetAssignmentByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get assignment"})
		return
	}

	if assignment == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Assignment not found"})
		return
	}

	c.JSON(http.StatusOK, assignment)
}

// CreateAssignment handles POST /courses/lessons/:id/assignment
func (h *CoursesHandler) CreateAssignment(c *gin.Context) {
	// Parse lesson ID
	lessonID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	// Parse request body
	var assignment models.Assignment
	if err := c.ShouldBindJSON(&assignment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set lesson ID
	assignment.LessonID = uint(lessonID)

	// Create assignment
	if err := h.coursesService.CreateAssignment(&assignment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create assignment"})
		return
	}

	c.JSON(http.StatusCreated, assignment)
}

// UpdateAssignment handles PUT /courses/assignments/:id
func (h *CoursesHandler) UpdateAssignment(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid assignment ID"})
		return
	}

	// Get existing assignment
	existingAssignment, err := h.coursesService.GetAssignmentByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get assignment"})
		return
	}

	if existingAssignment == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Assignment not found"})
		return
	}

	// Parse request body
	var assignment models.Assignment
	if err := c.ShouldBindJSON(&assignment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID and lesson ID
	assignment.ID = uint(id)
	assignment.LessonID = existingAssignment.LessonID

	// Update assignment
	if err := h.coursesService.UpdateAssignment(&assignment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update assignment"})
		return
	}

	c.JSON(http.StatusOK, assignment)
}

// DeleteAssignment handles DELETE /courses/assignments/:id
func (h *CoursesHandler) DeleteAssignment(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid assignment ID"})
		return
	}

	// Delete assignment
	if err := h.coursesService.DeleteAssignment(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete assignment"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Assignment deleted successfully"})
}

// GetEnrollmentsByUserID handles GET /courses/enrollments/user/:userId
func (h *CoursesHandler) GetEnrollmentsByUserID(c *gin.Context) {
	// Parse user ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get enrollments
	enrollments, err := h.coursesService.GetEnrollmentsByUserID(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get enrollments"})
		return
	}

	c.JSON(http.StatusOK, enrollments)
}

// GetEnrollmentsByCourseID handles GET /courses/enrollments/course/:courseId
func (h *CoursesHandler) GetEnrollmentsByCourseID(c *gin.Context) {
	// Parse course ID
	courseID, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Get enrollments
	enrollments, err := h.coursesService.GetEnrollmentsByCourseID(uint(courseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get enrollments"})
		return
	}

	c.JSON(http.StatusOK, enrollments)
}

// GetEnrollment handles GET /courses/enrollments/user/:userId/course/:courseId
func (h *CoursesHandler) GetEnrollment(c *gin.Context) {
	// Parse user ID and course ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	courseID, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Get enrollment
	enrollment, err := h.coursesService.GetEnrollment(uint(userID), uint(courseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get enrollment"})
		return
	}

	if enrollment == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Enrollment not found"})
		return
	}

	c.JSON(http.StatusOK, enrollment)
}

// CreateEnrollment handles POST /courses/enrollments
func (h *CoursesHandler) CreateEnrollment(c *gin.Context) {
	// Parse request body
	var enrollment models.Enrollment
	if err := c.ShouldBindJSON(&enrollment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Create enrollment
	if err := h.coursesService.CreateEnrollment(&enrollment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create enrollment"})
		return
	}

	c.JSON(http.StatusCreated, enrollment)
}

// UpdateEnrollment handles PUT /courses/enrollments/:id
func (h *CoursesHandler) UpdateEnrollment(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enrollment ID"})
		return
	}

	// Parse request body
	var enrollment models.Enrollment
	if err := c.ShouldBindJSON(&enrollment); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID
	enrollment.ID = uint(id)

	// Update enrollment
	if err := h.coursesService.UpdateEnrollment(&enrollment); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update enrollment"})
		return
	}

	c.JSON(http.StatusOK, enrollment)
}

// DeleteEnrollment handles DELETE /courses/enrollments/:id
func (h *CoursesHandler) DeleteEnrollment(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enrollment ID"})
		return
	}

	// Delete enrollment
	if err := h.coursesService.DeleteEnrollment(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete enrollment"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Enrollment deleted successfully"})
}

// GetProgressByUserAndCourse handles GET /courses/progress/user/:userId/course/:courseId
func (h *CoursesHandler) GetProgressByUserAndCourse(c *gin.Context) {
	// Parse user ID and course ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	courseID, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Get progress
	progress, err := h.coursesService.GetProgressByUserAndCourse(uint(userID), uint(courseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get progress"})
		return
	}

	c.JSON(http.StatusOK, progress)
}

// GetProgressByUserAndLesson handles GET /courses/progress/user/:userId/lesson/:lessonId
func (h *CoursesHandler) GetProgressByUserAndLesson(c *gin.Context) {
	// Parse user ID and lesson ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	lessonID, err := strconv.Atoi(c.Param("lessonId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	// Get progress
	progress, err := h.coursesService.GetProgressByUserAndLesson(uint(userID), uint(lessonID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get progress"})
		return
	}

	if progress == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Progress not found"})
		return
	}

	c.JSON(http.StatusOK, progress)
}

// CreateProgress handles POST /courses/progress
func (h *CoursesHandler) CreateProgress(c *gin.Context) {
	// Parse request body
	var progress models.Progress
	if err := c.ShouldBindJSON(&progress); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Create progress
	if err := h.coursesService.CreateProgress(&progress); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create progress"})
		return
	}

	c.JSON(http.StatusCreated, progress)
}

// UpdateProgress handles PUT /courses/progress/:id
func (h *CoursesHandler) UpdateProgress(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid progress ID"})
		return
	}

	// Parse request body
	var progress models.Progress
	if err := c.ShouldBindJSON(&progress); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID
	progress.ID = uint(id)

	// Update progress
	if err := h.coursesService.UpdateProgress(&progress); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update progress"})
		return
	}

	c.JSON(http.StatusOK, progress)
}

// GetQuizAttemptsByUserAndQuiz handles GET /courses/quiz-attempts/user/:userId/quiz/:quizId
func (h *CoursesHandler) GetQuizAttemptsByUserAndQuiz(c *gin.Context) {
	// Parse user ID and quiz ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	quizID, err := strconv.Atoi(c.Param("quizId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	// Get quiz attempts
	attempts, err := h.coursesService.GetQuizAttemptsByUserAndQuiz(uint(userID), uint(quizID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get quiz attempts"})
		return
	}

	c.JSON(http.StatusOK, attempts)
}

// GetQuizAttemptByID handles GET /courses/quiz-attempts/:id
func (h *CoursesHandler) GetQuizAttemptByID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz attempt ID"})
		return
	}

	// Get quiz attempt
	attempt, err := h.coursesService.GetQuizAttemptByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get quiz attempt"})
		return
	}

	if attempt == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz attempt not found"})
		return
	}

	c.JSON(http.StatusOK, attempt)
}

// CreateQuizAttempt handles POST /courses/quiz-attempts
func (h *CoursesHandler) CreateQuizAttempt(c *gin.Context) {
	// Parse request body
	var attempt models.QuizAttempt
	if err := c.ShouldBindJSON(&attempt); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Create quiz attempt
	if err := h.coursesService.CreateQuizAttempt(&attempt); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quiz attempt"})
		return
	}

	c.JSON(http.StatusCreated, attempt)
}

// UpdateQuizAttempt handles PUT /courses/quiz-attempts/:id
func (h *CoursesHandler) UpdateQuizAttempt(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz attempt ID"})
		return
	}

	// Parse request body
	var attempt models.QuizAttempt
	if err := c.ShouldBindJSON(&attempt); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID
	attempt.ID = uint(id)

	// Update quiz attempt
	if err := h.coursesService.UpdateQuizAttempt(&attempt); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update quiz attempt"})
		return
	}

	c.JSON(http.StatusOK, attempt)
}

// GetSubmissionsByUserAndAssignment handles GET /courses/submissions/user/:userId/assignment/:assignmentId
func (h *CoursesHandler) GetSubmissionsByUserAndAssignment(c *gin.Context) {
	// Parse user ID and assignment ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	assignmentID, err := strconv.Atoi(c.Param("assignmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid assignment ID"})
		return
	}

	// Get submissions
	submissions, err := h.coursesService.GetSubmissionsByUserAndAssignment(uint(userID), uint(assignmentID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get submissions"})
		return
	}

	c.JSON(http.StatusOK, submissions)
}

// GetSubmissionByID handles GET /courses/submissions/:id
func (h *CoursesHandler) GetSubmissionByID(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid submission ID"})
		return
	}

	// Get submission
	submission, err := h.coursesService.GetSubmissionByID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get submission"})
		return
	}

	if submission == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Submission not found"})
		return
	}

	c.JSON(http.StatusOK, submission)
}

// CreateSubmission handles POST /courses/submissions
func (h *CoursesHandler) CreateSubmission(c *gin.Context) {
	// Parse request body
	var submission models.AssignmentSubmission
	if err := c.ShouldBindJSON(&submission); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Create submission
	if err := h.coursesService.CreateSubmission(&submission); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create submission"})
		return
	}

	c.JSON(http.StatusCreated, submission)
}

// UpdateSubmission handles PUT /courses/submissions/:id
func (h *CoursesHandler) UpdateSubmission(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid submission ID"})
		return
	}

	// Parse request body
	var submission models.AssignmentSubmission
	if err := c.ShouldBindJSON(&submission); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID
	submission.ID = uint(id)

	// Update submission
	if err := h.coursesService.UpdateSubmission(&submission); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update submission"})
		return
	}

	c.JSON(http.StatusOK, submission)
}

// GetReviewsByCourseID handles GET /courses/reviews/course/:courseId
func (h *CoursesHandler) GetReviewsByCourseID(c *gin.Context) {
	// Parse course ID
	courseID, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Get reviews
	reviews, err := h.coursesService.GetReviewsByCourseID(uint(courseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get reviews"})
		return
	}

	c.JSON(http.StatusOK, reviews)
}

// GetReviewByUserAndCourse handles GET /courses/reviews/user/:userId/course/:courseId
func (h *CoursesHandler) GetReviewByUserAndCourse(c *gin.Context) {
	// Parse user ID and course ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	courseID, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Get review
	review, err := h.coursesService.GetReviewByUserAndCourse(uint(userID), uint(courseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get review"})
		return
	}

	if review == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Review not found"})
		return
	}

	c.JSON(http.StatusOK, review)
}

// CreateReview handles POST /courses/reviews
func (h *CoursesHandler) CreateReview(c *gin.Context) {
	// Parse request body
	var review models.Review
	if err := c.ShouldBindJSON(&review); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Create review
	if err := h.coursesService.CreateReview(&review); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create review"})
		return
	}

	c.JSON(http.StatusCreated, review)
}

// UpdateReview handles PUT /courses/reviews/:id
func (h *CoursesHandler) UpdateReview(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	// Parse request body
	var review models.Review
	if err := c.ShouldBindJSON(&review); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Set ID
	review.ID = uint(id)

	// Update review
	if err := h.coursesService.UpdateReview(&review); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update review"})
		return
	}

	c.JSON(http.StatusOK, review)
}

// DeleteReview handles DELETE /courses/reviews/:id
func (h *CoursesHandler) DeleteReview(c *gin.Context) {
	// Parse ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	// Delete review
	if err := h.coursesService.DeleteReview(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete review"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review deleted successfully"})
}

// GetCertificatesByUserID handles GET /courses/certificates/user/:userId
func (h *CoursesHandler) GetCertificatesByUserID(c *gin.Context) {
	// Parse user ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get certificates
	certificates, err := h.coursesService.GetCertificatesByUserID(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get certificates"})
		return
	}

	c.JSON(http.StatusOK, certificates)
}

// GetCertificateByUserAndCourse handles GET /courses/certificates/user/:userId/course/:courseId
func (h *CoursesHandler) GetCertificateByUserAndCourse(c *gin.Context) {
	// Parse user ID and course ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	courseID, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Get certificate
	certificate, err := h.coursesService.GetCertificateByUserAndCourse(uint(userID), uint(courseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get certificate"})
		return
	}

	if certificate == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Certificate not found"})
		return
	}

	c.JSON(http.StatusOK, certificate)
}

// CreateCertificate handles POST /courses/certificates
func (h *CoursesHandler) CreateCertificate(c *gin.Context) {
	// Parse request body
	var certificate models.Certificate
	if err := c.ShouldBindJSON(&certificate); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Create certificate
	if err := h.coursesService.CreateCertificate(&certificate); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create certificate"})
		return
	}

	c.JSON(http.StatusCreated, certificate)
}

// CheckCourseCompletion handles GET /courses/completion/user/:userId/course/:courseId
func (h *CoursesHandler) CheckCourseCompletion(c *gin.Context) {
	// Parse user ID and course ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	courseID, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Check course completion
	completed, err := h.coursesService.CheckCourseCompletion(uint(userID), uint(courseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check course completion"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"completed": completed})
}

// MarkCourseAsCompleted handles POST /courses/completion/user/:userId/course/:courseId
func (h *CoursesHandler) MarkCourseAsCompleted(c *gin.Context) {
	// Parse user ID and course ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	courseID, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Mark course as completed
	if err := h.coursesService.MarkCourseAsCompleted(uint(userID), uint(courseID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark course as completed"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Course marked as completed successfully"})
}

// GenerateCourseCompletionCertificate handles POST /courses/certificates/generate/user/:userId/course/:courseId
func (h *CoursesHandler) GenerateCourseCompletionCertificate(c *gin.Context) {
	// Parse user ID and course ID
	userID, err := strconv.Atoi(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	courseID, err := strconv.Atoi(c.Param("courseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Generate certificate
	certificate, err := h.coursesService.GenerateCourseCompletionCertificate(uint(userID), uint(courseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate certificate"})
		return
	}

	c.JSON(http.StatusCreated, certificate)
}
