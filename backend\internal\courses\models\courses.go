package models

import (
	"time"
)

// CourseStatus represents the status of a course
type CourseStatus string

const (
	CourseStatusDraft     CourseStatus = "draft"
	CourseStatusPublished CourseStatus = "published"
	CourseStatusArchived  CourseStatus = "archived"
)

// Course represents a course in the system
type Course struct {
	ID               uint         `json:"id" gorm:"primaryKey"`
	Title            string       `json:"title" gorm:"not null"`
	Slug             string       `json:"slug" gorm:"uniqueIndex;not null"`
	Description      string       `json:"description" gorm:"type:text"`
	ShortDescription string       `json:"shortDescription" gorm:"size:255"`
	CoverImage       string       `json:"coverImage"`
	InstructorID     uint         `json:"instructorId" gorm:"not null"`
	Status           CourseStatus `json:"status" gorm:"type:varchar(20);default:'draft'"`
	Price            float64      `json:"price" gorm:"default:0"`
	IsFree           bool         `json:"isFree" gorm:"default:true"`
	IsPublic         bool         `json:"isPublic" gorm:"default:true"`
	IsFeatured       bool         `json:"isFeatured" gorm:"default:false"`
	EnrollmentCount  int          `json:"enrollmentCount" gorm:"default:0"`
	Rating           float64      `json:"rating" gorm:"default:0"`
	RatingCount      int          `json:"ratingCount" gorm:"default:0"`
	Duration         int          `json:"duration" gorm:"default:0"` // In minutes
	Level            string       `json:"level" gorm:"default:'beginner'"`
	Prerequisites    string       `json:"prerequisites" gorm:"type:text"`
	LearningOutcomes string       `json:"learningOutcomes" gorm:"type:text"`
	Tags             string       `json:"tags" gorm:"type:text"`
	CreatedAt        time.Time    `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt        time.Time    `json:"updatedAt" gorm:"autoUpdateTime"`
	PublishedAt      *time.Time   `json:"publishedAt"`
}

// Module represents a module within a course
type Module struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	CourseID    uint      `json:"courseId" gorm:"not null;index"`
	Title       string    `json:"title" gorm:"not null"`
	Description string    `json:"description" gorm:"type:text"`
	Order       int       `json:"order" gorm:"not null"`
	IsPublished bool      `json:"isPublished" gorm:"default:false"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// LessonType represents the type of a lesson
type LessonType string

const (
	LessonTypeVideo    LessonType = "video"
	LessonTypeText     LessonType = "text"
	LessonTypeQuiz     LessonType = "quiz"
	LessonTypeAssignment LessonType = "assignment"
)

// Lesson represents a lesson within a module
type Lesson struct {
	ID          uint       `json:"id" gorm:"primaryKey"`
	ModuleID    uint       `json:"moduleId" gorm:"not null;index"`
	Title       string     `json:"title" gorm:"not null"`
	Description string     `json:"description" gorm:"type:text"`
	Type        LessonType `json:"type" gorm:"type:varchar(20);not null"`
	Content     string     `json:"content" gorm:"type:text"`
	VideoURL    string     `json:"videoUrl"`
	Duration    int        `json:"duration" gorm:"default:0"` // In minutes
	Order       int        `json:"order" gorm:"not null"`
	IsPublished bool       `json:"isPublished" gorm:"default:false"`
	CreatedAt   time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`
}

// Quiz represents a quiz within a lesson
type Quiz struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	LessonID    uint      `json:"lessonId" gorm:"not null;index"`
	Title       string    `json:"title" gorm:"not null"`
	Description string    `json:"description" gorm:"type:text"`
	PassScore   int       `json:"passScore" gorm:"default:70"` // Percentage
	TimeLimit   int       `json:"timeLimit" gorm:"default:0"`  // In minutes, 0 means no limit
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// QuestionType represents the type of a question
type QuestionType string

const (
	QuestionTypeMultipleChoice QuestionType = "multiple_choice"
	QuestionTypeTrueFalse      QuestionType = "true_false"
	QuestionTypeShortAnswer    QuestionType = "short_answer"
	QuestionTypeEssay          QuestionType = "essay"
)

// Question represents a question within a quiz
type Question struct {
	ID          uint         `json:"id" gorm:"primaryKey"`
	QuizID      uint         `json:"quizId" gorm:"not null;index"`
	Type        QuestionType `json:"type" gorm:"type:varchar(20);not null"`
	Question    string       `json:"question" gorm:"type:text;not null"`
	Options     string       `json:"options" gorm:"type:text"` // JSON array of options
	Answer      string       `json:"answer" gorm:"type:text"`  // For multiple choice, the index of the correct option; for true/false, "true" or "false"; for short answer and essay, the expected answer
	Explanation string       `json:"explanation" gorm:"type:text"`
	Points      int          `json:"points" gorm:"default:1"`
	Order       int          `json:"order" gorm:"not null"`
	CreatedAt   time.Time    `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time    `json:"updatedAt" gorm:"autoUpdateTime"`
}

// Assignment represents an assignment within a lesson
type Assignment struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	LessonID    uint      `json:"lessonId" gorm:"not null;index"`
	Title       string    `json:"title" gorm:"not null"`
	Description string    `json:"description" gorm:"type:text"`
	Instructions string   `json:"instructions" gorm:"type:text"`
	DueDate     time.Time `json:"dueDate"`
	Points      int       `json:"points" gorm:"default:100"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// Enrollment represents a user's enrollment in a course
type Enrollment struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	UserID         uint      `json:"userId" gorm:"not null;index"`
	CourseID       uint      `json:"courseId" gorm:"not null;index"`
	EnrollmentDate time.Time `json:"enrollmentDate" gorm:"autoCreateTime"`
	CompletionDate *time.Time `json:"completionDate"`
	IsCompleted    bool      `json:"isCompleted" gorm:"default:false"`
	Progress       float64   `json:"progress" gorm:"default:0"` // Percentage
	LastAccessedAt time.Time `json:"lastAccessedAt" gorm:"autoCreateTime"`
}

// Progress represents a user's progress in a course
type Progress struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"userId" gorm:"not null;index"`
	CourseID  uint      `json:"courseId" gorm:"not null;index"`
	ModuleID  uint      `json:"moduleId" gorm:"not null;index"`
	LessonID  uint      `json:"lessonId" gorm:"not null;index"`
	Completed bool      `json:"completed" gorm:"default:false"`
	CompletedAt *time.Time `json:"completedAt"`
	CreatedAt time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// QuizAttempt represents a user's attempt at a quiz
type QuizAttempt struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"userId" gorm:"not null;index"`
	QuizID    uint      `json:"quizId" gorm:"not null;index"`
	Score     float64   `json:"score" gorm:"default:0"` // Percentage
	Passed    bool      `json:"passed" gorm:"default:false"`
	StartedAt time.Time `json:"startedAt" gorm:"autoCreateTime"`
	FinishedAt *time.Time `json:"finishedAt"`
	Answers   string    `json:"answers" gorm:"type:text"` // JSON array of answers
}

// AssignmentSubmission represents a user's submission for an assignment
type AssignmentSubmission struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	UserID       uint      `json:"userId" gorm:"not null;index"`
	AssignmentID uint      `json:"assignmentId" gorm:"not null;index"`
	Content      string    `json:"content" gorm:"type:text"`
	FileURL      string    `json:"fileUrl"`
	Grade        float64   `json:"grade"`
	Feedback     string    `json:"feedback" gorm:"type:text"`
	IsGraded     bool      `json:"isGraded" gorm:"default:false"`
	SubmittedAt  time.Time `json:"submittedAt" gorm:"autoCreateTime"`
	GradedAt     *time.Time `json:"gradedAt"`
}

// Review represents a user's review of a course
type Review struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"userId" gorm:"not null;index"`
	CourseID  uint      `json:"courseId" gorm:"not null;index"`
	Rating    int       `json:"rating" gorm:"not null"`
	Comment   string    `json:"comment" gorm:"type:text"`
	CreatedAt time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// Certificate represents a certificate issued to a user for completing a course
type Certificate struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"userId" gorm:"not null;index"`
	CourseID  uint      `json:"courseId" gorm:"not null;index"`
	CertificateURL string `json:"certificateUrl"`
	IssuedAt  time.Time `json:"issuedAt" gorm:"autoCreateTime"`
}
