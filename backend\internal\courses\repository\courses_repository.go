package repository

import (
	"errors"
	"time"

	"github.com/greatnigeria/internal/courses/models"
	"gorm.io/gorm"
)

// CoursesRepository defines the interface for courses data access
type CoursesRepository interface {
	// Course methods
	GetCourses(page, pageSize int, filters map[string]interface{}) ([]models.Course, int64, error)
	GetCourseByID(id uint) (*models.Course, error)
	GetCourseBySlug(slug string) (*models.Course, error)
	CreateCourse(course *models.Course) error
	UpdateCourse(course *models.Course) error
	DeleteCourse(id uint) error
	
	// Module methods
	GetModulesByCourseID(courseID uint) ([]models.Module, error)
	GetModuleByID(id uint) (*models.Module, error)
	CreateModule(module *models.Module) error
	UpdateModule(module *models.Module) error
	DeleteModule(id uint) error
	
	// Lesson methods
	GetLessonsByModuleID(moduleID uint) ([]models.Lesson, error)
	GetLessonByID(id uint) (*models.Lesson, error)
	CreateLesson(lesson *models.Lesson) error
	UpdateLesson(lesson *models.Lesson) error
	DeleteLesson(id uint) error
	
	// Quiz methods
	GetQuizByLessonID(lessonID uint) (*models.Quiz, error)
	GetQuizByID(id uint) (*models.Quiz, error)
	CreateQuiz(quiz *models.Quiz) error
	UpdateQuiz(quiz *models.Quiz) error
	DeleteQuiz(id uint) error
	
	// Question methods
	GetQuestionsByQuizID(quizID uint) ([]models.Question, error)
	GetQuestionByID(id uint) (*models.Question, error)
	CreateQuestion(question *models.Question) error
	UpdateQuestion(question *models.Question) error
	DeleteQuestion(id uint) error
	
	// Assignment methods
	GetAssignmentByLessonID(lessonID uint) (*models.Assignment, error)
	GetAssignmentByID(id uint) (*models.Assignment, error)
	CreateAssignment(assignment *models.Assignment) error
	UpdateAssignment(assignment *models.Assignment) error
	DeleteAssignment(id uint) error
	
	// Enrollment methods
	GetEnrollmentsByUserID(userID uint) ([]models.Enrollment, error)
	GetEnrollmentsByCourseID(courseID uint) ([]models.Enrollment, error)
	GetEnrollment(userID, courseID uint) (*models.Enrollment, error)
	CreateEnrollment(enrollment *models.Enrollment) error
	UpdateEnrollment(enrollment *models.Enrollment) error
	DeleteEnrollment(id uint) error
	
	// Progress methods
	GetProgressByUserAndCourse(userID, courseID uint) ([]models.Progress, error)
	GetProgressByUserAndLesson(userID, lessonID uint) (*models.Progress, error)
	CreateProgress(progress *models.Progress) error
	UpdateProgress(progress *models.Progress) error
	
	// Quiz attempt methods
	GetQuizAttemptsByUserAndQuiz(userID, quizID uint) ([]models.QuizAttempt, error)
	GetQuizAttemptByID(id uint) (*models.QuizAttempt, error)
	CreateQuizAttempt(attempt *models.QuizAttempt) error
	UpdateQuizAttempt(attempt *models.QuizAttempt) error
	
	// Assignment submission methods
	GetSubmissionsByUserAndAssignment(userID, assignmentID uint) ([]models.AssignmentSubmission, error)
	GetSubmissionByID(id uint) (*models.AssignmentSubmission, error)
	CreateSubmission(submission *models.AssignmentSubmission) error
	UpdateSubmission(submission *models.AssignmentSubmission) error
	
	// Review methods
	GetReviewsByCourseID(courseID uint) ([]models.Review, error)
	GetReviewByUserAndCourse(userID, courseID uint) (*models.Review, error)
	CreateReview(review *models.Review) error
	UpdateReview(review *models.Review) error
	DeleteReview(id uint) error
	
	// Certificate methods
	GetCertificatesByUserID(userID uint) ([]models.Certificate, error)
	GetCertificateByUserAndCourse(userID, courseID uint) (*models.Certificate, error)
	CreateCertificate(certificate *models.Certificate) error
}

// DefaultCoursesRepository implements CoursesRepository
type DefaultCoursesRepository struct {
	db *gorm.DB
}

// NewCoursesRepository creates a new courses repository
func NewCoursesRepository(db *gorm.DB) CoursesRepository {
	return &DefaultCoursesRepository{
		db: db,
	}
}

// GetCourses retrieves courses with pagination and filters
func (r *DefaultCoursesRepository) GetCourses(page, pageSize int, filters map[string]interface{}) ([]models.Course, int64, error) {
	var courses []models.Course
	var total int64
	
	query := r.db
	
	// Apply filters
	for key, value := range filters {
		query = query.Where(key, value)
	}
	
	// Count total records
	if err := query.Model(&models.Course{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// Apply pagination
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&courses).Error; err != nil {
		return nil, 0, err
	}
	
	return courses, total, nil
}

// GetCourseByID retrieves a course by ID
func (r *DefaultCoursesRepository) GetCourseByID(id uint) (*models.Course, error) {
	var course models.Course
	if err := r.db.First(&course, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &course, nil
}

// GetCourseBySlug retrieves a course by slug
func (r *DefaultCoursesRepository) GetCourseBySlug(slug string) (*models.Course, error) {
	var course models.Course
	if err := r.db.Where("slug = ?", slug).First(&course).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &course, nil
}

// CreateCourse creates a new course
func (r *DefaultCoursesRepository) CreateCourse(course *models.Course) error {
	return r.db.Create(course).Error
}

// UpdateCourse updates an existing course
func (r *DefaultCoursesRepository) UpdateCourse(course *models.Course) error {
	// If the course is being published, set the published date
	if course.Status == models.CourseStatusPublished && course.PublishedAt == nil {
		now := time.Now()
		course.PublishedAt = &now
	}
	
	return r.db.Save(course).Error
}

// DeleteCourse deletes a course
func (r *DefaultCoursesRepository) DeleteCourse(id uint) error {
	return r.db.Delete(&models.Course{}, id).Error
}

// GetModulesByCourseID retrieves modules for a course
func (r *DefaultCoursesRepository) GetModulesByCourseID(courseID uint) ([]models.Module, error) {
	var modules []models.Module
	if err := r.db.Where("course_id = ?", courseID).Order("order").Find(&modules).Error; err != nil {
		return nil, err
	}
	return modules, nil
}

// GetModuleByID retrieves a module by ID
func (r *DefaultCoursesRepository) GetModuleByID(id uint) (*models.Module, error) {
	var module models.Module
	if err := r.db.First(&module, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &module, nil
}

// CreateModule creates a new module
func (r *DefaultCoursesRepository) CreateModule(module *models.Module) error {
	return r.db.Create(module).Error
}

// UpdateModule updates an existing module
func (r *DefaultCoursesRepository) UpdateModule(module *models.Module) error {
	return r.db.Save(module).Error
}

// DeleteModule deletes a module
func (r *DefaultCoursesRepository) DeleteModule(id uint) error {
	return r.db.Delete(&models.Module{}, id).Error
}

// GetLessonsByModuleID retrieves lessons for a module
func (r *DefaultCoursesRepository) GetLessonsByModuleID(moduleID uint) ([]models.Lesson, error) {
	var lessons []models.Lesson
	if err := r.db.Where("module_id = ?", moduleID).Order("order").Find(&lessons).Error; err != nil {
		return nil, err
	}
	return lessons, nil
}

// GetLessonByID retrieves a lesson by ID
func (r *DefaultCoursesRepository) GetLessonByID(id uint) (*models.Lesson, error) {
	var lesson models.Lesson
	if err := r.db.First(&lesson, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &lesson, nil
}

// CreateLesson creates a new lesson
func (r *DefaultCoursesRepository) CreateLesson(lesson *models.Lesson) error {
	return r.db.Create(lesson).Error
}

// UpdateLesson updates an existing lesson
func (r *DefaultCoursesRepository) UpdateLesson(lesson *models.Lesson) error {
	return r.db.Save(lesson).Error
}

// DeleteLesson deletes a lesson
func (r *DefaultCoursesRepository) DeleteLesson(id uint) error {
	return r.db.Delete(&models.Lesson{}, id).Error
}

// GetQuizByLessonID retrieves a quiz for a lesson
func (r *DefaultCoursesRepository) GetQuizByLessonID(lessonID uint) (*models.Quiz, error) {
	var quiz models.Quiz
	if err := r.db.Where("lesson_id = ?", lessonID).First(&quiz).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &quiz, nil
}

// GetQuizByID retrieves a quiz by ID
func (r *DefaultCoursesRepository) GetQuizByID(id uint) (*models.Quiz, error) {
	var quiz models.Quiz
	if err := r.db.First(&quiz, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &quiz, nil
}

// CreateQuiz creates a new quiz
func (r *DefaultCoursesRepository) CreateQuiz(quiz *models.Quiz) error {
	return r.db.Create(quiz).Error
}

// UpdateQuiz updates an existing quiz
func (r *DefaultCoursesRepository) UpdateQuiz(quiz *models.Quiz) error {
	return r.db.Save(quiz).Error
}

// DeleteQuiz deletes a quiz
func (r *DefaultCoursesRepository) DeleteQuiz(id uint) error {
	return r.db.Delete(&models.Quiz{}, id).Error
}

// GetQuestionsByQuizID retrieves questions for a quiz
func (r *DefaultCoursesRepository) GetQuestionsByQuizID(quizID uint) ([]models.Question, error) {
	var questions []models.Question
	if err := r.db.Where("quiz_id = ?", quizID).Order("order").Find(&questions).Error; err != nil {
		return nil, err
	}
	return questions, nil
}

// GetQuestionByID retrieves a question by ID
func (r *DefaultCoursesRepository) GetQuestionByID(id uint) (*models.Question, error) {
	var question models.Question
	if err := r.db.First(&question, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &question, nil
}

// CreateQuestion creates a new question
func (r *DefaultCoursesRepository) CreateQuestion(question *models.Question) error {
	return r.db.Create(question).Error
}

// UpdateQuestion updates an existing question
func (r *DefaultCoursesRepository) UpdateQuestion(question *models.Question) error {
	return r.db.Save(question).Error
}

// DeleteQuestion deletes a question
func (r *DefaultCoursesRepository) DeleteQuestion(id uint) error {
	return r.db.Delete(&models.Question{}, id).Error
}

// GetAssignmentByLessonID retrieves an assignment for a lesson
func (r *DefaultCoursesRepository) GetAssignmentByLessonID(lessonID uint) (*models.Assignment, error) {
	var assignment models.Assignment
	if err := r.db.Where("lesson_id = ?", lessonID).First(&assignment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &assignment, nil
}

// GetAssignmentByID retrieves an assignment by ID
func (r *DefaultCoursesRepository) GetAssignmentByID(id uint) (*models.Assignment, error) {
	var assignment models.Assignment
	if err := r.db.First(&assignment, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &assignment, nil
}

// CreateAssignment creates a new assignment
func (r *DefaultCoursesRepository) CreateAssignment(assignment *models.Assignment) error {
	return r.db.Create(assignment).Error
}

// UpdateAssignment updates an existing assignment
func (r *DefaultCoursesRepository) UpdateAssignment(assignment *models.Assignment) error {
	return r.db.Save(assignment).Error
}

// DeleteAssignment deletes an assignment
func (r *DefaultCoursesRepository) DeleteAssignment(id uint) error {
	return r.db.Delete(&models.Assignment{}, id).Error
}

// GetEnrollmentsByUserID retrieves enrollments for a user
func (r *DefaultCoursesRepository) GetEnrollmentsByUserID(userID uint) ([]models.Enrollment, error) {
	var enrollments []models.Enrollment
	if err := r.db.Where("user_id = ?", userID).Find(&enrollments).Error; err != nil {
		return nil, err
	}
	return enrollments, nil
}

// GetEnrollmentsByCourseID retrieves enrollments for a course
func (r *DefaultCoursesRepository) GetEnrollmentsByCourseID(courseID uint) ([]models.Enrollment, error) {
	var enrollments []models.Enrollment
	if err := r.db.Where("course_id = ?", courseID).Find(&enrollments).Error; err != nil {
		return nil, err
	}
	return enrollments, nil
}

// GetEnrollment retrieves an enrollment for a user and course
func (r *DefaultCoursesRepository) GetEnrollment(userID, courseID uint) (*models.Enrollment, error) {
	var enrollment models.Enrollment
	if err := r.db.Where("user_id = ? AND course_id = ?", userID, courseID).First(&enrollment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &enrollment, nil
}

// CreateEnrollment creates a new enrollment
func (r *DefaultCoursesRepository) CreateEnrollment(enrollment *models.Enrollment) error {
	return r.db.Create(enrollment).Error
}

// UpdateEnrollment updates an existing enrollment
func (r *DefaultCoursesRepository) UpdateEnrollment(enrollment *models.Enrollment) error {
	return r.db.Save(enrollment).Error
}

// DeleteEnrollment deletes an enrollment
func (r *DefaultCoursesRepository) DeleteEnrollment(id uint) error {
	return r.db.Delete(&models.Enrollment{}, id).Error
}

// GetProgressByUserAndCourse retrieves progress for a user and course
func (r *DefaultCoursesRepository) GetProgressByUserAndCourse(userID, courseID uint) ([]models.Progress, error) {
	var progress []models.Progress
	if err := r.db.Where("user_id = ? AND course_id = ?", userID, courseID).Find(&progress).Error; err != nil {
		return nil, err
	}
	return progress, nil
}

// GetProgressByUserAndLesson retrieves progress for a user and lesson
func (r *DefaultCoursesRepository) GetProgressByUserAndLesson(userID, lessonID uint) (*models.Progress, error) {
	var progress models.Progress
	if err := r.db.Where("user_id = ? AND lesson_id = ?", userID, lessonID).First(&progress).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &progress, nil
}

// CreateProgress creates a new progress record
func (r *DefaultCoursesRepository) CreateProgress(progress *models.Progress) error {
	return r.db.Create(progress).Error
}

// UpdateProgress updates an existing progress record
func (r *DefaultCoursesRepository) UpdateProgress(progress *models.Progress) error {
	return r.db.Save(progress).Error
}

// GetQuizAttemptsByUserAndQuiz retrieves quiz attempts for a user and quiz
func (r *DefaultCoursesRepository) GetQuizAttemptsByUserAndQuiz(userID, quizID uint) ([]models.QuizAttempt, error) {
	var attempts []models.QuizAttempt
	if err := r.db.Where("user_id = ? AND quiz_id = ?", userID, quizID).Find(&attempts).Error; err != nil {
		return nil, err
	}
	return attempts, nil
}

// GetQuizAttemptByID retrieves a quiz attempt by ID
func (r *DefaultCoursesRepository) GetQuizAttemptByID(id uint) (*models.QuizAttempt, error) {
	var attempt models.QuizAttempt
	if err := r.db.First(&attempt, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &attempt, nil
}

// CreateQuizAttempt creates a new quiz attempt
func (r *DefaultCoursesRepository) CreateQuizAttempt(attempt *models.QuizAttempt) error {
	return r.db.Create(attempt).Error
}

// UpdateQuizAttempt updates an existing quiz attempt
func (r *DefaultCoursesRepository) UpdateQuizAttempt(attempt *models.QuizAttempt) error {
	return r.db.Save(attempt).Error
}

// GetSubmissionsByUserAndAssignment retrieves submissions for a user and assignment
func (r *DefaultCoursesRepository) GetSubmissionsByUserAndAssignment(userID, assignmentID uint) ([]models.AssignmentSubmission, error) {
	var submissions []models.AssignmentSubmission
	if err := r.db.Where("user_id = ? AND assignment_id = ?", userID, assignmentID).Find(&submissions).Error; err != nil {
		return nil, err
	}
	return submissions, nil
}

// GetSubmissionByID retrieves a submission by ID
func (r *DefaultCoursesRepository) GetSubmissionByID(id uint) (*models.AssignmentSubmission, error) {
	var submission models.AssignmentSubmission
	if err := r.db.First(&submission, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &submission, nil
}

// CreateSubmission creates a new submission
func (r *DefaultCoursesRepository) CreateSubmission(submission *models.AssignmentSubmission) error {
	return r.db.Create(submission).Error
}

// UpdateSubmission updates an existing submission
func (r *DefaultCoursesRepository) UpdateSubmission(submission *models.AssignmentSubmission) error {
	return r.db.Save(submission).Error
}

// GetReviewsByCourseID retrieves reviews for a course
func (r *DefaultCoursesRepository) GetReviewsByCourseID(courseID uint) ([]models.Review, error) {
	var reviews []models.Review
	if err := r.db.Where("course_id = ?", courseID).Find(&reviews).Error; err != nil {
		return nil, err
	}
	return reviews, nil
}

// GetReviewByUserAndCourse retrieves a review for a user and course
func (r *DefaultCoursesRepository) GetReviewByUserAndCourse(userID, courseID uint) (*models.Review, error) {
	var review models.Review
	if err := r.db.Where("user_id = ? AND course_id = ?", userID, courseID).First(&review).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &review, nil
}

// CreateReview creates a new review
func (r *DefaultCoursesRepository) CreateReview(review *models.Review) error {
	return r.db.Create(review).Error
}

// UpdateReview updates an existing review
func (r *DefaultCoursesRepository) UpdateReview(review *models.Review) error {
	return r.db.Save(review).Error
}

// DeleteReview deletes a review
func (r *DefaultCoursesRepository) DeleteReview(id uint) error {
	return r.db.Delete(&models.Review{}, id).Error
}

// GetCertificatesByUserID retrieves certificates for a user
func (r *DefaultCoursesRepository) GetCertificatesByUserID(userID uint) ([]models.Certificate, error) {
	var certificates []models.Certificate
	if err := r.db.Where("user_id = ?", userID).Find(&certificates).Error; err != nil {
		return nil, err
	}
	return certificates, nil
}

// GetCertificateByUserAndCourse retrieves a certificate for a user and course
func (r *DefaultCoursesRepository) GetCertificateByUserAndCourse(userID, courseID uint) (*models.Certificate, error) {
	var certificate models.Certificate
	if err := r.db.Where("user_id = ? AND course_id = ?", userID, courseID).First(&certificate).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &certificate, nil
}

// CreateCertificate creates a new certificate
func (r *DefaultCoursesRepository) CreateCertificate(certificate *models.Certificate) error {
	return r.db.Create(certificate).Error
}
