package service

import (
	"fmt"
	"time"

	"github.com/greatnigeria/internal/courses/models"
	"github.com/greatnigeria/internal/courses/repository"
)

// CoursesService defines the interface for courses business logic
type CoursesService interface {
	// Course methods
	GetCourses(page, pageSize int, filters map[string]interface{}) ([]models.Course, int64, error)
	GetCourseByID(id uint) (*models.Course, error)
	GetCourseBySlug(slug string) (*models.Course, error)
	CreateCourse(course *models.Course) error
	UpdateCourse(course *models.Course) error
	DeleteCourse(id uint) error
	
	// Module methods
	GetModulesByCourseID(courseID uint) ([]models.Module, error)
	GetModuleByID(id uint) (*models.Module, error)
	CreateModule(module *models.Module) error
	UpdateModule(module *models.Module) error
	DeleteModule(id uint) error
	
	// Lesson methods
	GetLessonsByModuleID(moduleID uint) ([]models.Lesson, error)
	GetLessonByID(id uint) (*models.Lesson, error)
	CreateLesson(lesson *models.Lesson) error
	UpdateLesson(lesson *models.Lesson) error
	DeleteLesson(id uint) error
	
	// Quiz methods
	GetQuizByLessonID(lessonID uint) (*models.Quiz, error)
	GetQuizByID(id uint) (*models.Quiz, error)
	CreateQuiz(quiz *models.Quiz) error
	UpdateQuiz(quiz *models.Quiz) error
	DeleteQuiz(id uint) error
	
	// Question methods
	GetQuestionsByQuizID(quizID uint) ([]models.Question, error)
	GetQuestionByID(id uint) (*models.Question, error)
	CreateQuestion(question *models.Question) error
	UpdateQuestion(question *models.Question) error
	DeleteQuestion(id uint) error
	
	// Assignment methods
	GetAssignmentByLessonID(lessonID uint) (*models.Assignment, error)
	GetAssignmentByID(id uint) (*models.Assignment, error)
	CreateAssignment(assignment *models.Assignment) error
	UpdateAssignment(assignment *models.Assignment) error
	DeleteAssignment(id uint) error
	
	// Enrollment methods
	GetEnrollmentsByUserID(userID uint) ([]models.Enrollment, error)
	GetEnrollmentsByCourseID(courseID uint) ([]models.Enrollment, error)
	GetEnrollment(userID, courseID uint) (*models.Enrollment, error)
	CreateEnrollment(enrollment *models.Enrollment) error
	UpdateEnrollment(enrollment *models.Enrollment) error
	DeleteEnrollment(id uint) error
	
	// Progress methods
	GetProgressByUserAndCourse(userID, courseID uint) ([]models.Progress, error)
	GetProgressByUserAndLesson(userID, lessonID uint) (*models.Progress, error)
	CreateProgress(progress *models.Progress) error
	UpdateProgress(progress *models.Progress) error
	
	// Quiz attempt methods
	GetQuizAttemptsByUserAndQuiz(userID, quizID uint) ([]models.QuizAttempt, error)
	GetQuizAttemptByID(id uint) (*models.QuizAttempt, error)
	CreateQuizAttempt(attempt *models.QuizAttempt) error
	UpdateQuizAttempt(attempt *models.QuizAttempt) error
	
	// Assignment submission methods
	GetSubmissionsByUserAndAssignment(userID, assignmentID uint) ([]models.AssignmentSubmission, error)
	GetSubmissionByID(id uint) (*models.AssignmentSubmission, error)
	CreateSubmission(submission *models.AssignmentSubmission) error
	UpdateSubmission(submission *models.AssignmentSubmission) error
	
	// Review methods
	GetReviewsByCourseID(courseID uint) ([]models.Review, error)
	GetReviewByUserAndCourse(userID, courseID uint) (*models.Review, error)
	CreateReview(review *models.Review) error
	UpdateReview(review *models.Review) error
	DeleteReview(id uint) error
	
	// Certificate methods
	GetCertificatesByUserID(userID uint) ([]models.Certificate, error)
	GetCertificateByUserAndCourse(userID, courseID uint) (*models.Certificate, error)
	CreateCertificate(certificate *models.Certificate) error
	
	// Course completion methods
	CheckCourseCompletion(userID, courseID uint) (bool, error)
	MarkCourseAsCompleted(userID, courseID uint) error
	GenerateCourseCompletionCertificate(userID, courseID uint) (*models.Certificate, error)
}

// DefaultCoursesService implements CoursesService
type DefaultCoursesService struct {
	coursesRepo repository.CoursesRepository
}

// NewCoursesService creates a new courses service
func NewCoursesService(coursesRepo repository.CoursesRepository) CoursesService {
	return &DefaultCoursesService{
		coursesRepo: coursesRepo,
	}
}

// GetCourses retrieves courses with pagination and filters
func (s *DefaultCoursesService) GetCourses(page, pageSize int, filters map[string]interface{}) ([]models.Course, int64, error) {
	return s.coursesRepo.GetCourses(page, pageSize, filters)
}

// GetCourseByID retrieves a course by ID
func (s *DefaultCoursesService) GetCourseByID(id uint) (*models.Course, error) {
	return s.coursesRepo.GetCourseByID(id)
}

// GetCourseBySlug retrieves a course by slug
func (s *DefaultCoursesService) GetCourseBySlug(slug string) (*models.Course, error) {
	return s.coursesRepo.GetCourseBySlug(slug)
}

// CreateCourse creates a new course
func (s *DefaultCoursesService) CreateCourse(course *models.Course) error {
	return s.coursesRepo.CreateCourse(course)
}

// UpdateCourse updates an existing course
func (s *DefaultCoursesService) UpdateCourse(course *models.Course) error {
	return s.coursesRepo.UpdateCourse(course)
}

// DeleteCourse deletes a course
func (s *DefaultCoursesService) DeleteCourse(id uint) error {
	return s.coursesRepo.DeleteCourse(id)
}

// GetModulesByCourseID retrieves modules for a course
func (s *DefaultCoursesService) GetModulesByCourseID(courseID uint) ([]models.Module, error) {
	return s.coursesRepo.GetModulesByCourseID(courseID)
}

// GetModuleByID retrieves a module by ID
func (s *DefaultCoursesService) GetModuleByID(id uint) (*models.Module, error) {
	return s.coursesRepo.GetModuleByID(id)
}

// CreateModule creates a new module
func (s *DefaultCoursesService) CreateModule(module *models.Module) error {
	return s.coursesRepo.CreateModule(module)
}

// UpdateModule updates an existing module
func (s *DefaultCoursesService) UpdateModule(module *models.Module) error {
	return s.coursesRepo.UpdateModule(module)
}

// DeleteModule deletes a module
func (s *DefaultCoursesService) DeleteModule(id uint) error {
	return s.coursesRepo.DeleteModule(id)
}

// GetLessonsByModuleID retrieves lessons for a module
func (s *DefaultCoursesService) GetLessonsByModuleID(moduleID uint) ([]models.Lesson, error) {
	return s.coursesRepo.GetLessonsByModuleID(moduleID)
}

// GetLessonByID retrieves a lesson by ID
func (s *DefaultCoursesService) GetLessonByID(id uint) (*models.Lesson, error) {
	return s.coursesRepo.GetLessonByID(id)
}

// CreateLesson creates a new lesson
func (s *DefaultCoursesService) CreateLesson(lesson *models.Lesson) error {
	return s.coursesRepo.CreateLesson(lesson)
}

// UpdateLesson updates an existing lesson
func (s *DefaultCoursesService) UpdateLesson(lesson *models.Lesson) error {
	return s.coursesRepo.UpdateLesson(lesson)
}

// DeleteLesson deletes a lesson
func (s *DefaultCoursesService) DeleteLesson(id uint) error {
	return s.coursesRepo.DeleteLesson(id)
}

// GetQuizByLessonID retrieves a quiz for a lesson
func (s *DefaultCoursesService) GetQuizByLessonID(lessonID uint) (*models.Quiz, error) {
	return s.coursesRepo.GetQuizByLessonID(lessonID)
}

// GetQuizByID retrieves a quiz by ID
func (s *DefaultCoursesService) GetQuizByID(id uint) (*models.Quiz, error) {
	return s.coursesRepo.GetQuizByID(id)
}

// CreateQuiz creates a new quiz
func (s *DefaultCoursesService) CreateQuiz(quiz *models.Quiz) error {
	return s.coursesRepo.CreateQuiz(quiz)
}

// UpdateQuiz updates an existing quiz
func (s *DefaultCoursesService) UpdateQuiz(quiz *models.Quiz) error {
	return s.coursesRepo.UpdateQuiz(quiz)
}

// DeleteQuiz deletes a quiz
func (s *DefaultCoursesService) DeleteQuiz(id uint) error {
	return s.coursesRepo.DeleteQuiz(id)
}

// GetQuestionsByQuizID retrieves questions for a quiz
func (s *DefaultCoursesService) GetQuestionsByQuizID(quizID uint) ([]models.Question, error) {
	return s.coursesRepo.GetQuestionsByQuizID(quizID)
}

// GetQuestionByID retrieves a question by ID
func (s *DefaultCoursesService) GetQuestionByID(id uint) (*models.Question, error) {
	return s.coursesRepo.GetQuestionByID(id)
}

// CreateQuestion creates a new question
func (s *DefaultCoursesService) CreateQuestion(question *models.Question) error {
	return s.coursesRepo.CreateQuestion(question)
}

// UpdateQuestion updates an existing question
func (s *DefaultCoursesService) UpdateQuestion(question *models.Question) error {
	return s.coursesRepo.UpdateQuestion(question)
}

// DeleteQuestion deletes a question
func (s *DefaultCoursesService) DeleteQuestion(id uint) error {
	return s.coursesRepo.DeleteQuestion(id)
}

// GetAssignmentByLessonID retrieves an assignment for a lesson
func (s *DefaultCoursesService) GetAssignmentByLessonID(lessonID uint) (*models.Assignment, error) {
	return s.coursesRepo.GetAssignmentByLessonID(lessonID)
}

// GetAssignmentByID retrieves an assignment by ID
func (s *DefaultCoursesService) GetAssignmentByID(id uint) (*models.Assignment, error) {
	return s.coursesRepo.GetAssignmentByID(id)
}

// CreateAssignment creates a new assignment
func (s *DefaultCoursesService) CreateAssignment(assignment *models.Assignment) error {
	return s.coursesRepo.CreateAssignment(assignment)
}

// UpdateAssignment updates an existing assignment
func (s *DefaultCoursesService) UpdateAssignment(assignment *models.Assignment) error {
	return s.coursesRepo.UpdateAssignment(assignment)
}

// DeleteAssignment deletes an assignment
func (s *DefaultCoursesService) DeleteAssignment(id uint) error {
	return s.coursesRepo.DeleteAssignment(id)
}

// GetEnrollmentsByUserID retrieves enrollments for a user
func (s *DefaultCoursesService) GetEnrollmentsByUserID(userID uint) ([]models.Enrollment, error) {
	return s.coursesRepo.GetEnrollmentsByUserID(userID)
}

// GetEnrollmentsByCourseID retrieves enrollments for a course
func (s *DefaultCoursesService) GetEnrollmentsByCourseID(courseID uint) ([]models.Enrollment, error) {
	return s.coursesRepo.GetEnrollmentsByCourseID(courseID)
}

// GetEnrollment retrieves an enrollment for a user and course
func (s *DefaultCoursesService) GetEnrollment(userID, courseID uint) (*models.Enrollment, error) {
	return s.coursesRepo.GetEnrollment(userID, courseID)
}

// CreateEnrollment creates a new enrollment
func (s *DefaultCoursesService) CreateEnrollment(enrollment *models.Enrollment) error {
	return s.coursesRepo.CreateEnrollment(enrollment)
}

// UpdateEnrollment updates an existing enrollment
func (s *DefaultCoursesService) UpdateEnrollment(enrollment *models.Enrollment) error {
	return s.coursesRepo.UpdateEnrollment(enrollment)
}

// DeleteEnrollment deletes an enrollment
func (s *DefaultCoursesService) DeleteEnrollment(id uint) error {
	return s.coursesRepo.DeleteEnrollment(id)
}

// GetProgressByUserAndCourse retrieves progress for a user and course
func (s *DefaultCoursesService) GetProgressByUserAndCourse(userID, courseID uint) ([]models.Progress, error) {
	return s.coursesRepo.GetProgressByUserAndCourse(userID, courseID)
}

// GetProgressByUserAndLesson retrieves progress for a user and lesson
func (s *DefaultCoursesService) GetProgressByUserAndLesson(userID, lessonID uint) (*models.Progress, error) {
	return s.coursesRepo.GetProgressByUserAndLesson(userID, lessonID)
}

// CreateProgress creates a new progress record
func (s *DefaultCoursesService) CreateProgress(progress *models.Progress) error {
	return s.coursesRepo.CreateProgress(progress)
}

// UpdateProgress updates an existing progress record
func (s *DefaultCoursesService) UpdateProgress(progress *models.Progress) error {
	return s.coursesRepo.UpdateProgress(progress)
}

// GetQuizAttemptsByUserAndQuiz retrieves quiz attempts for a user and quiz
func (s *DefaultCoursesService) GetQuizAttemptsByUserAndQuiz(userID, quizID uint) ([]models.QuizAttempt, error) {
	return s.coursesRepo.GetQuizAttemptsByUserAndQuiz(userID, quizID)
}

// GetQuizAttemptByID retrieves a quiz attempt by ID
func (s *DefaultCoursesService) GetQuizAttemptByID(id uint) (*models.QuizAttempt, error) {
	return s.coursesRepo.GetQuizAttemptByID(id)
}

// CreateQuizAttempt creates a new quiz attempt
func (s *DefaultCoursesService) CreateQuizAttempt(attempt *models.QuizAttempt) error {
	return s.coursesRepo.CreateQuizAttempt(attempt)
}

// UpdateQuizAttempt updates an existing quiz attempt
func (s *DefaultCoursesService) UpdateQuizAttempt(attempt *models.QuizAttempt) error {
	return s.coursesRepo.UpdateQuizAttempt(attempt)
}

// GetSubmissionsByUserAndAssignment retrieves submissions for a user and assignment
func (s *DefaultCoursesService) GetSubmissionsByUserAndAssignment(userID, assignmentID uint) ([]models.AssignmentSubmission, error) {
	return s.coursesRepo.GetSubmissionsByUserAndAssignment(userID, assignmentID)
}

// GetSubmissionByID retrieves a submission by ID
func (s *DefaultCoursesService) GetSubmissionByID(id uint) (*models.AssignmentSubmission, error) {
	return s.coursesRepo.GetSubmissionByID(id)
}

// CreateSubmission creates a new submission
func (s *DefaultCoursesService) CreateSubmission(submission *models.AssignmentSubmission) error {
	return s.coursesRepo.CreateSubmission(submission)
}

// UpdateSubmission updates an existing submission
func (s *DefaultCoursesService) UpdateSubmission(submission *models.AssignmentSubmission) error {
	return s.coursesRepo.UpdateSubmission(submission)
}

// GetReviewsByCourseID retrieves reviews for a course
func (s *DefaultCoursesService) GetReviewsByCourseID(courseID uint) ([]models.Review, error) {
	return s.coursesRepo.GetReviewsByCourseID(courseID)
}

// GetReviewByUserAndCourse retrieves a review for a user and course
func (s *DefaultCoursesService) GetReviewByUserAndCourse(userID, courseID uint) (*models.Review, error) {
	return s.coursesRepo.GetReviewByUserAndCourse(userID, courseID)
}

// CreateReview creates a new review
func (s *DefaultCoursesService) CreateReview(review *models.Review) error {
	return s.coursesRepo.CreateReview(review)
}

// UpdateReview updates an existing review
func (s *DefaultCoursesService) UpdateReview(review *models.Review) error {
	return s.coursesRepo.UpdateReview(review)
}

// DeleteReview deletes a review
func (s *DefaultCoursesService) DeleteReview(id uint) error {
	return s.coursesRepo.DeleteReview(id)
}

// GetCertificatesByUserID retrieves certificates for a user
func (s *DefaultCoursesService) GetCertificatesByUserID(userID uint) ([]models.Certificate, error) {
	return s.coursesRepo.GetCertificatesByUserID(userID)
}

// GetCertificateByUserAndCourse retrieves a certificate for a user and course
func (s *DefaultCoursesService) GetCertificateByUserAndCourse(userID, courseID uint) (*models.Certificate, error) {
	return s.coursesRepo.GetCertificateByUserAndCourse(userID, courseID)
}

// CreateCertificate creates a new certificate
func (s *DefaultCoursesService) CreateCertificate(certificate *models.Certificate) error {
	return s.coursesRepo.CreateCertificate(certificate)
}

// CheckCourseCompletion checks if a user has completed a course
func (s *DefaultCoursesService) CheckCourseCompletion(userID, courseID uint) (bool, error) {
	// Get enrollment
	enrollment, err := s.coursesRepo.GetEnrollment(userID, courseID)
	if err != nil {
		return false, fmt.Errorf("error getting enrollment: %w", err)
	}
	
	if enrollment == nil {
		return false, fmt.Errorf("user is not enrolled in this course")
	}
	
	if enrollment.IsCompleted {
		return true, nil
	}
	
	// Get all modules for the course
	modules, err := s.coursesRepo.GetModulesByCourseID(courseID)
	if err != nil {
		return false, fmt.Errorf("error getting modules: %w", err)
	}
	
	// Check if all modules are completed
	for _, module := range modules {
		// Get all lessons for the module
		lessons, err := s.coursesRepo.GetLessonsByModuleID(module.ID)
		if err != nil {
			return false, fmt.Errorf("error getting lessons: %w", err)
		}
		
		// Check if all lessons are completed
		for _, lesson := range lessons {
			progress, err := s.coursesRepo.GetProgressByUserAndLesson(userID, lesson.ID)
			if err != nil {
				return false, fmt.Errorf("error getting progress: %w", err)
			}
			
			if progress == nil || !progress.Completed {
				return false, nil
			}
			
			// Check if quiz is completed if exists
			quiz, err := s.coursesRepo.GetQuizByLessonID(lesson.ID)
			if err != nil {
				return false, fmt.Errorf("error getting quiz: %w", err)
			}
			
			if quiz != nil {
				attempts, err := s.coursesRepo.GetQuizAttemptsByUserAndQuiz(userID, quiz.ID)
				if err != nil {
					return false, fmt.Errorf("error getting quiz attempts: %w", err)
				}
				
				// Check if any attempt passed
				quizPassed := false
				for _, attempt := range attempts {
					if attempt.Passed {
						quizPassed = true
						break
					}
				}
				
				if !quizPassed {
					return false, nil
				}
			}
		}
	}
	
	return true, nil
}

// MarkCourseAsCompleted marks a course as completed for a user
func (s *DefaultCoursesService) MarkCourseAsCompleted(userID, courseID uint) error {
	// Get enrollment
	enrollment, err := s.coursesRepo.GetEnrollment(userID, courseID)
	if err != nil {
		return fmt.Errorf("error getting enrollment: %w", err)
	}
	
	if enrollment == nil {
		return fmt.Errorf("user is not enrolled in this course")
	}
	
	// Update enrollment
	enrollment.IsCompleted = true
	now := time.Now()
	enrollment.CompletionDate = &now
	
	return s.coursesRepo.UpdateEnrollment(enrollment)
}

// GenerateCourseCompletionCertificate generates a certificate for a completed course
func (s *DefaultCoursesService) GenerateCourseCompletionCertificate(userID, courseID uint) (*models.Certificate, error) {
	// Check if course is completed
	completed, err := s.CheckCourseCompletion(userID, courseID)
	if err != nil {
		return nil, fmt.Errorf("error checking course completion: %w", err)
	}
	
	if !completed {
		return nil, fmt.Errorf("course is not completed")
	}
	
	// Check if certificate already exists
	existingCert, err := s.coursesRepo.GetCertificateByUserAndCourse(userID, courseID)
	if err != nil {
		return nil, fmt.Errorf("error checking existing certificate: %w", err)
	}
	
	if existingCert != nil {
		return existingCert, nil
	}
	
	// Create certificate
	certificate := &models.Certificate{
		UserID:    userID,
		CourseID:  courseID,
		IssuedAt:  time.Now(),
	}
	
	// Generate certificate URL (in a real implementation, this would generate a PDF)
	certificate.CertificateURL = fmt.Sprintf("/certificates/%d_%d.pdf", userID, courseID)
	
	// Save certificate
	err = s.coursesRepo.CreateCertificate(certificate)
	if err != nil {
		return nil, fmt.Errorf("error creating certificate: %w", err)
	}
	
	return certificate, nil
}
