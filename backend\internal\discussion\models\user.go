package models

import (
        "time"

        "gorm.io/gorm"
)

// User represents a user in the discussion module
type User struct {
        gorm.Model
        Username        string    `json:"username" gorm:"uniqueIndex"`
        Email           string    `json:"email" gorm:"uniqueIndex"`
        DisplayName     string    `json:"displayName"`
        ProfileImageURL string    `json:"profileImageUrl"`
        Bio             string    `json:"bio" gorm:"type:text"`
        IsActive        bool      `json:"isActive" gorm:"default:true"`
        LastSeenAt      time.Time `json:"lastSeenAt"`
        Reputation      int       `json:"reputation" gorm:"default:0"`
}