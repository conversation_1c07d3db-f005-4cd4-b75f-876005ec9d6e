package repository

import (
	"github.com/greatnigeria/internal/discussion/models"
	"gorm.io/gorm"
)

// ContentLinkRepository defines the interface for content link operations
type ContentLinkRepository interface {
	// Topic content links
	CreateTopicContentLink(link *models.TopicContentLink) error
	GetTopicContentLinks(topicID uint) ([]models.TopicContentLink, error)
	GetTopicsByContent(contentType models.ContentReferenceType, contentID uint) ([]models.TopicContentLink, error)
	GetHighlightedTopicsByContent(contentType models.ContentReferenceType, contentID uint) ([]models.TopicContentLink, error)
	UpdateTopicContentLink(link *models.TopicContentLink) error
	DeleteTopicContentLink(id uint) error
	
	// Comment content links
	CreateCommentContentLink(link *models.CommentContentLink) error
	GetCommentContentLinks(commentID uint) ([]models.CommentContentLink, error)
	GetCommentsByContent(contentType models.ContentReferenceType, contentID uint) ([]models.CommentContentLink, error)
	UpdateCommentContentLink(link *models.CommentContentLink) error
	DeleteCommentContentLink(id uint) error
	
	// Recommendations
	CreateContentDiscussionRecommendation(recommendation *models.ContentDiscussionRecommendation) error
	GetContentDiscussionRecommendations(contentType models.ContentReferenceType, contentID uint, limit int) ([]models.ContentDiscussionRecommendation, error)
	UpdateContentDiscussionRecommendation(recommendation *models.ContentDiscussionRecommendation) error
	DeleteContentDiscussionRecommendation(id uint) error
	
	// Auto-generated topic templates
	CreateAutoGeneratedTopicTemplate(template *models.AutoGeneratedTopicTemplate) error
	GetAutoGeneratedTopicTemplates(contentType models.ContentReferenceType) ([]models.AutoGeneratedTopicTemplate, error)
	GetActiveAutoGeneratedTopicTemplates(contentType models.ContentReferenceType) ([]models.AutoGeneratedTopicTemplate, error)
	GetAutoGeneratedTopicTemplateByID(id uint) (*models.AutoGeneratedTopicTemplate, error)
	UpdateAutoGeneratedTopicTemplate(template *models.AutoGeneratedTopicTemplate) error
	DeleteAutoGeneratedTopicTemplate(id uint) error
}

// GormContentLinkRepository implements the ContentLinkRepository interface
type GormContentLinkRepository struct {
	db *gorm.DB
}

// NewGormContentLinkRepository creates a new content link repository
func NewGormContentLinkRepository(db *gorm.DB) *GormContentLinkRepository {
	return &GormContentLinkRepository{db: db}
}

// CreateTopicContentLink creates a new topic content link
func (r *GormContentLinkRepository) CreateTopicContentLink(link *models.TopicContentLink) error {
	return r.db.Create(link).Error
}

// GetTopicContentLinks retrieves content links for a topic
func (r *GormContentLinkRepository) GetTopicContentLinks(topicID uint) ([]models.TopicContentLink, error) {
	var links []models.TopicContentLink
	result := r.db.Where("topic_id = ?", topicID).Find(&links)
	return links, result.Error
}

// GetTopicsByContent retrieves topics linked to a content
func (r *GormContentLinkRepository) GetTopicsByContent(contentType models.ContentReferenceType, contentID uint) ([]models.TopicContentLink, error) {
	var links []models.TopicContentLink
	result := r.db.Where("content_type = ? AND content_id = ?", contentType, contentID).Find(&links)
	return links, result.Error
}

// GetHighlightedTopicsByContent retrieves highlighted topics for a content
func (r *GormContentLinkRepository) GetHighlightedTopicsByContent(contentType models.ContentReferenceType, contentID uint) ([]models.TopicContentLink, error) {
	var links []models.TopicContentLink
	result := r.db.Where("content_type = ? AND content_id = ? AND is_highlighted = ?", contentType, contentID, true).Find(&links)
	return links, result.Error
}

// UpdateTopicContentLink updates a topic content link
func (r *GormContentLinkRepository) UpdateTopicContentLink(link *models.TopicContentLink) error {
	return r.db.Save(link).Error
}

// DeleteTopicContentLink deletes a topic content link
func (r *GormContentLinkRepository) DeleteTopicContentLink(id uint) error {
	return r.db.Delete(&models.TopicContentLink{}, id).Error
}

// CreateCommentContentLink creates a new comment content link
func (r *GormContentLinkRepository) CreateCommentContentLink(link *models.CommentContentLink) error {
	return r.db.Create(link).Error
}

// GetCommentContentLinks retrieves content links for a comment
func (r *GormContentLinkRepository) GetCommentContentLinks(commentID uint) ([]models.CommentContentLink, error) {
	var links []models.CommentContentLink
	result := r.db.Where("comment_id = ?", commentID).Find(&links)
	return links, result.Error
}

// GetCommentsByContent retrieves comments linked to a content
func (r *GormContentLinkRepository) GetCommentsByContent(contentType models.ContentReferenceType, contentID uint) ([]models.CommentContentLink, error) {
	var links []models.CommentContentLink
	result := r.db.Where("content_type = ? AND content_id = ?", contentType, contentID).Find(&links)
	return links, result.Error
}

// UpdateCommentContentLink updates a comment content link
func (r *GormContentLinkRepository) UpdateCommentContentLink(link *models.CommentContentLink) error {
	return r.db.Save(link).Error
}

// DeleteCommentContentLink deletes a comment content link
func (r *GormContentLinkRepository) DeleteCommentContentLink(id uint) error {
	return r.db.Delete(&models.CommentContentLink{}, id).Error
}

// CreateContentDiscussionRecommendation creates a new content discussion recommendation
func (r *GormContentLinkRepository) CreateContentDiscussionRecommendation(recommendation *models.ContentDiscussionRecommendation) error {
	return r.db.Create(recommendation).Error
}

// GetContentDiscussionRecommendations retrieves discussion recommendations for a content
func (r *GormContentLinkRepository) GetContentDiscussionRecommendations(contentType models.ContentReferenceType, contentID uint, limit int) ([]models.ContentDiscussionRecommendation, error) {
	var recommendations []models.ContentDiscussionRecommendation
	
	query := r.db.Where("content_type = ? AND content_id = ?", contentType, contentID).
		Order("is_manually_added DESC, recommendation_score DESC")
		
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	result := query.Find(&recommendations)
	return recommendations, result.Error
}

// UpdateContentDiscussionRecommendation updates a content discussion recommendation
func (r *GormContentLinkRepository) UpdateContentDiscussionRecommendation(recommendation *models.ContentDiscussionRecommendation) error {
	return r.db.Save(recommendation).Error
}

// DeleteContentDiscussionRecommendation deletes a content discussion recommendation
func (r *GormContentLinkRepository) DeleteContentDiscussionRecommendation(id uint) error {
	return r.db.Delete(&models.ContentDiscussionRecommendation{}, id).Error
}

// CreateAutoGeneratedTopicTemplate creates a new auto-generated topic template
func (r *GormContentLinkRepository) CreateAutoGeneratedTopicTemplate(template *models.AutoGeneratedTopicTemplate) error {
	return r.db.Create(template).Error
}

// GetAutoGeneratedTopicTemplates retrieves auto-generated topic templates for a content type
func (r *GormContentLinkRepository) GetAutoGeneratedTopicTemplates(contentType models.ContentReferenceType) ([]models.AutoGeneratedTopicTemplate, error) {
	var templates []models.AutoGeneratedTopicTemplate
	result := r.db.Where("content_type = ?", contentType).Find(&templates)
	return templates, result.Error
}

// GetActiveAutoGeneratedTopicTemplates retrieves active auto-generated topic templates for a content type
func (r *GormContentLinkRepository) GetActiveAutoGeneratedTopicTemplates(contentType models.ContentReferenceType) ([]models.AutoGeneratedTopicTemplate, error) {
	var templates []models.AutoGeneratedTopicTemplate
	result := r.db.Where("content_type = ? AND is_active = ?", contentType, true).Find(&templates)
	return templates, result.Error
}

// GetAutoGeneratedTopicTemplateByID retrieves an auto-generated topic template by ID
func (r *GormContentLinkRepository) GetAutoGeneratedTopicTemplateByID(id uint) (*models.AutoGeneratedTopicTemplate, error) {
	var template models.AutoGeneratedTopicTemplate
	result := r.db.First(&template, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &template, nil
}

// UpdateAutoGeneratedTopicTemplate updates an auto-generated topic template
func (r *GormContentLinkRepository) UpdateAutoGeneratedTopicTemplate(template *models.AutoGeneratedTopicTemplate) error {
	return r.db.Save(template).Error
}

// DeleteAutoGeneratedTopicTemplate deletes an auto-generated topic template
func (r *GormContentLinkRepository) DeleteAutoGeneratedTopicTemplate(id uint) error {
	return r.db.Delete(&models.AutoGeneratedTopicTemplate{}, id).Error
}