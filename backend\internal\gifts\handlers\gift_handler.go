package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/greatnigeria/internal/gifts/models"
	"github.com/greatnigeria/internal/gifts/service"
	"github.com/greatnigeria/pkg/common/errors"
	"github.com/greatnigeria/pkg/common/logger"
)

// GiftHandler handles HTTP requests related to virtual gifts
type GiftHandler struct {
	giftService service.GiftService
	logger      *logger.Logger
}

// NewGiftHandler creates a new gift handler
func NewGiftHandler(giftService service.GiftService, logger *logger.Logger) *GiftHandler {
	return &GiftHandler{
		giftService: giftService,
		logger:      logger,
	}
}

// GetAllGifts retrieves all gifts with pagination
// @Summary Get all virtual gifts
// @Description Retrieve all virtual gifts with pagination
// @Tags gifts
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Number of items per page (default: 20)"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} errors.APIError
// @Router /gifts [get]
func (h *GiftHandler) GetAllGifts(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Get all gifts
	gifts, total, err := h.giftService.GetAllGifts(c.Request.Context(), page, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get all gifts")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get gifts"))
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"gifts": gifts,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
		},
	})
}

// GetGiftsByCategory retrieves gifts by category with pagination
// @Summary Get gifts by category
// @Description Retrieve gifts by category with pagination
// @Tags gifts
// @Accept json
// @Produce json
// @Param category path string true "Gift category"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Number of items per page (default: 20)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} errors.APIError
// @Failure 500 {object} errors.APIError
// @Router /gifts/categories/{category} [get]
func (h *GiftHandler) GetGiftsByCategory(c *gin.Context) {
	// Get category parameter
	categoryStr := c.Param("category")
	category := models.GiftCategory(categoryStr)

	// Validate category
	if !isValidCategory(category) {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid gift category"))
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Get gifts by category
	gifts, total, err := h.giftService.GetGiftsByCategory(c.Request.Context(), category, page, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get gifts by category")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get gifts"))
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"category": category,
		"gifts":    gifts,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
		},
	})
}

// GetFeaturedGifts retrieves featured gifts
// @Summary Get featured gifts
// @Description Retrieve featured gifts
// @Tags gifts
// @Accept json
// @Produce json
// @Param limit query int false "Number of items to return (default: 10)"
// @Success 200 {array} models.Gift
// @Failure 500 {object} errors.APIError
// @Router /gifts/featured [get]
func (h *GiftHandler) GetFeaturedGifts(c *gin.Context) {
	// Parse limit parameter
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// Get featured gifts
	gifts, err := h.giftService.GetFeaturedGifts(c.Request.Context(), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get featured gifts")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get featured gifts"))
		return
	}

	c.JSON(http.StatusOK, gifts)
}

// GetGiftByID retrieves a gift by its ID
// @Summary Get gift by ID
// @Description Retrieve a gift by its ID
// @Tags gifts
// @Accept json
// @Produce json
// @Param id path int true "Gift ID"
// @Success 200 {object} models.Gift
// @Failure 404 {object} errors.APIError
// @Failure 500 {object} errors.APIError
// @Router /gifts/{id} [get]
func (h *GiftHandler) GetGiftByID(c *gin.Context) {
	// Parse gift ID
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid gift ID"))
		return
	}

	// Get gift by ID
	gift, err := h.giftService.GetGiftByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get gift by ID")
		c.JSON(http.StatusNotFound, errors.ErrNotFound("Gift not found"))
		return
	}

	c.JSON(http.StatusOK, gift)
}

// SearchGifts searches for gifts by name or description
// @Summary Search gifts
// @Description Search for gifts by name or description
// @Tags gifts
// @Accept json
// @Produce json
// @Param q query string true "Search query"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Number of items per page (default: 20)"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} errors.APIError
// @Router /gifts/search [get]
func (h *GiftHandler) SearchGifts(c *gin.Context) {
	// Get search query
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Search query is required"))
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Search gifts
	gifts, total, err := h.giftService.SearchGifts(c.Request.Context(), query, page, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search gifts")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to search gifts"))
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"query": query,
		"gifts": gifts,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
		},
	})
}

// GetGiftPriceTiers retrieves price tiers for a gift
// @Summary Get gift price tiers
// @Description Retrieve price tiers for a gift
// @Tags gifts
// @Accept json
// @Produce json
// @Param id path int true "Gift ID"
// @Success 200 {array} models.GiftPriceTier
// @Failure 404 {object} errors.APIError
// @Failure 500 {object} errors.APIError
// @Router /gifts/{id}/price-tiers [get]
func (h *GiftHandler) GetGiftPriceTiers(c *gin.Context) {
	// Parse gift ID
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid gift ID"))
		return
	}

	// Get price tiers for gift
	tiers, err := h.giftService.GetGiftPriceTiers(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get gift price tiers")
		c.JSON(http.StatusNotFound, errors.ErrNotFound("Gift price tiers not found"))
		return
	}

	c.JSON(http.StatusOK, tiers)
}

// SendGift sends a gift from one user to another
// @Summary Send a gift
// @Description Send a gift from one user to another
// @Tags gifts
// @Accept json
// @Produce json
// @Param body body SendGiftRequest true "Gift information"
// @Success 200 {object} models.GiftTransaction
// @Failure 400 {object} errors.APIError
// @Failure 500 {object} errors.APIError
// @Router /gifts/send [post]
func (h *GiftHandler) SendGift(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.ErrUnauthorized("User not authenticated"))
		return
	}

	// Parse request body
	var req SendGiftRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest(err.Error()))
		return
	}

	// Validate request
	if req.GiftID == 0 {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Gift ID is required"))
		return
	}

	if req.RecipientID == 0 {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Recipient ID is required"))
		return
	}

	if req.PriceTierID == 0 {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Price tier ID is required"))
		return
	}

	// Send gift
	transaction, err := h.giftService.SendGift(
		c.Request.Context(),
		userID.(uint),
		req.RecipientID,
		req.GiftID,
		req.PriceTierID,
		req.ContentID,
		req.ContentType,
		req.Message,
		req.IsAnonymous,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to send gift")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to send gift: "+err.Error()))
		return
	}

	c.JSON(http.StatusOK, transaction)
}

// GetUserSentGifts retrieves gifts sent by the current user
// @Summary Get gifts sent by user
// @Description Retrieve gifts sent by the current user
// @Tags gifts
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Number of items per page (default: 20)"
// @Success 200 {object} map[string]interface{}
// @Failure 401 {object} errors.APIError
// @Failure 500 {object} errors.APIError
// @Router /gifts/sent [get]
func (h *GiftHandler) GetUserSentGifts(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.ErrUnauthorized("User not authenticated"))
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Get sent gifts
	transactions, total, err := h.giftService.GetUserSentGifts(c.Request.Context(), userID.(uint), page, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user sent gifts")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get sent gifts"))
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"transactions": transactions,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
		},
	})
}

// GetUserReceivedGifts retrieves gifts received by the current user
// @Summary Get gifts received by user
// @Description Retrieve gifts received by the current user
// @Tags gifts
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Number of items per page (default: 20)"
// @Success 200 {object} map[string]interface{}
// @Failure 401 {object} errors.APIError
// @Failure 500 {object} errors.APIError
// @Router /gifts/received [get]
func (h *GiftHandler) GetUserReceivedGifts(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.ErrUnauthorized("User not authenticated"))
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Get received gifts
	transactions, total, err := h.giftService.GetUserReceivedGifts(c.Request.Context(), userID.(uint), page, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user received gifts")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get received gifts"))
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"transactions": transactions,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
		},
	})
}

// GetContentGifts retrieves gifts for a specific content
// @Summary Get gifts for content
// @Description Retrieve gifts for a specific content
// @Tags gifts
// @Accept json
// @Produce json
// @Param content_id path int true "Content ID"
// @Param content_type query string false "Content type"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Number of items per page (default: 20)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} errors.APIError
// @Failure 500 {object} errors.APIError
// @Router /gifts/content/{content_id} [get]
func (h *GiftHandler) GetContentGifts(c *gin.Context) {
	// Parse content ID
	contentIDParam := c.Param("content_id")
	contentID, err := strconv.ParseUint(contentIDParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid content ID"))
		return
	}

	// Get content type
	contentType := c.Query("content_type")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Get content gifts
	transactions, total, err := h.giftService.GetContentGifts(c.Request.Context(), uint(contentID), contentType, page, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get content gifts")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get content gifts"))
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"content_id":   contentID,
		"content_type": contentType,
		"transactions": transactions,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + limit - 1) / limit,
		},
	})
}

// GetLeaderboard retrieves the gift leaderboard
// @Summary Get gift leaderboard
// @Description Retrieve the gift leaderboard
// @Tags gifts
// @Accept json
// @Produce json
// @Param type path string true "Leaderboard type (sender or recipient)"
// @Param period query string false "Period type (daily, weekly, monthly, all_time) (default: weekly)"
// @Param limit query int false "Number of items to return (default: 20)"
// @Success 200 {array} models.GiftLeaderboard
// @Failure 400 {object} errors.APIError
// @Failure 500 {object} errors.APIError
// @Router /gifts/leaderboard/{type} [get]
func (h *GiftHandler) GetLeaderboard(c *gin.Context) {
	// Get leaderboard type
	leaderboardType := c.Param("type")
	if leaderboardType != "sender" && leaderboardType != "recipient" {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid leaderboard type: must be 'sender' or 'recipient'"))
		return
	}

	// Get period type
	periodType := c.DefaultQuery("period", "weekly")
	validPeriods := map[string]bool{
		"daily":    true,
		"weekly":   true,
		"monthly":  true,
		"all_time": true,
	}
	if !validPeriods[periodType] {
		c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid period type: must be 'daily', 'weekly', 'monthly', or 'all_time'"))
		return
	}

	// Parse limit parameter
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Get leaderboard
	leaderboard, err := h.giftService.GetLeaderboard(c.Request.Context(), leaderboardType, periodType, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get leaderboard")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get leaderboard"))
		return
	}

	c.JSON(http.StatusOK, leaderboard)
}

// GetUserGiftSummary retrieves the gift summary for the current user
// @Summary Get user gift summary
// @Description Retrieve the gift summary for the current user
// @Tags gifts
// @Accept json
// @Produce json
// @Success 200 {object} models.UserGiftSummary
// @Failure 401 {object} errors.APIError
// @Failure 500 {object} errors.APIError
// @Router /gifts/summary [get]
func (h *GiftHandler) GetUserGiftSummary(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.ErrUnauthorized("User not authenticated"))
		return
	}

	// Get user gift summary
	summary, err := h.giftService.GetUserGiftSummary(c.Request.Context(), userID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user gift summary")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get gift summary"))
		return
	}

	c.JSON(http.StatusOK, summary)
}

// GetGiftCategoryConfigs retrieves all gift category configurations
// @Summary Get gift category configurations
// @Description Retrieve all gift category configurations
// @Tags gifts
// @Accept json
// @Produce json
// @Success 200 {array} models.GiftCategoryConfig
// @Failure 500 {object} errors.APIError
// @Router /gifts/categories [get]
func (h *GiftHandler) GetGiftCategoryConfigs(c *gin.Context) {
	// Get gift category configs
	configs, err := h.giftService.GetGiftCategoryConfigs(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get gift category configs")
		c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get category configurations"))
		return
	}

	c.JSON(http.StatusOK, configs)
}

// SendGiftRequest represents a request to send a gift
type SendGiftRequest struct {
	GiftID      uint   `json:"giftId" binding:"required"`
	RecipientID uint   `json:"recipientId" binding:"required"`
	PriceTierID uint   `json:"priceTierId" binding:"required"`
	ContentID   *uint  `json:"contentId"`
	ContentType string `json:"contentType"`
	Message     string `json:"message"`
	IsAnonymous bool   `json:"isAnonymous"`
}

// Helper function to validate gift category
func isValidCategory(category models.GiftCategory) bool {
	validCategories := map[models.GiftCategory]bool{
		models.CategoryTraditional: true,
		models.CategoryRoyal:       true,
		models.CategoryCelebration: true,
		models.CategoryPremium:     true,
	}
	return validCategories[category]
}