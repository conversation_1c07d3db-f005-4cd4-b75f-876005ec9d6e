package models

import (
	"time"

	"gorm.io/gorm"
)

// GiftRarityLevel represents the rarity level of a virtual gift
type GiftRarityLevel string

const (
	// RarityCommon for common, low-cost gifts
	RarityCommon GiftRarityLevel = "common"

	// RarityUncommon for uncommon gifts
	RarityUncommon GiftRarityLevel = "uncommon"

	// RarityRare for rare, higher value gifts
	RarityRare GiftRarityLevel = "rare"

	// RarityEpic for epic, special occasion gifts
	RarityEpic GiftRarityLevel = "epic"

	// RarityLegendary for legendary, highest tier gifts
	RarityLegendary GiftRarityLevel = "legendary"
)

// GiftCategory represents the category of a virtual gift
type GiftCategory string

const (
	// CategoryTraditional for traditional symbols like cowrie shells, kola nut
	CategoryTraditional GiftCategory = "traditional"

	// CategoryRoyal for royal gifts like chief's cap, beaded crown
	CategoryRoyal GiftCategory = "royal"

	// CategoryCelebration for celebration items like Ankara fabric, palmwine cup
	CategoryCelebration GiftCategory = "celebration"

	// CategoryPremium for premium national gifts like Naija Eagle, Unity Bridge
	CategoryPremium GiftCategory = "premium"
)

// Gift represents a Nigerian cultural virtual gift
type Gift struct {
	gorm.Model
	Name         string         `json:"name" gorm:"not null"`
	Description  string         `json:"description" gorm:"type:text"`
	Category     GiftCategory   `json:"category" gorm:"not null;type:varchar(50)"`
	EthnicOrigin string         `json:"ethnicOrigin" gorm:"type:varchar(100)"`
	RarityLevel  GiftRarityLevel `json:"rarityLevel" gorm:"not null;type:varchar(50)"`
	
	// Visual and Audio assets
	AssetBaseURL  string `json:"assetBaseURL"`
	ThumbnailPath string `json:"thumbnailPath"`
	ModelPath     string `json:"modelPath"`
	AnimationPath string `json:"animationPath"`
	SoundPath     string `json:"soundPath"`
	
	// Cultural and display information
	CulturalSignificance string `json:"culturalSignificance" gorm:"type:text"`
	DisplayMessage       string `json:"displayMessage" gorm:"type:varchar(255)"`
	
	// Pricing and availability
	PriceTiersJSON string    `json:"priceTiersJSON" gorm:"type:text;column:price_tiers_json"` // Stored as JSON array
	IsActive       bool      `json:"isActive" gorm:"default:true"`
	AvailableFrom  time.Time `json:"availableFrom"`
	AvailableTo    *time.Time `json:"availableTo"`
	
	// Admin and management fields
	CreatedBy   uint  `json:"createdBy"`
	IsVerified  bool  `json:"isVerified" gorm:"default:false"`
	VerifiedBy  *uint `json:"verifiedBy"`
	FeaturedOrder *int `json:"featuredOrder"` // For prioritizing in UI, null = not featured
}

// GiftPriceTier represents a price tier for a gift when purchased
type GiftPriceTier struct {
	gorm.Model
	GiftID      uint    `json:"giftId" gorm:"index"`
	TierLevel   int     `json:"tierLevel" gorm:"not null"`
	PriceNaira  float64 `json:"priceNaira" gorm:"not null"`
	PriceUSD    float64 `json:"priceUSD" gorm:"not null"`
	Description string  `json:"description" gorm:"type:varchar(255)"`
	IsDefault   bool    `json:"isDefault" gorm:"default:false"`
}

// GiftTransaction represents a gift sent from one user to another
type GiftTransaction struct {
	gorm.Model
	GiftID       uint    `json:"giftId" gorm:"index:idx_gift_transaction_gift"`
	SenderID     uint    `json:"senderId" gorm:"index:idx_gift_transaction_sender"`
	RecipientID  uint    `json:"recipientId" gorm:"index:idx_gift_transaction_recipient"`
	ContentID    *uint   `json:"contentId" gorm:"index:idx_gift_transaction_content"` // Content where gift was given
	ContentType  string  `json:"contentType" gorm:"type:varchar(50)"`                 // Type of content (post, comment, stream)
	PriceTierID  uint    `json:"priceTierId"`
	PricePaid    float64 `json:"pricePaid" gorm:"not null"`
	CurrencyCode string  `json:"currencyCode" gorm:"type:varchar(3);default:'NGN'"`
	
	// Transaction information
	TransactionID string `json:"transactionId" gorm:"type:varchar(100)"`
	PaymentID     *uint  `json:"paymentId"`
	Status        string `json:"status" gorm:"type:varchar(50);default:'completed'"`
	
	// Message and visibility
	Message     string `json:"message" gorm:"type:text"`
	IsAnonymous bool   `json:"isAnonymous" gorm:"default:false"`
	IsPublic    bool   `json:"isPublic" gorm:"default:true"`
	
	// Revenue sharing
	CreatorRevenuePercent float64 `json:"creatorRevenuePercent" gorm:"default:50.0"`
	CreatorRevenueAmount  float64 `json:"creatorRevenueAmount"`
	PlatformRevenueAmount float64 `json:"platformRevenueAmount"`
}

// GiftLeaderboard represents the leaderboard of users based on gifts
type GiftLeaderboard struct {
	gorm.Model
	UserID           uint      `json:"userId" gorm:"uniqueIndex:idx_gift_leaderboard_user_period"`
	LeaderboardType  string    `json:"leaderboardType" gorm:"type:varchar(50);uniqueIndex:idx_gift_leaderboard_user_period"` // "sender" or "recipient"
	PeriodType       string    `json:"periodType" gorm:"type:varchar(50);uniqueIndex:idx_gift_leaderboard_user_period"`     // "daily", "weekly", "monthly", "all_time"
	PeriodStart      time.Time `json:"periodStart" gorm:"uniqueIndex:idx_gift_leaderboard_user_period"`
	PeriodEnd        time.Time `json:"periodEnd"`
	
	// Metrics
	TotalGiftsSent     int     `json:"totalGiftsSent" gorm:"default:0"`
	TotalGiftsReceived int     `json:"totalGiftsReceived" gorm:"default:0"`
	TotalValueSent     float64 `json:"totalValueSent" gorm:"default:0"`
	TotalValueReceived float64 `json:"totalValueReceived" gorm:"default:0"`
	
	// Ranking
	Rank         int  `json:"rank" gorm:"default:0"`
	RankChange   *int `json:"rankChange"` // Change since last period
	IsHighlighted bool `json:"isHighlighted" gorm:"default:false"`
}

// UserGiftSummary represents the summary of a user's gift activity
type UserGiftSummary struct {
	gorm.Model
	UserID              uint    `json:"userId" gorm:"uniqueIndex"`
	TotalGiftsSent      int     `json:"totalGiftsSent" gorm:"default:0"`
	TotalGiftsReceived  int     `json:"totalGiftsReceived" gorm:"default:0"`
	TotalValueSent      float64 `json:"totalValueSent" gorm:"default:0"`
	TotalValueReceived  float64 `json:"totalValueReceived" gorm:"default:0"`
	MostReceivedGiftID  *uint   `json:"mostReceivedGiftId"`
	MostSentGiftID      *uint   `json:"mostSentGiftId"`
	TopSenderID         *uint   `json:"topSenderId"`      // User who sent this user the most gifts
	TopRecipientID      *uint   `json:"topRecipientId"`   // User who received most gifts from this user
	LastGiftReceivedAt  *time.Time `json:"lastGiftReceivedAt"`
	LastGiftSentAt      *time.Time `json:"lastGiftSentAt"`
}

// GiftCategoryConfig represents configuration for a gift category
type GiftCategoryConfig struct {
	gorm.Model
	Category             GiftCategory `json:"category" gorm:"uniqueIndex;type:varchar(50)"`
	DisplayName          string       `json:"displayName" gorm:"not null"`
	Description          string       `json:"description" gorm:"type:text"`
	IconPath             string       `json:"iconPath"`
	IsEnabled            bool         `json:"isEnabled" gorm:"default:true"`
	DisplayOrder         int          `json:"displayOrder" gorm:"default:0"`
	MinimumPriceNaira    float64      `json:"minimumPriceNaira" gorm:"default:0"`
	RevenueSharePercent  float64      `json:"revenueSharePercent" gorm:"default:50.0"`
	FeaturedGiftID       *uint        `json:"featuredGiftId"`
}