package repository

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/greatnigeria/internal/gifts/models"
	"github.com/greatnigeria/pkg/common/logger"
	"gorm.io/gorm"
)

// GiftRepositoryImpl implements the GiftRepository interface
type GiftRepositoryImpl struct {
	db     *gorm.DB
	logger *logger.Logger
}

// NewGiftRepository creates a new instance of the gift repository
func NewGiftRepository(db *gorm.DB, logger *logger.Logger) GiftRepository {
	return &GiftRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// CreateGift creates a new gift in the database
func (r *GiftRepositoryImpl) CreateGift(ctx context.Context, gift *models.Gift) error {
	return r.db.WithContext(ctx).Create(gift).Error
}

// GetGiftByID retrieves a gift by its ID
func (r *GiftRepositoryImpl) GetGiftByID(ctx context.Context, id uint) (*models.Gift, error) {
	var gift models.Gift
	err := r.db.WithContext(ctx).First(&gift, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("gift with ID %d not found", id)
		}
		return nil, err
	}
	return &gift, nil
}

// UpdateGift updates an existing gift
func (r *GiftRepositoryImpl) UpdateGift(ctx context.Context, gift *models.Gift) error {
	return r.db.WithContext(ctx).Save(gift).Error
}

// DeleteGift deletes a gift by its ID
func (r *GiftRepositoryImpl) DeleteGift(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.Gift{}, id).Error
}

// GetAllGifts retrieves all gifts with pagination
func (r *GiftRepositoryImpl) GetAllGifts(ctx context.Context, page, limit int) ([]models.Gift, int, error) {
	var gifts []models.Gift
	var count int64

	// Get total count
	if err := r.db.WithContext(ctx).Model(&models.Gift{}).Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated gifts
	err := r.db.WithContext(ctx).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&gifts).Error

	if err != nil {
		return nil, 0, err
	}

	return gifts, int(count), nil
}

// GetGiftsByCategory retrieves gifts by category with pagination
func (r *GiftRepositoryImpl) GetGiftsByCategory(ctx context.Context, category models.GiftCategory, page, limit int) ([]models.Gift, int, error) {
	var gifts []models.Gift
	var count int64

	// Get total count for the category
	if err := r.db.WithContext(ctx).Model(&models.Gift{}).Where("category = ?", category).Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated gifts by category
	err := r.db.WithContext(ctx).
		Where("category = ?", category).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&gifts).Error

	if err != nil {
		return nil, 0, err
	}

	return gifts, int(count), nil
}

// GetFeaturedGifts retrieves featured gifts
func (r *GiftRepositoryImpl) GetFeaturedGifts(ctx context.Context, limit int) ([]models.Gift, error) {
	var gifts []models.Gift

	err := r.db.WithContext(ctx).
		Where("featured_order IS NOT NULL").
		Order("featured_order ASC").
		Limit(limit).
		Find(&gifts).Error

	if err != nil {
		return nil, err
	}

	return gifts, nil
}

// SearchGifts searches for gifts by name or description
func (r *GiftRepositoryImpl) SearchGifts(ctx context.Context, query string, page, limit int) ([]models.Gift, int, error) {
	var gifts []models.Gift
	var count int64

	// Prepare search query
	searchQuery := "%" + query + "%"

	// Get total count for search results
	if err := r.db.WithContext(ctx).Model(&models.Gift{}).
		Where("name ILIKE ? OR description ILIKE ?", searchQuery, searchQuery).
		Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated search results
	err := r.db.WithContext(ctx).
		Where("name ILIKE ? OR description ILIKE ?", searchQuery, searchQuery).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&gifts).Error

	if err != nil {
		return nil, 0, err
	}

	return gifts, int(count), nil
}

// CreatePriceTier creates a new price tier for a gift
func (r *GiftRepositoryImpl) CreatePriceTier(ctx context.Context, tier *models.GiftPriceTier) error {
	return r.db.WithContext(ctx).Create(tier).Error
}

// GetGiftPriceTiers retrieves all price tiers for a gift
func (r *GiftRepositoryImpl) GetGiftPriceTiers(ctx context.Context, giftID uint) ([]models.GiftPriceTier, error) {
	var tiers []models.GiftPriceTier

	err := r.db.WithContext(ctx).
		Where("gift_id = ?", giftID).
		Order("tier_level ASC").
		Find(&tiers).Error

	if err != nil {
		return nil, err
	}

	return tiers, nil
}

// UpdatePriceTier updates an existing price tier
func (r *GiftRepositoryImpl) UpdatePriceTier(ctx context.Context, tier *models.GiftPriceTier) error {
	return r.db.WithContext(ctx).Save(tier).Error
}

// DeletePriceTier deletes a price tier by its ID
func (r *GiftRepositoryImpl) DeletePriceTier(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.GiftPriceTier{}, id).Error
}

// CreateGiftTransaction creates a new gift transaction
func (r *GiftRepositoryImpl) CreateGiftTransaction(ctx context.Context, transaction *models.GiftTransaction) error {
	return r.db.WithContext(ctx).Create(transaction).Error
}

// GetGiftTransactionByID retrieves a gift transaction by its ID
func (r *GiftRepositoryImpl) GetGiftTransactionByID(ctx context.Context, id uint) (*models.GiftTransaction, error) {
	var transaction models.GiftTransaction
	err := r.db.WithContext(ctx).First(&transaction, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("gift transaction with ID %d not found", id)
		}
		return nil, err
	}
	return &transaction, nil
}

// GetUserSentGifts retrieves gifts sent by a user with pagination
func (r *GiftRepositoryImpl) GetUserSentGifts(ctx context.Context, userID uint, page, limit int) ([]models.GiftTransaction, int, error) {
	var transactions []models.GiftTransaction
	var count int64

	// Get total count
	if err := r.db.WithContext(ctx).Model(&models.GiftTransaction{}).
		Where("sender_id = ?", userID).
		Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated transactions
	err := r.db.WithContext(ctx).
		Where("sender_id = ?", userID).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&transactions).Error

	if err != nil {
		return nil, 0, err
	}

	return transactions, int(count), nil
}

// GetUserReceivedGifts retrieves gifts received by a user with pagination
func (r *GiftRepositoryImpl) GetUserReceivedGifts(ctx context.Context, userID uint, page, limit int) ([]models.GiftTransaction, int, error) {
	var transactions []models.GiftTransaction
	var count int64

	// Get total count
	if err := r.db.WithContext(ctx).Model(&models.GiftTransaction{}).
		Where("recipient_id = ?", userID).
		Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated transactions
	err := r.db.WithContext(ctx).
		Where("recipient_id = ?", userID).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&transactions).Error

	if err != nil {
		return nil, 0, err
	}

	return transactions, int(count), nil
}

// GetContentGifts retrieves gifts for a specific content
func (r *GiftRepositoryImpl) GetContentGifts(ctx context.Context, contentID uint, contentType string, page, limit int) ([]models.GiftTransaction, int, error) {
	var transactions []models.GiftTransaction
	var count int64

	// Get total count
	query := r.db.WithContext(ctx).Model(&models.GiftTransaction{}).Where("content_id = ?", contentID)
	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get paginated transactions
	query = r.db.WithContext(ctx).Where("content_id = ?", contentID)
	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}
	err := query.
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&transactions).Error

	if err != nil {
		return nil, 0, err
	}

	return transactions, int(count), nil
}

// GetLeaderboard retrieves the gift leaderboard
func (r *GiftRepositoryImpl) GetLeaderboard(ctx context.Context, leaderboardType, periodType string, limit int) ([]models.GiftLeaderboard, error) {
	var leaderboard []models.GiftLeaderboard

	// Get current time for period filtering
	now := time.Now()

	// Determine period start based on periodType
	var periodStart time.Time
	switch periodType {
	case "daily":
		periodStart = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	case "weekly":
		// Start from the beginning of the week (Sunday)
		daysFromSunday := int(now.Weekday())
		periodStart = time.Date(now.Year(), now.Month(), now.Day()-daysFromSunday, 0, 0, 0, 0, now.Location())
	case "monthly":
		periodStart = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	case "all_time":
		// Use a far past date for all-time
		periodStart = time.Date(2000, 1, 1, 0, 0, 0, 0, now.Location())
	default:
		return nil, fmt.Errorf("invalid period type: %s", periodType)
	}

	err := r.db.WithContext(ctx).
		Where("leaderboard_type = ? AND period_type = ? AND period_start = ?", leaderboardType, periodType, periodStart).
		Order("rank ASC").
		Limit(limit).
		Find(&leaderboard).Error

	if err != nil {
		return nil, err
	}

	return leaderboard, nil
}

// UpdateLeaderboards updates all leaderboards for the current periods
func (r *GiftRepositoryImpl) UpdateLeaderboards(ctx context.Context) error {
	// This is a complex operation that would typically:
	// 1. Calculate daily leaderboards
	// 2. Calculate weekly leaderboards
	// 3. Calculate monthly leaderboards
	// 4. Calculate all-time leaderboards
	// For both senders and recipients

	// For simplicity, we'll implement a basic version here
	now := time.Now()
	
	// Daily period
	dailyStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	dailyEnd := dailyStart.Add(24 * time.Hour)
	
	// Update sender daily leaderboard
	if err := r.updateSenderLeaderboard(ctx, "daily", dailyStart, dailyEnd); err != nil {
		return err
	}
	
	// Update recipient daily leaderboard
	if err := r.updateRecipientLeaderboard(ctx, "daily", dailyStart, dailyEnd); err != nil {
		return err
	}
	
	// Similar updates would be done for weekly, monthly, all_time periods
	// ...
	
	return nil
}

// Helper method to update sender leaderboard
func (r *GiftRepositoryImpl) updateSenderLeaderboard(ctx context.Context, periodType string, periodStart, periodEnd time.Time) error {
	// This would calculate stats for gift senders during the period
	// and update the leaderboard table
	
	// For a complete implementation, this would involve complex SQL to:
	// 1. Calculate totals per user for the period
	// 2. Assign ranks
	// 3. Calculate rank changes
	// 4. Persist to the leaderboard table
	
	// Example of the complex SQL that would be needed (pseudocode):
	/*
	WITH ranked_users AS (
		SELECT 
			sender_id,
			COUNT(*) as total_gifts_sent,
			SUM(price_paid) as total_value_sent,
			ROW_NUMBER() OVER (ORDER BY SUM(price_paid) DESC) as rank
		FROM gift_transactions
		WHERE created_at BETWEEN ? AND ?
		GROUP BY sender_id
	)
	MERGE INTO gift_leaderboards
	USING ranked_users
	ON (user_id = sender_id AND leaderboard_type = 'sender' AND period_type = ? AND period_start = ?)
	WHEN MATCHED THEN
		UPDATE SET 
			total_gifts_sent = ranked_users.total_gifts_sent,
			total_value_sent = ranked_users.total_value_sent,
			rank = ranked_users.rank,
			period_end = ?,
			updated_at = CURRENT_TIMESTAMP
	WHEN NOT MATCHED THEN
		INSERT (user_id, leaderboard_type, period_type, period_start, period_end, total_gifts_sent, total_value_sent, rank)
		VALUES (ranked_users.sender_id, 'sender', ?, ?, ?, ranked_users.total_gifts_sent, ranked_users.total_value_sent, ranked_users.rank)
	*/
	
	// For now, we'll return nil as this is a placeholder
	return nil
}

// Helper method to update recipient leaderboard
func (r *GiftRepositoryImpl) updateRecipientLeaderboard(ctx context.Context, periodType string, periodStart, periodEnd time.Time) error {
	// Similar to updateSenderLeaderboard but for gift recipients
	return nil
}

// GetUserGiftSummary retrieves the gift summary for a user
func (r *GiftRepositoryImpl) GetUserGiftSummary(ctx context.Context, userID uint) (*models.UserGiftSummary, error) {
	var summary models.UserGiftSummary

	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		First(&summary).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create a new empty summary if none exists
			summary = models.UserGiftSummary{
				UserID: userID,
			}
			return &summary, nil
		}
		return nil, err
	}

	return &summary, nil
}

// UpdateUserGiftSummary updates the gift summary for a user
func (r *GiftRepositoryImpl) UpdateUserGiftSummary(ctx context.Context, userID uint) error {
	// This would recalculate all user gift stats and update the summary
	
	// For a complete implementation, this would involve complex SQL to:
	// 1. Calculate totals for sent and received gifts
	// 2. Find most received/sent gift types
	// 3. Find top sender/recipient
	// 4. Get latest gift timestamps
	// 5. Persist to the user_gift_summary table
	
	// For now, we'll return nil as this is a placeholder
	return nil
}

// GetGiftCategoryConfigs retrieves all gift category configurations
func (r *GiftRepositoryImpl) GetGiftCategoryConfigs(ctx context.Context) ([]models.GiftCategoryConfig, error) {
	var configs []models.GiftCategoryConfig

	err := r.db.WithContext(ctx).
		Order("display_order ASC").
		Find(&configs).Error

	if err != nil {
		return nil, err
	}

	return configs, nil
}

// UpdateGiftCategoryConfig updates a gift category configuration
func (r *GiftRepositoryImpl) UpdateGiftCategoryConfig(ctx context.Context, config *models.GiftCategoryConfig) error {
	return r.db.WithContext(ctx).Save(config).Error
}