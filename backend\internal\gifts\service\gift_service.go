package service

import (
	"context"

	"github.com/greatnigeria/internal/gifts/models"
)

// GiftService defines the interface for virtual gift business logic
type GiftService interface {
	// Gift management
	CreateGift(ctx context.Context, gift *models.Gift) error
	GetGiftByID(ctx context.Context, id uint) (*models.Gift, error)
	UpdateGift(ctx context.Context, gift *models.Gift) error
	DeleteGift(ctx context.Context, id uint) error
	GetAllGifts(ctx context.Context, page, limit int) ([]models.Gift, int, error)
	GetGiftsByCategory(ctx context.Context, category models.GiftCategory, page, limit int) ([]models.Gift, int, error)
	GetFeaturedGifts(ctx context.Context, limit int) ([]models.Gift, error)
	SearchGifts(ctx context.Context, query string, page, limit int) ([]models.Gift, int, error)
	
	// Price tier management
	CreatePriceTier(ctx context.Context, tier *models.GiftPriceTier) error
	GetGiftPriceTiers(ctx context.Context, giftID uint) ([]models.GiftPriceTier, error)
	UpdatePriceTier(ctx context.Context, tier *models.GiftPriceTier) error
	DeletePriceTier(ctx context.Context, id uint) error
	
	// Gift transaction operations
	SendGift(ctx context.Context, senderID, recipientID uint, giftID uint, priceTierID uint, contentID *uint, contentType string, message string, isAnonymous bool) (*models.GiftTransaction, error)
	GetGiftTransactionByID(ctx context.Context, id uint) (*models.GiftTransaction, error)
	GetUserSentGifts(ctx context.Context, userID uint, page, limit int) ([]models.GiftTransaction, int, error)
	GetUserReceivedGifts(ctx context.Context, userID uint, page, limit int) ([]models.GiftTransaction, int, error)
	GetContentGifts(ctx context.Context, contentID uint, contentType string, page, limit int) ([]models.GiftTransaction, int, error)
	
	// Leaderboard operations
	GetLeaderboard(ctx context.Context, leaderboardType, periodType string, limit int) ([]models.GiftLeaderboard, error)
	RefreshLeaderboards(ctx context.Context) error
	
	// User gift summary
	GetUserGiftSummary(ctx context.Context, userID uint) (*models.UserGiftSummary, error)
	RefreshUserGiftSummary(ctx context.Context, userID uint) error
	
	// Gift category configuration
	GetGiftCategoryConfigs(ctx context.Context) ([]models.GiftCategoryConfig, error)
	UpdateGiftCategoryConfig(ctx context.Context, config *models.GiftCategoryConfig) error
}