package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/greatnigeria/internal/gifts/models"
	"github.com/greatnigeria/internal/gifts/repository"
	"github.com/greatnigeria/internal/payment/service"
	"github.com/greatnigeria/internal/user/repository"
	"github.com/greatnigeria/pkg/common/logger"
)

// PaymentServiceClient represents the interface for interacting with the payment service
type PaymentServiceClient interface {
	ProcessGiftPayment(ctx context.Context, userID uint, amount float64, currencyCode string, description string) (uint, error)
	RefundPayment(ctx context.Context, paymentID uint, reason string) error
}

// UserServiceClient represents the interface for interacting with the user service
type UserServiceClient interface {
	GetUserByID(ctx context.Context, userID uint) (*userrepository.User, error)
	NotifyUser(ctx context.Context, userID uint, notificationType string, data map[string]interface{}) error
	GetUserName(ctx context.Context, userID uint) string
}

// PointsServiceClient represents the interface for interacting with the points service
type PointsServiceClient interface {
	AwardPoints(ctx context.Context, userID uint, points int, reason string, referenceID string) error
}

// GiftServiceImpl implements the GiftService interface
type GiftServiceImpl struct {
	repo           repository.GiftRepository
	paymentService PaymentServiceClient
	userService    UserServiceClient
	pointsService  PointsServiceClient
	logger         *logger.Logger
}

// NewGiftService creates a new instance of the gift service
func NewGiftService(
	repo repository.GiftRepository,
	paymentService PaymentServiceClient,
	userService UserServiceClient,
	pointsService PointsServiceClient,
	logger *logger.Logger,
) GiftService {
	return &GiftServiceImpl{
		repo:           repo,
		paymentService: paymentService,
		userService:    userService,
		pointsService:  pointsService,
		logger:         logger,
	}
}

// CreateGift creates a new gift
func (s *GiftServiceImpl) CreateGift(ctx context.Context, gift *models.Gift) error {
	if gift.Name == "" {
		return errors.New("gift name is required")
	}
	
	if gift.Category == "" {
		return errors.New("gift category is required")
	}
	
	if gift.RarityLevel == "" {
		gift.RarityLevel = models.RarityCommon
	}
	
	// Set available from if not provided
	if gift.AvailableFrom.IsZero() {
		gift.AvailableFrom = time.Now()
	}
	
	return s.repo.CreateGift(ctx, gift)
}

// GetGiftByID retrieves a gift by its ID
func (s *GiftServiceImpl) GetGiftByID(ctx context.Context, id uint) (*models.Gift, error) {
	return s.repo.GetGiftByID(ctx, id)
}

// UpdateGift updates an existing gift
func (s *GiftServiceImpl) UpdateGift(ctx context.Context, gift *models.Gift) error {
	// Check if gift exists
	existingGift, err := s.repo.GetGiftByID(ctx, gift.ID)
	if err != nil {
		return err
	}
	
	// Update the gift
	return s.repo.UpdateGift(ctx, gift)
}

// DeleteGift deletes a gift by its ID
func (s *GiftServiceImpl) DeleteGift(ctx context.Context, id uint) error {
	// Check if gift exists
	_, err := s.repo.GetGiftByID(ctx, id)
	if err != nil {
		return err
	}
	
	return s.repo.DeleteGift(ctx, id)
}

// GetAllGifts retrieves all gifts with pagination
func (s *GiftServiceImpl) GetAllGifts(ctx context.Context, page, limit int) ([]models.Gift, int, error) {
	if page < 1 {
		page = 1
	}
	
	if limit < 1 || limit > 100 {
		limit = 20
	}
	
	return s.repo.GetAllGifts(ctx, page, limit)
}

// GetGiftsByCategory retrieves gifts by category with pagination
func (s *GiftServiceImpl) GetGiftsByCategory(ctx context.Context, category models.GiftCategory, page, limit int) ([]models.Gift, int, error) {
	if page < 1 {
		page = 1
	}
	
	if limit < 1 || limit > 100 {
		limit = 20
	}
	
	return s.repo.GetGiftsByCategory(ctx, category, page, limit)
}

// GetFeaturedGifts retrieves featured gifts
func (s *GiftServiceImpl) GetFeaturedGifts(ctx context.Context, limit int) ([]models.Gift, error) {
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	return s.repo.GetFeaturedGifts(ctx, limit)
}

// SearchGifts searches for gifts by name or description
func (s *GiftServiceImpl) SearchGifts(ctx context.Context, query string, page, limit int) ([]models.Gift, int, error) {
	if page < 1 {
		page = 1
	}
	
	if limit < 1 || limit > 100 {
		limit = 20
	}
	
	return s.repo.SearchGifts(ctx, query, page, limit)
}

// CreatePriceTier creates a new price tier for a gift
func (s *GiftServiceImpl) CreatePriceTier(ctx context.Context, tier *models.GiftPriceTier) error {
	// Check if gift exists
	_, err := s.repo.GetGiftByID(ctx, tier.GiftID)
	if err != nil {
		return err
	}
	
	// Validate price
	if tier.PriceNaira <= 0 {
		return errors.New("price must be greater than zero")
	}
	
	return s.repo.CreatePriceTier(ctx, tier)
}

// GetGiftPriceTiers retrieves all price tiers for a gift
func (s *GiftServiceImpl) GetGiftPriceTiers(ctx context.Context, giftID uint) ([]models.GiftPriceTier, error) {
	// Check if gift exists
	_, err := s.repo.GetGiftByID(ctx, giftID)
	if err != nil {
		return nil, err
	}
	
	return s.repo.GetGiftPriceTiers(ctx, giftID)
}

// UpdatePriceTier updates an existing price tier
func (s *GiftServiceImpl) UpdatePriceTier(ctx context.Context, tier *models.GiftPriceTier) error {
	// Validate price
	if tier.PriceNaira <= 0 {
		return errors.New("price must be greater than zero")
	}
	
	return s.repo.UpdatePriceTier(ctx, tier)
}

// DeletePriceTier deletes a price tier by its ID
func (s *GiftServiceImpl) DeletePriceTier(ctx context.Context, id uint) error {
	return s.repo.DeletePriceTier(ctx, id)
}

// SendGift sends a gift from one user to another
func (s *GiftServiceImpl) SendGift(
	ctx context.Context,
	senderID, recipientID uint,
	giftID uint,
	priceTierID uint,
	contentID *uint,
	contentType string,
	message string,
	isAnonymous bool,
) (*models.GiftTransaction, error) {
	// 1. Validate gift exists
	gift, err := s.repo.GetGiftByID(ctx, giftID)
	if err != nil {
		return nil, fmt.Errorf("gift not found: %w", err)
	}
	
	// 2. Validate gift is active and available
	if !gift.IsActive {
		return nil, errors.New("gift is not currently available")
	}
	
	now := time.Now()
	if now.Before(gift.AvailableFrom) {
		return nil, errors.New("gift is not yet available")
	}
	
	if gift.AvailableTo != nil && now.After(*gift.AvailableTo) {
		return nil, errors.New("gift is no longer available")
	}
	
	// 3. Validate price tier
	tiers, err := s.repo.GetGiftPriceTiers(ctx, giftID)
	if err != nil {
		return nil, fmt.Errorf("failed to get price tiers: %w", err)
	}
	
	var selectedTier *models.GiftPriceTier
	for _, tier := range tiers {
		if tier.ID == priceTierID {
			selectedTier = &tier
			break
		}
	}
	
	if selectedTier == nil {
		return nil, errors.New("invalid price tier")
	}
	
	// 4. Validate sender and recipient exist
	_, err = s.userService.GetUserByID(ctx, senderID)
	if err != nil {
		return nil, fmt.Errorf("sender not found: %w", err)
	}
	
	_, err = s.userService.GetUserByID(ctx, recipientID)
	if err != nil {
		return nil, fmt.Errorf("recipient not found: %w", err)
	}
	
	// 5. Process payment
	paymentDesc := fmt.Sprintf("Gift: %s (to: %s)", gift.Name, s.userService.GetUserName(ctx, recipientID))
	paymentID, err := s.paymentService.ProcessGiftPayment(ctx, senderID, selectedTier.PriceNaira, "NGN", paymentDesc)
	if err != nil {
		return nil, fmt.Errorf("payment failed: %w", err)
	}
	
	// 6. Create transaction record
	// Calculate revenue sharing (default 50%)
	revenueSharePercent := 50.0 // Get from config or category settings
	
	// Use category config if available
	configs, err := s.repo.GetGiftCategoryConfigs(ctx)
	if err == nil {
		for _, config := range configs {
			if config.Category == gift.Category {
				revenueSharePercent = config.RevenueSharePercent
				break
			}
		}
	}
	
	creatorAmount := selectedTier.PriceNaira * (revenueSharePercent / 100.0)
	platformAmount := selectedTier.PriceNaira - creatorAmount
	
	transaction := &models.GiftTransaction{
		GiftID:                giftID,
		SenderID:              senderID,
		RecipientID:           recipientID,
		ContentID:             contentID,
		ContentType:           contentType,
		PriceTierID:           priceTierID,
		PricePaid:             selectedTier.PriceNaira,
		CurrencyCode:          "NGN",
		PaymentID:             &paymentID,
		Message:               message,
		IsAnonymous:           isAnonymous,
		IsPublic:              true, // Default to public
		CreatorRevenuePercent: revenueSharePercent,
		CreatorRevenueAmount:  creatorAmount,
		PlatformRevenueAmount: platformAmount,
		Status:                "completed",
	}
	
	if err := s.repo.CreateGiftTransaction(ctx, transaction); err != nil {
		// If transaction record fails, attempt to refund payment
		s.paymentService.RefundPayment(ctx, paymentID, "Failed to record gift transaction")
		return nil, fmt.Errorf("failed to record transaction: %w", err)
	}
	
	// 7. Award points to recipient (10 points per Naira)
	pointsAmount := int(selectedTier.PriceNaira * 10)
	s.pointsService.AwardPoints(ctx, recipientID, pointsAmount, "gift_received", fmt.Sprintf("%d", transaction.ID))
	
	// 8. Notify recipient
	s.userService.NotifyUser(ctx, recipientID, "gift_received", map[string]interface{}{
		"giftId":        giftID,
		"giftName":      gift.Name,
		"senderId":      senderID,
		"senderName":    isAnonymous ? "Anonymous" : s.userService.GetUserName(ctx, senderID),
		"contentId":     contentID,
		"contentType":   contentType,
		"pricePaid":     selectedTier.PriceNaira,
		"currencyCode":  "NGN",
		"message":       message,
		"transactionId": transaction.ID,
	})
	
	// 9. Update user gift summaries
	go s.RefreshUserGiftSummary(context.Background(), senderID)
	go s.RefreshUserGiftSummary(context.Background(), recipientID)
	
	return transaction, nil
}

// GetGiftTransactionByID retrieves a gift transaction by its ID
func (s *GiftServiceImpl) GetGiftTransactionByID(ctx context.Context, id uint) (*models.GiftTransaction, error) {
	return s.repo.GetGiftTransactionByID(ctx, id)
}

// GetUserSentGifts retrieves gifts sent by a user with pagination
func (s *GiftServiceImpl) GetUserSentGifts(ctx context.Context, userID uint, page, limit int) ([]models.GiftTransaction, int, error) {
	if page < 1 {
		page = 1
	}
	
	if limit < 1 || limit > 100 {
		limit = 20
	}
	
	return s.repo.GetUserSentGifts(ctx, userID, page, limit)
}

// GetUserReceivedGifts retrieves gifts received by a user with pagination
func (s *GiftServiceImpl) GetUserReceivedGifts(ctx context.Context, userID uint, page, limit int) ([]models.GiftTransaction, int, error) {
	if page < 1 {
		page = 1
	}
	
	if limit < 1 || limit > 100 {
		limit = 20
	}
	
	return s.repo.GetUserReceivedGifts(ctx, userID, page, limit)
}

// GetContentGifts retrieves gifts for a specific content
func (s *GiftServiceImpl) GetContentGifts(ctx context.Context, contentID uint, contentType string, page, limit int) ([]models.GiftTransaction, int, error) {
	if page < 1 {
		page = 1
	}
	
	if limit < 1 || limit > 100 {
		limit = 20
	}
	
	return s.repo.GetContentGifts(ctx, contentID, contentType, page, limit)
}

// GetLeaderboard retrieves the gift leaderboard
func (s *GiftServiceImpl) GetLeaderboard(ctx context.Context, leaderboardType, periodType string, limit int) ([]models.GiftLeaderboard, error) {
	if limit < 1 || limit > 100 {
		limit = 20
	}
	
	// Validate leaderboard type
	if leaderboardType != "sender" && leaderboardType != "recipient" {
		return nil, errors.New("invalid leaderboard type: must be 'sender' or 'recipient'")
	}
	
	// Validate period type
	validPeriods := map[string]bool{
		"daily":    true,
		"weekly":   true,
		"monthly":  true,
		"all_time": true,
	}
	
	if !validPeriods[periodType] {
		return nil, errors.New("invalid period type: must be 'daily', 'weekly', 'monthly', or 'all_time'")
	}
	
	return s.repo.GetLeaderboard(ctx, leaderboardType, periodType, limit)
}

// RefreshLeaderboards updates all leaderboards
func (s *GiftServiceImpl) RefreshLeaderboards(ctx context.Context) error {
	return s.repo.UpdateLeaderboards(ctx)
}

// GetUserGiftSummary retrieves the gift summary for a user
func (s *GiftServiceImpl) GetUserGiftSummary(ctx context.Context, userID uint) (*models.UserGiftSummary, error) {
	return s.repo.GetUserGiftSummary(ctx, userID)
}

// RefreshUserGiftSummary updates the gift summary for a user
func (s *GiftServiceImpl) RefreshUserGiftSummary(ctx context.Context, userID uint) error {
	return s.repo.UpdateUserGiftSummary(ctx, userID)
}

// GetGiftCategoryConfigs retrieves all gift category configurations
func (s *GiftServiceImpl) GetGiftCategoryConfigs(ctx context.Context) ([]models.GiftCategoryConfig, error) {
	return s.repo.GetGiftCategoryConfigs(ctx)
}

// UpdateGiftCategoryConfig updates a gift category configuration
func (s *GiftServiceImpl) UpdateGiftCategoryConfig(ctx context.Context, config *models.GiftCategoryConfig) error {
	return s.repo.UpdateGiftCategoryConfig(ctx, config)
}