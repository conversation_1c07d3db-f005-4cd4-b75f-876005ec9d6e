package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/greatnigeria/internal/livestream/service"
	"github.com/greatnigeria/pkg/common/logger"
)

// LiveStreamGiftHandler handles HTTP requests for live stream gift operations
type LiveStreamGiftHandler struct {
	service service.LiveStreamGiftService
	logger  *logger.Logger
}

// NewLiveStreamGiftHandler creates a new instance of the live stream gift handler
func NewLiveStreamGiftHandler(service service.LiveStreamGiftService, logger *logger.Logger) *LiveStreamGiftHandler {
	return &LiveStreamGiftHandler{
		service: service,
		logger:  logger,
	}
}

// SendGift sends a gift during a live stream
func (h *LiveStreamGiftHandler) SendGift(c *gin.Context) {
	// Get user ID from authenticated user
	senderID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Parse request body
	var request struct {
		StreamID     uint    `json:"streamId" binding:"required"`
		RecipientID  uint    `json:"recipientId" binding:"required"`
		GiftID       uint    `json:"giftId" binding:"required"`
		CoinsAmount  float64 `json:"coinsAmount" binding:"required"`
		Message      string  `json:"message"`
		IsAnonymous  bool    `json:"isAnonymous"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// Validate coins amount
	if request.CoinsAmount <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Coins amount must be greater than zero"})
		return
	}
	
	// Send gift
	gift, err := h.service.SendGift(
		c.Request.Context(),
		request.StreamID,
		senderID.(uint),
		request.RecipientID,
		request.GiftID,
		request.CoinsAmount,
		request.Message,
		request.IsAnonymous,
	)
	
	if err != nil {
		h.logger.Errorf("Failed to send gift: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, gift)
}

// GetStreamGifts retrieves gifts for a stream
func (h *LiveStreamGiftHandler) GetStreamGifts(c *gin.Context) {
	// Get stream ID from URL parameter
	streamIDStr := c.Param("streamId")
	streamID, err := strconv.ParseUint(streamIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid stream ID"})
		return
	}
	
	// Get page and limit from query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	// Validate page and limit
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	// Get gifts
	gifts, total, err := h.service.GetStreamGifts(c.Request.Context(), uint(streamID), page, limit)
	if err != nil {
		h.logger.Errorf("Failed to get gifts for stream %d: %v", streamID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get gifts"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"gifts": gifts,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + limit - 1) / limit,
		},
	})
}

// GetUserSentGifts retrieves gifts sent by a user
func (h *LiveStreamGiftHandler) GetUserSentGifts(c *gin.Context) {
	// Get user ID from URL parameter
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}
	
	// Get authenticated user ID
	authUserID, exists := c.Get("userId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	
	// Only allow users to view their own sent gifts
	if authUserID.(uint) != uint(userID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only view your own sent gifts"})
		return
	}
	
	// Get page and limit from query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	// Validate page and limit
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	// Get gifts
	gifts, total, err := h.service.GetUserSentGifts(c.Request.Context(), uint(userID), page, limit)
	if err != nil {
		h.logger.Errorf("Failed to get sent gifts for user %d: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get sent gifts"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"gifts": gifts,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + limit - 1) / limit,
		},
	})
}

// GetUserReceivedGifts retrieves gifts received by a user
func (h *LiveStreamGiftHandler) GetUserReceivedGifts(c *gin.Context) {
	// Get user ID from URL parameter
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}
	
	// Get page and limit from query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	// Validate page and limit
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	// Get gifts
	gifts, total, err := h.service.GetUserReceivedGifts(c.Request.Context(), uint(userID), page, limit)
	if err != nil {
		h.logger.Errorf("Failed to get received gifts for user %d: %v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get received gifts"})
		return
	}
	
	// Filter out anonymous gifts for non-recipients
	authUserID, exists := c.Get("userId")
	if !exists || authUserID.(uint) != uint(userID) {
		// Remove sender information from anonymous gifts
		for i := range gifts {
			if gifts[i].IsAnonymous {
				gifts[i].SenderID = 0
			}
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"gifts": gifts,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + limit - 1) / limit,
		},
	})
}
