package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/greatnigeria/pkg/common/logger"
)

const (
	// Time allowed to write a message to the peer
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer
	pongWait = 60 * time.Second

	// Send pings to peer with this period (must be less than pongWait)
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer
	maxMessageSize = 512
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow all origins in development
	},
}

// Message represents a WebSocket message
type Message struct {
	Type    string      `json:"type"`
	Content interface{} `json:"content"`
	From    uint        `json:"from,omitempty"`
	To      *uint       `json:"to,omitempty"`
	StreamID uint       `json:"streamId,omitempty"`
	Time    time.Time   `json:"time"`
}

// Client represents a connected WebSocket client
type Client struct {
	hub      *Hub
	conn     *websocket.Conn
	send     chan []byte
	userID   uint
	streamID uint
}

// Hub maintains the set of active clients and broadcasts messages
type Hub struct {
	// Registered clients
	clients map[*Client]bool

	// Inbound messages from clients
	broadcast chan []byte

	// Register requests from clients
	register chan *Client

	// Unregister requests from clients
	unregister chan *Client

	// Stream-specific clients
	streamClients map[uint]map[*Client]bool

	// User-specific clients (for private messages)
	userClients map[uint]*Client

	// Logger
	logger *logger.Logger
}

// NewWebSocketHub creates a new WebSocket hub
func NewWebSocketHub(logger *logger.Logger) *Hub {
	return &Hub{
		broadcast:     make(chan []byte),
		register:      make(chan *Client),
		unregister:    make(chan *Client),
		clients:       make(map[*Client]bool),
		streamClients: make(map[uint]map[*Client]bool),
		userClients:   make(map[uint]*Client),
		logger:        logger,
	}
}

// Run starts the WebSocket hub
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client] = true
			
			// Register in stream-specific clients
			if client.streamID > 0 {
				if _, ok := h.streamClients[client.streamID]; !ok {
					h.streamClients[client.streamID] = make(map[*Client]bool)
				}
				h.streamClients[client.streamID][client] = true
			}
			
			// Register in user-specific clients
			if client.userID > 0 {
				h.userClients[client.userID] = client
			}
			
			// Send connection confirmation
			msg := Message{
				Type:    "connected",
				Content: "Connected to WebSocket server",
				Time:    time.Now(),
			}
			
			jsonMsg, _ := json.Marshal(msg)
			client.send <- jsonMsg
			
			h.logger.Infof("Client connected: user %d, stream %d", client.userID, client.streamID)
			
		case client := <-h.unregister:
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
				
				// Unregister from stream-specific clients
				if client.streamID > 0 {
					if _, ok := h.streamClients[client.streamID]; ok {
						delete(h.streamClients[client.streamID], client)
						
						// Clean up empty stream maps
						if len(h.streamClients[client.streamID]) == 0 {
							delete(h.streamClients, client.streamID)
						}
					}
				}
				
				// Unregister from user-specific clients
				if client.userID > 0 {
					if h.userClients[client.userID] == client {
						delete(h.userClients, client.userID)
					}
				}
				
				h.logger.Infof("Client disconnected: user %d, stream %d", client.userID, client.streamID)
			}
			
		case message := <-h.broadcast:
			// Parse message to determine routing
			var msg Message
			if err := json.Unmarshal(message, &msg); err != nil {
				h.logger.Warnf("Failed to parse WebSocket message: %v", err)
				continue
			}
			
			// Route message based on type
			switch msg.Type {
			case "stream":
				// Send to all clients in the stream
				if msg.StreamID > 0 {
					if clients, ok := h.streamClients[msg.StreamID]; ok {
						for client := range clients {
							select {
							case client.send <- message:
							default:
								close(client.send)
								delete(clients, client)
								delete(h.clients, client)
								
								// Clean up user clients
								if client.userID > 0 && h.userClients[client.userID] == client {
									delete(h.userClients, client.userID)
								}
							}
						}
					}
				}
				
			case "private":
				// Send to specific user
				if msg.To != nil && *msg.To > 0 {
					if client, ok := h.userClients[*msg.To]; ok {
						select {
						case client.send <- message:
						default:
							close(client.send)
							delete(h.clients, client)
							delete(h.userClients, *msg.To)
							
							// Clean up stream clients
							if client.streamID > 0 {
								if streamClients, ok := h.streamClients[client.streamID]; ok {
									delete(streamClients, client)
									
									// Clean up empty stream maps
									if len(streamClients) == 0 {
										delete(h.streamClients, client.streamID)
									}
								}
							}
						}
					}
				}
				
			case "gift":
				// Send to all clients in the stream
				if msg.StreamID > 0 {
					if clients, ok := h.streamClients[msg.StreamID]; ok {
						for client := range clients {
							select {
							case client.send <- message:
							default:
								close(client.send)
								delete(clients, client)
								delete(h.clients, client)
								
								// Clean up user clients
								if client.userID > 0 && h.userClients[client.userID] == client {
									delete(h.userClients, client.userID)
								}
							}
						}
					}
				}
				
			case "broadcast":
				// Send to all connected clients
				for client := range h.clients {
					select {
					case client.send <- message:
					default:
						close(client.send)
						delete(h.clients, client)
						
						// Clean up stream clients
						if client.streamID > 0 {
							if streamClients, ok := h.streamClients[client.streamID]; ok {
								delete(streamClients, client)
								
								// Clean up empty stream maps
								if len(streamClients) == 0 {
									delete(h.streamClients, client.streamID)
								}
							}
						}
						
						// Clean up user clients
						if client.userID > 0 && h.userClients[client.userID] == client {
							delete(h.userClients, client.userID)
						}
					}
				}
				
			default:
				h.logger.Warnf("Unknown message type: %s", msg.Type)
			}
		}
	}
}

// readPump pumps messages from the WebSocket connection to the hub
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()
	
	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error { c.conn.SetReadDeadline(time.Now().Add(pongWait)); return nil })
	
	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.hub.logger.Warnf("WebSocket error: %v", err)
			}
			break
		}
		
		// Parse message to add metadata
		var msg Message
		if err := json.Unmarshal(message, &msg); err != nil {
			c.hub.logger.Warnf("Failed to parse client message: %v", err)
			continue
		}
		
		// Add client metadata
		msg.From = c.userID
		msg.Time = time.Now()
		
		// If stream ID is not set in the message, use the client's stream ID
		if msg.StreamID == 0 {
			msg.StreamID = c.streamID
		}
		
		// Re-marshal with added metadata
		message, err = json.Marshal(msg)
		if err != nil {
			c.hub.logger.Warnf("Failed to marshal message: %v", err)
			continue
		}
		
		c.hub.broadcast <- message
	}
}

// writePump pumps messages from the hub to the WebSocket connection
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()
	
	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// The hub closed the channel
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}
			
			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)
			
			// Add queued messages to the current WebSocket message
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}
			
			if err := w.Close(); err != nil {
				return
			}
			
		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// ServeWs handles WebSocket requests from clients
func ServeWs(hub *Hub, c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		hub.logger.Errorf("Failed to upgrade connection: %v", err)
		return
	}
	
	// Get user ID from query parameter or JWT token
	userIDStr := c.Query("userId")
	var userID uint
	if userIDStr != "" {
		_, err := fmt.Sscanf(userIDStr, "%d", &userID)
		if err != nil {
			hub.logger.Warnf("Invalid user ID: %s", userIDStr)
			userID = 0
		}
	} else {
		// In a real implementation, extract user ID from JWT token
		userID = 0
	}
	
	// Get stream ID from query parameter
	streamIDStr := c.Query("streamId")
	var streamID uint
	if streamIDStr != "" {
		_, err := fmt.Sscanf(streamIDStr, "%d", &streamID)
		if err != nil {
			hub.logger.Warnf("Invalid stream ID: %s", streamIDStr)
			streamID = 0
		}
	}
	
	client := &Client{
		hub:      hub,
		conn:     conn,
		send:     make(chan []byte, 256),
		userID:   userID,
		streamID: streamID,
	}
	
	client.hub.register <- client
	
	// Allow collection of memory referenced by the caller by doing all work in new goroutines
	go client.writePump()
	go client.readPump()
}
