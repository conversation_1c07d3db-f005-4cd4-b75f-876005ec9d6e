package models

import (
	"time"

	"gorm.io/gorm"
)

// VirtualCurrency represents a user's virtual currency balance
type VirtualCurrency struct {
	gorm.Model
	UserID      uint    `json:"userId" gorm:"uniqueIndex"`
	Balance     float64 `json:"balance" gorm:"default:0"`
	TotalBought float64 `json:"totalBought" gorm:"default:0"`
	TotalSpent  float64 `json:"totalSpent" gorm:"default:0"`
}

// VirtualCurrencyTransaction represents a transaction in the virtual currency system
type VirtualCurrencyTransaction struct {
	gorm.Model
	UserID          uint    `json:"userId" gorm:"index"`
	TransactionType string  `json:"transactionType" gorm:"type:varchar(50)"` // purchase, gift_sent, gift_received, refund, bonus
	Amount          float64 `json:"amount"`
	BalanceBefore   float64 `json:"balanceBefore"`
	BalanceAfter    float64 `json:"balanceAfter"`
	Description     string  `json:"description" gorm:"type:text"`
	ReferenceID     string  `json:"referenceId" gorm:"type:varchar(100)"`
	PaymentID       *uint   `json:"paymentId"`
	Status          string  `json:"status" gorm:"type:varchar(50);default:'completed'"`
}

// CoinPackage represents a purchasable package of virtual coins
type CoinPackage struct {
	gorm.Model
	Name           string  `json:"name" gorm:"type:varchar(100)"`
	CoinsAmount    float64 `json:"coinsAmount"`
	PriceNaira     float64 `json:"priceNaira"`
	BonusCoins     float64 `json:"bonusCoins" gorm:"default:0"`
	IsActive       bool    `json:"isActive" gorm:"default:true"`
	DisplayOrder   int     `json:"displayOrder" gorm:"default:0"`
	IsPromotional  bool    `json:"isPromotional" gorm:"default:false"`
	PromotionEnds  *time.Time `json:"promotionEnds"`
	Description    string  `json:"description" gorm:"type:text"`
	ImageURL       string  `json:"imageUrl" gorm:"type:varchar(255)"`
}

// LiveStream represents a live streaming session
type LiveStream struct {
	gorm.Model
	CreatorID       uint      `json:"creatorId" gorm:"index"`
	Title           string    `json:"title" gorm:"type:varchar(255)"`
	Description     string    `json:"description" gorm:"type:text"`
	ThumbnailURL    string    `json:"thumbnailUrl" gorm:"type:varchar(255)"`
	Status          string    `json:"status" gorm:"type:varchar(50);default:'scheduled'"` // scheduled, live, ended
	ScheduledStart  time.Time `json:"scheduledStart"`
	ActualStart     *time.Time `json:"actualStart"`
	EndTime         *time.Time `json:"endTime"`
	ViewerCount     int       `json:"viewerCount" gorm:"default:0"`
	PeakViewerCount int       `json:"peakViewerCount" gorm:"default:0"`
	TotalGiftsValue float64   `json:"totalGiftsValue" gorm:"default:0"`
	StreamKey       string    `json:"streamKey" gorm:"type:varchar(100);uniqueIndex"`
	PlaybackURL     string    `json:"playbackUrl" gorm:"type:varchar(255)"`
	IsPrivate       bool      `json:"isPrivate" gorm:"default:false"`
	AllowGifting    bool      `json:"allowGifting" gorm:"default:true"`
	Categories      string    `json:"categories" gorm:"type:varchar(255)"`
	Tags            string    `json:"tags" gorm:"type:varchar(255)"`
}

// LiveStreamViewer represents a viewer of a live stream
type LiveStreamViewer struct {
	gorm.Model
	StreamID  uint      `json:"streamId" gorm:"index:idx_stream_viewer"`
	UserID    uint      `json:"userId" gorm:"index:idx_stream_viewer"`
	JoinTime  time.Time `json:"joinTime"`
	LeaveTime *time.Time `json:"leaveTime"`
	Duration  int       `json:"duration" gorm:"default:0"` // in seconds
	IsActive  bool      `json:"isActive" gorm:"default:true"`
}

// LiveStreamGift represents a gift sent during a live stream
type LiveStreamGift struct {
	gorm.Model
	StreamID       uint    `json:"streamId" gorm:"index"`
	SenderID       uint    `json:"senderId" gorm:"index"`
	RecipientID    uint    `json:"recipientId" gorm:"index"`
	GiftID         uint    `json:"giftId"`
	GiftName       string  `json:"giftName" gorm:"type:varchar(100)"`
	CoinsAmount    float64 `json:"coinsAmount"`
	NairaValue     float64 `json:"nairaValue"`
	Message        string  `json:"message" gorm:"type:text"`
	IsAnonymous    bool    `json:"isAnonymous" gorm:"default:false"`
	IsHighlighted  bool    `json:"isHighlighted" gorm:"default:false"`
	ComboCount     int     `json:"comboCount" gorm:"default:1"`
	
	// Revenue sharing
	CreatorRevenuePercent float64 `json:"creatorRevenuePercent" gorm:"default:70.0"`
	CreatorRevenueAmount  float64 `json:"creatorRevenueAmount"`
	PlatformRevenueAmount float64 `json:"platformRevenueAmount"`
}

// GifterRanking represents the ranking of gifters in a stream or globally
type GifterRanking struct {
	gorm.Model
	UserID         uint      `json:"userId" gorm:"uniqueIndex:idx_gifter_ranking"`
	StreamID       *uint     `json:"streamId" gorm:"uniqueIndex:idx_gifter_ranking"` // null for global rankings
	RankingPeriod  string    `json:"rankingPeriod" gorm:"type:varchar(50);uniqueIndex:idx_gifter_ranking"` // daily, weekly, monthly, all_time
	PeriodStart    time.Time `json:"periodStart" gorm:"uniqueIndex:idx_gifter_ranking"`
	PeriodEnd      time.Time `json:"periodEnd"`
	TotalGifts     int       `json:"totalGifts" gorm:"default:0"`
	TotalCoins     float64   `json:"totalCoins" gorm:"default:0"`
	TotalNairaValue float64  `json:"totalNairaValue" gorm:"default:0"`
	Rank           int       `json:"rank" gorm:"default:0"`
	PreviousRank   *int      `json:"previousRank"`
	BadgeLevel     string    `json:"badgeLevel" gorm:"type:varchar(50);default:'bronze'"` // bronze, silver, gold, platinum, diamond
}

// CreatorRevenue represents the revenue earned by a creator from gifts
type CreatorRevenue struct {
	gorm.Model
	CreatorID      uint      `json:"creatorId" gorm:"index"`
	StreamID       *uint     `json:"streamId" gorm:"index"` // null for non-stream gifts
	Period         string    `json:"period" gorm:"type:varchar(50)"` // daily, weekly, monthly
	PeriodStart    time.Time `json:"periodStart"`
	PeriodEnd      time.Time `json:"periodEnd"`
	TotalGifts     int       `json:"totalGifts" gorm:"default:0"`
	TotalCoins     float64   `json:"totalCoins" gorm:"default:0"`
	TotalNairaValue float64  `json:"totalNairaValue" gorm:"default:0"`
	PlatformFee    float64   `json:"platformFee" gorm:"default:0"`
	NetRevenue     float64   `json:"netRevenue" gorm:"default:0"`
	IsPaid         bool      `json:"isPaid" gorm:"default:false"`
	PaymentDate    *time.Time `json:"paymentDate"`
	PaymentReference string   `json:"paymentReference" gorm:"type:varchar(100)"`
}

// WithdrawalRequest represents a request to withdraw earnings
type WithdrawalRequest struct {
	gorm.Model
	CreatorID      uint    `json:"creatorId" gorm:"index"`
	Amount         float64 `json:"amount"`
	Status         string  `json:"status" gorm:"type:varchar(50);default:'pending'"` // pending, approved, rejected, completed
	BankName       string  `json:"bankName" gorm:"type:varchar(100)"`
	AccountNumber  string  `json:"accountNumber" gorm:"type:varchar(50)"`
	AccountName    string  `json:"accountName" gorm:"type:varchar(100)"`
	ProcessedDate  *time.Time `json:"processedDate"`
	ProcessedBy    *uint   `json:"processedBy"`
	Notes          string  `json:"notes" gorm:"type:text"`
	TransactionReference string `json:"transactionReference" gorm:"type:varchar(100)"`
}

// FraudDetectionLog represents a log entry for potential fraud detection
type FraudDetectionLog struct {
	gorm.Model
	UserID         uint    `json:"userId" gorm:"index"`
	EventType      string  `json:"eventType" gorm:"type:varchar(100)"`
	RiskScore      float64 `json:"riskScore"`
	Details        string  `json:"details" gorm:"type:text"`
	IPAddress      string  `json:"ipAddress" gorm:"type:varchar(50)"`
	DeviceInfo     string  `json:"deviceInfo" gorm:"type:text"`
	ActionTaken    string  `json:"actionTaken" gorm:"type:varchar(100);default:'none'"` // none, flag, block, review
	ReviewedBy     *uint   `json:"reviewedBy"`
	ReviewNotes    string  `json:"reviewNotes" gorm:"type:text"`
}
