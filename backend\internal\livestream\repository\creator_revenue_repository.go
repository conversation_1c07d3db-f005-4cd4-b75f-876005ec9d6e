package repository

import (
	"context"
	"time"

	"github.com/greatnigeria/internal/livestream/models"
	"gorm.io/gorm"
)

// CreatorRevenue represents the revenue earned by a creator from gifts
type CreatorRevenue struct {
	gorm.Model
	CreatorID      uint      `json:"creatorId" gorm:"index"`
	StreamID       *uint     `json:"streamId" gorm:"index"` // null for non-stream gifts
	Period         string    `json:"period" gorm:"type:varchar(50)"` // daily, weekly, monthly
	PeriodStart    time.Time `json:"periodStart"`
	PeriodEnd      time.Time `json:"periodEnd"`
	TotalGifts     int       `json:"totalGifts" gorm:"default:0"`
	TotalCoins     float64   `json:"totalCoins" gorm:"default:0"`
	TotalNairaValue float64  `json:"totalNairaValue" gorm:"default:0"`
	PlatformFee    float64   `json:"platformFee" gorm:"default:0"`
	NetRevenue     float64   `json:"netRevenue" gorm:"default:0"`
	IsPaid         bool      `json:"isPaid" gorm:"default:false"`
	PaymentDate    *time.Time `json:"paymentDate"`
	PaymentReference string   `json:"paymentReference" gorm:"type:varchar(100)"`
}

// WithdrawalRequest represents a request to withdraw earnings
type WithdrawalRequest struct {
	gorm.Model
	CreatorID      uint    `json:"creatorId" gorm:"index"`
	Amount         float64 `json:"amount"`
	Status         string  `json:"status" gorm:"type:varchar(50);default:'pending'"` // pending, approved, rejected, completed
	BankName       string  `json:"bankName" gorm:"type:varchar(100)"`
	AccountNumber  string  `json:"accountNumber" gorm:"type:varchar(50)"`
	AccountName    string  `json:"accountName" gorm:"type:varchar(100)"`
	ProcessedDate  *time.Time `json:"processedDate"`
	ProcessedBy    *uint   `json:"processedBy"`
	Notes          string  `json:"notes" gorm:"type:text"`
	TransactionReference string `json:"transactionReference" gorm:"type:varchar(100)"`
}

// CreatorRevenueRepository defines the interface for creator revenue data access
type CreatorRevenueRepository interface {
	// Revenue CRUD operations
	CreateRevenue(ctx context.Context, revenue *CreatorRevenue) error
	GetRevenueByID(ctx context.Context, id uint) (*CreatorRevenue, error)
	UpdateRevenue(ctx context.Context, revenue *CreatorRevenue) error
	
	// Revenue queries
	GetCreatorRevenue(ctx context.Context, creatorID uint, period string, page, limit int) ([]CreatorRevenue, int, error)
	GetStreamRevenue(ctx context.Context, streamID uint) (*CreatorRevenue, error)
	GetUnpaidRevenue(ctx context.Context, creatorID uint) (float64, error)
	
	// Revenue operations
	UpdateCreatorRevenue(ctx context.Context, creatorID uint) error
	MarkRevenuePaid(ctx context.Context, revenueIDs []uint, paymentReference string) error
	
	// Withdrawal operations
	CreateWithdrawalRequest(ctx context.Context, request *WithdrawalRequest) error
	GetWithdrawalRequestByID(ctx context.Context, id uint) (*WithdrawalRequest, error)
	UpdateWithdrawalRequest(ctx context.Context, request *WithdrawalRequest) error
	GetPendingWithdrawalRequests(ctx context.Context, page, limit int) ([]WithdrawalRequest, int, error)
	GetCreatorWithdrawalRequests(ctx context.Context, creatorID uint, page, limit int) ([]WithdrawalRequest, int, error)
}

// CreatorRevenueRepositoryImpl implements the CreatorRevenueRepository interface
type CreatorRevenueRepositoryImpl struct {
	db *gorm.DB
}

// NewCreatorRevenueRepository creates a new instance of the creator revenue repository
func NewCreatorRevenueRepository(db *gorm.DB) CreatorRevenueRepository {
	return &CreatorRevenueRepositoryImpl{
		db: db,
	}
}

// CreateRevenue creates a new creator revenue record
func (r *CreatorRevenueRepositoryImpl) CreateRevenue(ctx context.Context, revenue *CreatorRevenue) error {
	return r.db.WithContext(ctx).Create(revenue).Error
}

// GetRevenueByID retrieves a creator revenue record by its ID
func (r *CreatorRevenueRepositoryImpl) GetRevenueByID(ctx context.Context, id uint) (*CreatorRevenue, error) {
	var revenue CreatorRevenue
	if err := r.db.WithContext(ctx).First(&revenue, id).Error; err != nil {
		return nil, err
	}
	return &revenue, nil
}

// UpdateRevenue updates a creator revenue record
func (r *CreatorRevenueRepositoryImpl) UpdateRevenue(ctx context.Context, revenue *CreatorRevenue) error {
	return r.db.WithContext(ctx).Save(revenue).Error
}

// GetCreatorRevenue retrieves revenue records for a creator with pagination
func (r *CreatorRevenueRepositoryImpl) GetCreatorRevenue(ctx context.Context, creatorID uint, period string, page, limit int) ([]CreatorRevenue, int, error) {
	var revenues []CreatorRevenue
	var count int64
	
	query := r.db.WithContext(ctx).Model(&CreatorRevenue{}).Where("creator_id = ?", creatorID)
	
	if period != "" {
		query = query.Where("period = ?", period)
	}
	
	// Get total count
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated revenues
	if err := query.
		Order("period_start DESC").
		Offset(offset).
		Limit(limit).
		Find(&revenues).Error; err != nil {
		return nil, 0, err
	}
	
	return revenues, int(count), nil
}

// GetStreamRevenue retrieves revenue for a specific stream
func (r *CreatorRevenueRepositoryImpl) GetStreamRevenue(ctx context.Context, streamID uint) (*CreatorRevenue, error) {
	var revenue CreatorRevenue
	if err := r.db.WithContext(ctx).Where("stream_id = ?", streamID).First(&revenue).Error; err != nil {
		return nil, err
	}
	return &revenue, nil
}

// GetUnpaidRevenue retrieves the total unpaid revenue for a creator
func (r *CreatorRevenueRepositoryImpl) GetUnpaidRevenue(ctx context.Context, creatorID uint) (float64, error) {
	type Result struct {
		TotalRevenue float64
	}
	
	var result Result
	
	if err := r.db.WithContext(ctx).Model(&CreatorRevenue{}).
		Select("SUM(net_revenue) as total_revenue").
		Where("creator_id = ? AND is_paid = ?", creatorID, false).
		Scan(&result).Error; err != nil {
		return 0, err
	}
	
	return result.TotalRevenue, nil
}

// UpdateCreatorRevenue updates revenue records for a creator
func (r *CreatorRevenueRepositoryImpl) UpdateCreatorRevenue(ctx context.Context, creatorID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get current time
		now := time.Now()
		
		// Define periods
		periods := []struct {
			Name  string
			Start time.Time
			End   time.Time
		}{
			{
				Name:  "daily",
				Start: time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()),
				End:   time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location()),
			},
			{
				Name:  "weekly",
				Start: now.AddDate(0, 0, -int(now.Weekday())),
				End:   now.AddDate(0, 0, 6-int(now.Weekday())),
			},
			{
				Name:  "monthly",
				Start: time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location()),
				End:   time.Date(now.Year(), now.Month()+1, 0, 23, 59, 59, 999999999, now.Location()),
			},
		}
		
		// Process each period
		for _, period := range periods {
			// Get gift data for this period
			type GiftData struct {
				StreamID       *uint
				TotalGifts     int
				TotalCoins     float64
				TotalNairaValue float64
				CreatorRevenue float64
				PlatformFee    float64
			}
			
			var giftDataList []GiftData
			
			// Get data grouped by stream
			if err := tx.WithContext(ctx).Model(&LiveStreamGift{}).
				Select("stream_id, COUNT(*) as total_gifts, SUM(coins_amount) as total_coins, SUM(naira_value) as total_naira_value, SUM(creator_revenue_amount) as creator_revenue, SUM(platform_revenue_amount) as platform_fee").
				Where("recipient_id = ? AND created_at BETWEEN ? AND ?", creatorID, period.Start, period.End).
				Group("stream_id").
				Find(&giftDataList).Error; err != nil {
				return err
			}
			
			// Process each stream's gift data
			for _, giftData := range giftDataList {
				// Check if revenue record already exists
				var existingRevenue CreatorRevenue
				result := tx.WithContext(ctx).
					Where("creator_id = ? AND period = ? AND period_start = ? AND stream_id = ?", 
						creatorID, period.Name, period.Start, giftData.StreamID).
					First(&existingRevenue)
				
				if result.Error == nil {
					// Update existing record
					existingRevenue.TotalGifts = giftData.TotalGifts
					existingRevenue.TotalCoins = giftData.TotalCoins
					existingRevenue.TotalNairaValue = giftData.TotalNairaValue
					existingRevenue.PlatformFee = giftData.PlatformFee
					existingRevenue.NetRevenue = giftData.CreatorRevenue
					
					if err := tx.Save(&existingRevenue).Error; err != nil {
						return err
					}
				} else {
					// Create new record
					newRevenue := CreatorRevenue{
						CreatorID:      creatorID,
						StreamID:       giftData.StreamID,
						Period:         period.Name,
						PeriodStart:    period.Start,
						PeriodEnd:      period.End,
						TotalGifts:     giftData.TotalGifts,
						TotalCoins:     giftData.TotalCoins,
						TotalNairaValue: giftData.TotalNairaValue,
						PlatformFee:    giftData.PlatformFee,
						NetRevenue:     giftData.CreatorRevenue,
						IsPaid:         false,
					}
					
					if err := tx.Create(&newRevenue).Error; err != nil {
						return err
					}
				}
			}
			
			// Also create/update a record for all streams combined
			var totalGiftData GiftData
			
			if err := tx.WithContext(ctx).Model(&LiveStreamGift{}).
				Select("COUNT(*) as total_gifts, SUM(coins_amount) as total_coins, SUM(naira_value) as total_naira_value, SUM(creator_revenue_amount) as creator_revenue, SUM(platform_revenue_amount) as platform_fee").
				Where("recipient_id = ? AND created_at BETWEEN ? AND ?", creatorID, period.Start, period.End).
				Scan(&totalGiftData).Error; err != nil {
				return err
			}
			
			// Check if total revenue record already exists
			var existingTotalRevenue CreatorRevenue
			result := tx.WithContext(ctx).
				Where("creator_id = ? AND period = ? AND period_start = ? AND stream_id IS NULL", 
					creatorID, period.Name, period.Start).
				First(&existingTotalRevenue)
			
			if result.Error == nil {
				// Update existing record
				existingTotalRevenue.TotalGifts = totalGiftData.TotalGifts
				existingTotalRevenue.TotalCoins = totalGiftData.TotalCoins
				existingTotalRevenue.TotalNairaValue = totalGiftData.TotalNairaValue
				existingTotalRevenue.PlatformFee = totalGiftData.PlatformFee
				existingTotalRevenue.NetRevenue = totalGiftData.CreatorRevenue
				
				if err := tx.Save(&existingTotalRevenue).Error; err != nil {
					return err
				}
			} else {
				// Create new record
				newTotalRevenue := CreatorRevenue{
					CreatorID:      creatorID,
					StreamID:       nil, // All streams combined
					Period:         period.Name,
					PeriodStart:    period.Start,
					PeriodEnd:      period.End,
					TotalGifts:     totalGiftData.TotalGifts,
					TotalCoins:     totalGiftData.TotalCoins,
					TotalNairaValue: totalGiftData.TotalNairaValue,
					PlatformFee:    totalGiftData.PlatformFee,
					NetRevenue:     totalGiftData.CreatorRevenue,
					IsPaid:         false,
				}
				
				if err := tx.Create(&newTotalRevenue).Error; err != nil {
					return err
				}
			}
		}
		
		return nil
	})
}

// MarkRevenuePaid marks revenue records as paid
func (r *CreatorRevenueRepositoryImpl) MarkRevenuePaid(ctx context.Context, revenueIDs []uint, paymentReference string) error {
	now := time.Now()
	
	return r.db.WithContext(ctx).Model(&CreatorRevenue{}).
		Where("id IN ?", revenueIDs).
		Updates(map[string]interface{}{
			"is_paid":            true,
			"payment_date":       now,
			"payment_reference": paymentReference,
		}).Error
}

// CreateWithdrawalRequest creates a new withdrawal request
func (r *CreatorRevenueRepositoryImpl) CreateWithdrawalRequest(ctx context.Context, request *WithdrawalRequest) error {
	return r.db.WithContext(ctx).Create(request).Error
}

// GetWithdrawalRequestByID retrieves a withdrawal request by its ID
func (r *CreatorRevenueRepositoryImpl) GetWithdrawalRequestByID(ctx context.Context, id uint) (*WithdrawalRequest, error) {
	var request WithdrawalRequest
	if err := r.db.WithContext(ctx).First(&request, id).Error; err != nil {
		return nil, err
	}
	return &request, nil
}

// UpdateWithdrawalRequest updates a withdrawal request
func (r *CreatorRevenueRepositoryImpl) UpdateWithdrawalRequest(ctx context.Context, request *WithdrawalRequest) error {
	return r.db.WithContext(ctx).Save(request).Error
}

// GetPendingWithdrawalRequests retrieves pending withdrawal requests with pagination
func (r *CreatorRevenueRepositoryImpl) GetPendingWithdrawalRequests(ctx context.Context, page, limit int) ([]WithdrawalRequest, int, error) {
	var requests []WithdrawalRequest
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&WithdrawalRequest{}).Where("status = ?", "pending").Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated requests
	if err := r.db.WithContext(ctx).
		Where("status = ?", "pending").
		Order("created_at ASC").
		Offset(offset).
		Limit(limit).
		Find(&requests).Error; err != nil {
		return nil, 0, err
	}
	
	return requests, int(count), nil
}

// GetCreatorWithdrawalRequests retrieves withdrawal requests for a creator with pagination
func (r *CreatorRevenueRepositoryImpl) GetCreatorWithdrawalRequests(ctx context.Context, creatorID uint, page, limit int) ([]WithdrawalRequest, int, error) {
	var requests []WithdrawalRequest
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&WithdrawalRequest{}).Where("creator_id = ?", creatorID).Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated requests
	if err := r.db.WithContext(ctx).
		Where("creator_id = ?", creatorID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&requests).Error; err != nil {
		return nil, 0, err
	}
	
	return requests, int(count), nil
}
