package repository

import (
	"context"
	"time"

	"github.com/greatnigeria/internal/livestream/models"
	"gorm.io/gorm"
)

// LiveStreamGift represents a gift sent during a live stream
type LiveStreamGift struct {
	gorm.Model
	StreamID       uint    `json:"streamId" gorm:"index"`
	SenderID       uint    `json:"senderId" gorm:"index"`
	RecipientID    uint    `json:"recipientId" gorm:"index"`
	GiftID         uint    `json:"giftId"`
	GiftName       string  `json:"giftName" gorm:"type:varchar(100)"`
	CoinsAmount    float64 `json:"coinsAmount"`
	NairaValue     float64 `json:"nairaValue"`
	Message        string  `json:"message" gorm:"type:text"`
	IsAnonymous    bool    `json:"isAnonymous" gorm:"default:false"`
	IsHighlighted  bool    `json:"isHighlighted" gorm:"default:false"`
	ComboCount     int     `json:"comboCount" gorm:"default:1"`
	
	// Revenue sharing
	CreatorRevenuePercent float64 `json:"creatorRevenuePercent" gorm:"default:70.0"`
	CreatorRevenueAmount  float64 `json:"creatorRevenueAmount"`
	PlatformRevenueAmount float64 `json:"platformRevenueAmount"`
}

// LiveStreamGiftRepository defines the interface for live stream gift data access
type LiveStreamGiftRepository interface {
	// Gift CRUD operations
	CreateGift(ctx context.Context, gift *LiveStreamGift) error
	GetGiftByID(ctx context.Context, id uint) (*LiveStreamGift, error)
	
	// Gift queries
	GetStreamGifts(ctx context.Context, streamID uint, page, limit int) ([]LiveStreamGift, int, error)
	GetUserSentGifts(ctx context.Context, userID uint, page, limit int) ([]LiveStreamGift, int, error)
	GetUserReceivedGifts(ctx context.Context, userID uint, page, limit int) ([]LiveStreamGift, int, error)
	
	// Gift statistics
	GetStreamGiftStats(ctx context.Context, streamID uint) (int, float64, float64, error)
	GetUserGiftStats(ctx context.Context, userID uint, isRecipient bool) (int, float64, float64, error)
	
	// Combo tracking
	GetRecentGift(ctx context.Context, streamID, senderID, giftID uint, timeWindow time.Duration) (*LiveStreamGift, error)
	UpdateComboCount(ctx context.Context, giftID uint, newCount int) error
}

// LiveStreamGiftRepositoryImpl implements the LiveStreamGiftRepository interface
type LiveStreamGiftRepositoryImpl struct {
	db *gorm.DB
}

// NewLiveStreamGiftRepository creates a new instance of the live stream gift repository
func NewLiveStreamGiftRepository(db *gorm.DB) LiveStreamGiftRepository {
	return &LiveStreamGiftRepositoryImpl{
		db: db,
	}
}

// CreateGift creates a new live stream gift
func (r *LiveStreamGiftRepositoryImpl) CreateGift(ctx context.Context, gift *LiveStreamGift) error {
	return r.db.WithContext(ctx).Create(gift).Error
}

// GetGiftByID retrieves a live stream gift by its ID
func (r *LiveStreamGiftRepositoryImpl) GetGiftByID(ctx context.Context, id uint) (*LiveStreamGift, error) {
	var gift LiveStreamGift
	if err := r.db.WithContext(ctx).First(&gift, id).Error; err != nil {
		return nil, err
	}
	return &gift, nil
}

// GetStreamGifts retrieves gifts for a stream with pagination
func (r *LiveStreamGiftRepositoryImpl) GetStreamGifts(ctx context.Context, streamID uint, page, limit int) ([]LiveStreamGift, int, error) {
	var gifts []LiveStreamGift
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&LiveStreamGift{}).Where("stream_id = ?", streamID).Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated gifts
	if err := r.db.WithContext(ctx).
		Where("stream_id = ?", streamID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&gifts).Error; err != nil {
		return nil, 0, err
	}
	
	return gifts, int(count), nil
}

// GetUserSentGifts retrieves gifts sent by a user with pagination
func (r *LiveStreamGiftRepositoryImpl) GetUserSentGifts(ctx context.Context, userID uint, page, limit int) ([]LiveStreamGift, int, error) {
	var gifts []LiveStreamGift
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&LiveStreamGift{}).Where("sender_id = ?", userID).Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated gifts
	if err := r.db.WithContext(ctx).
		Where("sender_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&gifts).Error; err != nil {
		return nil, 0, err
	}
	
	return gifts, int(count), nil
}

// GetUserReceivedGifts retrieves gifts received by a user with pagination
func (r *LiveStreamGiftRepositoryImpl) GetUserReceivedGifts(ctx context.Context, userID uint, page, limit int) ([]LiveStreamGift, int, error) {
	var gifts []LiveStreamGift
	var count int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&LiveStreamGift{}).Where("recipient_id = ?", userID).Count(&count).Error; err != nil {
		return nil, 0, err
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get paginated gifts
	if err := r.db.WithContext(ctx).
		Where("recipient_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&gifts).Error; err != nil {
		return nil, 0, err
	}
	
	return gifts, int(count), nil
}

// GetStreamGiftStats retrieves gift statistics for a stream
func (r *LiveStreamGiftRepositoryImpl) GetStreamGiftStats(ctx context.Context, streamID uint) (int, float64, float64, error) {
	type Result struct {
		Count      int
		CoinsTotal float64
		NairaTotal float64
	}
	
	var result Result
	
	err := r.db.WithContext(ctx).Model(&LiveStreamGift{}).
		Select("COUNT(*) as count, SUM(coins_amount) as coins_total, SUM(naira_value) as naira_total").
		Where("stream_id = ?", streamID).
		Scan(&result).Error
	
	if err != nil {
		return 0, 0, 0, err
	}
	
	return result.Count, result.CoinsTotal, result.NairaTotal, nil
}

// GetUserGiftStats retrieves gift statistics for a user
func (r *LiveStreamGiftRepositoryImpl) GetUserGiftStats(ctx context.Context, userID uint, isRecipient bool) (int, float64, float64, error) {
	type Result struct {
		Count      int
		CoinsTotal float64
		NairaTotal float64
	}
	
	var result Result
	query := r.db.WithContext(ctx).Model(&LiveStreamGift{})
	
	if isRecipient {
		query = query.Where("recipient_id = ?", userID)
	} else {
		query = query.Where("sender_id = ?", userID)
	}
	
	err := query.
		Select("COUNT(*) as count, SUM(coins_amount) as coins_total, SUM(naira_value) as naira_total").
		Scan(&result).Error
	
	if err != nil {
		return 0, 0, 0, err
	}
	
	return result.Count, result.CoinsTotal, result.NairaTotal, nil
}

// GetRecentGift retrieves the most recent gift of the same type from the same sender in a stream
func (r *LiveStreamGiftRepositoryImpl) GetRecentGift(ctx context.Context, streamID, senderID, giftID uint, timeWindow time.Duration) (*LiveStreamGift, error) {
	var gift LiveStreamGift
	
	// Calculate the time threshold
	threshold := time.Now().Add(-timeWindow)
	
	result := r.db.WithContext(ctx).
		Where("stream_id = ? AND sender_id = ? AND gift_id = ? AND created_at > ?", 
			streamID, senderID, giftID, threshold).
		Order("created_at DESC").
		First(&gift)
	
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, result.Error
	}
	
	return &gift, nil
}

// UpdateComboCount updates the combo count of a gift
func (r *LiveStreamGiftRepositoryImpl) UpdateComboCount(ctx context.Context, giftID uint, newCount int) error {
	return r.db.WithContext(ctx).Model(&LiveStreamGift{}).
		Where("id = ?", giftID).
		Update("combo_count", newCount).Error
}
