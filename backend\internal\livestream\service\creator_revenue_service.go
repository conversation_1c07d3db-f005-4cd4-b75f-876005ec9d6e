package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/greatnigeria/internal/livestream/repository"
	"github.com/greatnigeria/pkg/common/logger"
)

// CreatorRevenueService defines the interface for creator revenue business logic
type CreatorRevenueService interface {
	// Revenue queries
	GetCreatorRevenue(ctx context.Context, creatorID uint, period string, page, limit int) ([]repository.CreatorRevenue, int, error)
	GetStreamRevenue(ctx context.Context, streamID uint) (*repository.CreatorRevenue, error)
	GetRevenueSummary(ctx context.Context, creatorID uint) (map[string]interface{}, error)
	GetUnpaidRevenue(ctx context.Context, creatorID uint) (float64, error)
	
	// Revenue operations
	UpdateCreatorRevenue(ctx context.Context, creatorID uint) error
	
	// Withdrawal operations
	RequestWithdrawal(ctx context.Context, creatorID uint, amount float64, bankName, accountNumber, accountName string) (*repository.WithdrawalRequest, error)
	GetWithdrawalRequestByID(ctx context.Context, id uint) (*repository.WithdrawalRequest, error)
	GetCreatorWithdrawalRequests(ctx context.Context, creatorID uint, page, limit int) ([]repository.WithdrawalRequest, int, error)
	
	// Admin operations
	GetPendingWithdrawalRequests(ctx context.Context, page, limit int) ([]repository.WithdrawalRequest, int, error)
	ProcessWithdrawalRequest(ctx context.Context, requestID uint, status string, notes string, processedBy uint, transactionReference string) error
}

// CreatorRevenueServiceImpl implements the CreatorRevenueService interface
type CreatorRevenueServiceImpl struct {
	repo   repository.CreatorRevenueRepository
	logger *logger.Logger
}

// NewCreatorRevenueService creates a new instance of the creator revenue service
func NewCreatorRevenueService(repo repository.CreatorRevenueRepository, logger *logger.Logger) CreatorRevenueService {
	return &CreatorRevenueServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

// GetCreatorRevenue retrieves revenue records for a creator
func (s *CreatorRevenueServiceImpl) GetCreatorRevenue(ctx context.Context, creatorID uint, period string, page, limit int) ([]repository.CreatorRevenue, int, error) {
	// Validate period
	if period != "" && !isValidRevenuePeriod(period) {
		return nil, 0, errors.New("invalid period")
	}
	
	revenues, count, err := s.repo.GetCreatorRevenue(ctx, creatorID, period, page, limit)
	if err != nil {
		return nil, 0, err
	}
	
	s.logger.Infof("Retrieved %d revenue records for creator %d", len(revenues), creatorID)
	
	return revenues, count, nil
}

// GetStreamRevenue retrieves revenue for a specific stream
func (s *CreatorRevenueServiceImpl) GetStreamRevenue(ctx context.Context, streamID uint) (*repository.CreatorRevenue, error) {
	revenue, err := s.repo.GetStreamRevenue(ctx, streamID)
	if err != nil {
		return nil, err
	}
	
	s.logger.Infof("Retrieved revenue for stream %d: %.2f Naira", streamID, revenue.NetRevenue)
	
	return revenue, nil
}

// GetRevenueSummary retrieves a summary of a creator's revenue
func (s *CreatorRevenueServiceImpl) GetRevenueSummary(ctx context.Context, creatorID uint) (map[string]interface{}, error) {
	// Get revenue for different periods
	periods := []string{"daily", "weekly", "monthly"}
	summary := make(map[string]interface{})
	
	for _, period := range periods {
		revenues, _, err := s.repo.GetCreatorRevenue(ctx, creatorID, period, 1, 1)
		if err != nil {
			return nil, err
		}
		
		if len(revenues) > 0 {
			summary[period] = map[string]interface{}{
				"totalGifts":     revenues[0].TotalGifts,
				"totalCoins":     revenues[0].TotalCoins,
				"totalNairaValue": revenues[0].TotalNairaValue,
				"netRevenue":     revenues[0].NetRevenue,
			}
		} else {
			summary[period] = map[string]interface{}{
				"totalGifts":     0,
				"totalCoins":     0.0,
				"totalNairaValue": 0.0,
				"netRevenue":     0.0,
			}
		}
	}
	
	// Get unpaid revenue
	unpaidRevenue, err := s.repo.GetUnpaidRevenue(ctx, creatorID)
	if err != nil {
		return nil, err
	}
	
	summary["unpaidRevenue"] = unpaidRevenue
	
	// Get withdrawal requests
	withdrawalRequests, _, err := s.repo.GetCreatorWithdrawalRequests(ctx, creatorID, 1, 5)
	if err != nil {
		return nil, err
	}
	
	var recentWithdrawals []map[string]interface{}
	for _, request := range withdrawalRequests {
		recentWithdrawals = append(recentWithdrawals, map[string]interface{}{
			"id":        request.ID,
			"amount":    request.Amount,
			"status":    request.Status,
			"createdAt": request.CreatedAt,
		})
	}
	
	summary["recentWithdrawals"] = recentWithdrawals
	
	s.logger.Infof("Generated revenue summary for creator %d", creatorID)
	
	return summary, nil
}

// GetUnpaidRevenue retrieves the total unpaid revenue for a creator
func (s *CreatorRevenueServiceImpl) GetUnpaidRevenue(ctx context.Context, creatorID uint) (float64, error) {
	unpaidRevenue, err := s.repo.GetUnpaidRevenue(ctx, creatorID)
	if err != nil {
		return 0, err
	}
	
	s.logger.Infof("Retrieved unpaid revenue for creator %d: %.2f Naira", creatorID, unpaidRevenue)
	
	return unpaidRevenue, nil
}

// UpdateCreatorRevenue updates revenue records for a creator
func (s *CreatorRevenueServiceImpl) UpdateCreatorRevenue(ctx context.Context, creatorID uint) error {
	if err := s.repo.UpdateCreatorRevenue(ctx, creatorID); err != nil {
		s.logger.Errorf("Failed to update revenue for creator %d: %v", creatorID, err)
		return err
	}
	
	s.logger.Infof("Updated revenue for creator %d", creatorID)
	
	return nil
}

// RequestWithdrawal creates a withdrawal request
func (s *CreatorRevenueServiceImpl) RequestWithdrawal(ctx context.Context, creatorID uint, amount float64, bankName, accountNumber, accountName string) (*repository.WithdrawalRequest, error) {
	// Validate inputs
	if amount <= 0 {
		return nil, errors.New("withdrawal amount must be greater than zero")
	}
	
	if bankName == "" || accountNumber == "" || accountName == "" {
		return nil, errors.New("bank details are required")
	}
	
	// Check if creator has sufficient unpaid revenue
	unpaidRevenue, err := s.repo.GetUnpaidRevenue(ctx, creatorID)
	if err != nil {
		return nil, err
	}
	
	if amount > unpaidRevenue {
		return nil, fmt.Errorf("insufficient funds: available %.2f, requested %.2f", unpaidRevenue, amount)
	}
	
	// Create withdrawal request
	request := &repository.WithdrawalRequest{
		CreatorID:     creatorID,
		Amount:        amount,
		Status:        "pending",
		BankName:      bankName,
		AccountNumber: accountNumber,
		AccountName:   accountName,
	}
	
	if err := s.repo.CreateWithdrawalRequest(ctx, request); err != nil {
		return nil, err
	}
	
	s.logger.Infof("Created withdrawal request for creator %d: %.2f Naira", creatorID, amount)
	
	return request, nil
}

// GetWithdrawalRequestByID retrieves a withdrawal request by its ID
func (s *CreatorRevenueServiceImpl) GetWithdrawalRequestByID(ctx context.Context, id uint) (*repository.WithdrawalRequest, error) {
	return s.repo.GetWithdrawalRequestByID(ctx, id)
}

// GetCreatorWithdrawalRequests retrieves withdrawal requests for a creator
func (s *CreatorRevenueServiceImpl) GetCreatorWithdrawalRequests(ctx context.Context, creatorID uint, page, limit int) ([]repository.WithdrawalRequest, int, error) {
	return s.repo.GetCreatorWithdrawalRequests(ctx, creatorID, page, limit)
}

// GetPendingWithdrawalRequests retrieves pending withdrawal requests
func (s *CreatorRevenueServiceImpl) GetPendingWithdrawalRequests(ctx context.Context, page, limit int) ([]repository.WithdrawalRequest, int, error) {
	return s.repo.GetPendingWithdrawalRequests(ctx, page, limit)
}

// ProcessWithdrawalRequest processes a withdrawal request
func (s *CreatorRevenueServiceImpl) ProcessWithdrawalRequest(ctx context.Context, requestID uint, status string, notes string, processedBy uint, transactionReference string) error {
	// Validate status
	if status != "approved" && status != "rejected" && status != "completed" {
		return errors.New("invalid status")
	}
	
	// Get the withdrawal request
	request, err := s.repo.GetWithdrawalRequestByID(ctx, requestID)
	if err != nil {
		return err
	}
	
	// Check if request is in a processable state
	if request.Status != "pending" && !(request.Status == "approved" && status == "completed") {
		return errors.New("request cannot be processed from its current state")
	}
	
	// Update request
	now := time.Now()
	request.Status = status
	request.ProcessedDate = &now
	request.ProcessedBy = &processedBy
	request.Notes = notes
	
	if status == "completed" {
		if transactionReference == "" {
			// Generate a transaction reference if none provided
			request.TransactionReference = fmt.Sprintf("WD-%s", uuid.New().String())
		} else {
			request.TransactionReference = transactionReference
		}
		
		// Mark revenue as paid
		// In a real implementation, we would identify which revenue records to mark as paid
		// For simplicity, we're not implementing this part
	}
	
	if err := s.repo.UpdateWithdrawalRequest(ctx, request); err != nil {
		return err
	}
	
	s.logger.Infof("Processed withdrawal request %d: status set to %s", requestID, status)
	
	return nil
}

// isValidRevenuePeriod checks if a period is valid for revenue
func isValidRevenuePeriod(period string) bool {
	validPeriods := map[string]bool{
		"daily":   true,
		"weekly":  true,
		"monthly": true,
	}
	
	return validPeriods[period]
}
