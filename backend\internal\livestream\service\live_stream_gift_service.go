package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/greatnigeria/internal/livestream/repository"
	"github.com/greatnigeria/pkg/common/logger"
)

// LiveStreamGiftService defines the interface for live stream gift business logic
type LiveStreamGiftService interface {
	// Gift operations
	SendGift(ctx context.Context, streamID, senderID, recipientID, giftID uint, coinsAmount float64, message string, isAnonymous bool) (*repository.LiveStreamGift, error)
	GetGiftByID(ctx context.Context, id uint) (*repository.LiveStreamGift, error)
	
	// Gift queries
	GetStreamGifts(ctx context.Context, streamID uint, page, limit int) ([]repository.LiveStreamGift, int, error)
	GetUserSentGifts(ctx context.Context, userID uint, page, limit int) ([]repository.LiveStreamGift, int, error)
	GetUserReceivedGifts(ctx context.Context, userID uint, page, limit int) ([]repository.LiveStreamGift, int, error)
	
	// Gift statistics
	GetStreamGiftStats(ctx context.Context, streamID uint) (int, float64, float64, error)
	GetUserGiftStats(ctx context.Context, userID uint, isRecipient bool) (int, float64, float64, error)
}

// LiveStreamGiftServiceImpl implements the LiveStreamGiftService interface
type LiveStreamGiftServiceImpl struct {
	giftRepo     repository.LiveStreamGiftRepository
	currencyRepo repository.VirtualCurrencyRepository
	rankingRepo  repository.GifterRankingRepository
	revenueRepo  repository.CreatorRevenueRepository
	logger       *logger.Logger
}

// NewLiveStreamGiftService creates a new instance of the live stream gift service
func NewLiveStreamGiftService(
	giftRepo repository.LiveStreamGiftRepository,
	currencyRepo repository.VirtualCurrencyRepository,
	rankingRepo repository.GifterRankingRepository,
	revenueRepo repository.CreatorRevenueRepository,
	logger *logger.Logger,
) LiveStreamGiftService {
	return &LiveStreamGiftServiceImpl{
		giftRepo:     giftRepo,
		currencyRepo: currencyRepo,
		rankingRepo:  rankingRepo,
		revenueRepo:  revenueRepo,
		logger:       logger,
	}
}

// SendGift sends a gift during a live stream
func (s *LiveStreamGiftServiceImpl) SendGift(ctx context.Context, streamID, senderID, recipientID, giftID uint, coinsAmount float64, message string, isAnonymous bool) (*repository.LiveStreamGift, error) {
	// Check if user has sufficient balance
	balance, err := s.currencyRepo.GetUserBalance(ctx, senderID)
	if err != nil {
		return nil, err
	}
	
	if balance.Balance < coinsAmount {
		return nil, errors.New("insufficient balance")
	}
	
	// Calculate Naira value (1 coin = 1 Naira for simplicity)
	nairaValue := coinsAmount
	
	// Calculate revenue sharing (70% to creator, 30% to platform)
	creatorRevenuePercent := 70.0
	creatorRevenueAmount := nairaValue * (creatorRevenuePercent / 100)
	platformRevenueAmount := nairaValue - creatorRevenueAmount
	
	// Check for combo (same gift sent by same user in last 10 seconds)
	comboCount := 1
	timeWindow := 10 * time.Second
	
	recentGift, err := s.giftRepo.GetRecentGift(ctx, streamID, senderID, giftID, timeWindow)
	if err == nil && recentGift != nil {
		// Update combo count on previous gift
		comboCount = recentGift.ComboCount + 1
		if err := s.giftRepo.UpdateComboCount(ctx, recentGift.ID, comboCount); err != nil {
			s.logger.Warnf("Failed to update combo count: %v", err)
			// Continue with new gift anyway
			comboCount = 1
		}
	}
	
	// Create gift record
	gift := &repository.LiveStreamGift{
		StreamID:             streamID,
		SenderID:             senderID,
		RecipientID:          recipientID,
		GiftID:               giftID,
		GiftName:             fmt.Sprintf("Gift %d", giftID), // This would be fetched from gift catalog in a real implementation
		CoinsAmount:          coinsAmount,
		NairaValue:           nairaValue,
		Message:              message,
		IsAnonymous:          isAnonymous,
		IsHighlighted:        coinsAmount >= 1000, // Highlight gifts worth 1000+ coins
		ComboCount:           comboCount,
		CreatorRevenuePercent: creatorRevenuePercent,
		CreatorRevenueAmount:  creatorRevenueAmount,
		PlatformRevenueAmount: platformRevenueAmount,
	}
	
	// Begin transaction
	// Note: In a real implementation, this would be a proper transaction
	
	// 1. Create gift record
	if err := s.giftRepo.CreateGift(ctx, gift); err != nil {
		return nil, err
	}
	
	// 2. Deduct coins from sender
	referenceID := fmt.Sprintf("GIFT-%d", gift.ID)
	description := fmt.Sprintf("Gift sent in stream %d", streamID)
	
	if err := s.currencyRepo.DeductCoinsFromUser(ctx, senderID, coinsAmount, "gift_sent", description, referenceID); err != nil {
		// In a real implementation, we would roll back the gift creation
		s.logger.Errorf("Failed to deduct coins after gift creation: %v", err)
		return nil, err
	}
	
	// 3. Update stream metrics
	// This would typically be done by a separate service
	
	// 4. Update rankings
	go func() {
		// Use background context for async operations
		bgCtx := context.Background()
		
		// Update stream rankings
		if err := s.rankingRepo.UpdateStreamRankings(bgCtx, streamID); err != nil {
			s.logger.Warnf("Failed to update stream rankings: %v", err)
		}
		
		// Update global rankings
		if err := s.rankingRepo.UpdateGlobalRankings(bgCtx); err != nil {
			s.logger.Warnf("Failed to update global rankings: %v", err)
		}
		
		// Update creator revenue
		if err := s.revenueRepo.UpdateCreatorRevenue(bgCtx, recipientID); err != nil {
			s.logger.Warnf("Failed to update creator revenue: %v", err)
		}
	}()
	
	s.logger.Infof("User %d sent gift worth %.2f coins to user %d in stream %d", senderID, coinsAmount, recipientID, streamID)
	
	return gift, nil
}

// GetGiftByID retrieves a gift by its ID
func (s *LiveStreamGiftServiceImpl) GetGiftByID(ctx context.Context, id uint) (*repository.LiveStreamGift, error) {
	return s.giftRepo.GetGiftByID(ctx, id)
}

// GetStreamGifts retrieves gifts for a stream
func (s *LiveStreamGiftServiceImpl) GetStreamGifts(ctx context.Context, streamID uint, page, limit int) ([]repository.LiveStreamGift, int, error) {
	return s.giftRepo.GetStreamGifts(ctx, streamID, page, limit)
}

// GetUserSentGifts retrieves gifts sent by a user
func (s *LiveStreamGiftServiceImpl) GetUserSentGifts(ctx context.Context, userID uint, page, limit int) ([]repository.LiveStreamGift, int, error) {
	return s.giftRepo.GetUserSentGifts(ctx, userID, page, limit)
}

// GetUserReceivedGifts retrieves gifts received by a user
func (s *LiveStreamGiftServiceImpl) GetUserReceivedGifts(ctx context.Context, userID uint, page, limit int) ([]repository.LiveStreamGift, int, error) {
	return s.giftRepo.GetUserReceivedGifts(ctx, userID, page, limit)
}

// GetStreamGiftStats retrieves gift statistics for a stream
func (s *LiveStreamGiftServiceImpl) GetStreamGiftStats(ctx context.Context, streamID uint) (int, float64, float64, error) {
	return s.giftRepo.GetStreamGiftStats(ctx, streamID)
}

// GetUserGiftStats retrieves gift statistics for a user
func (s *LiveStreamGiftServiceImpl) GetUserGiftStats(ctx context.Context, userID uint, isRecipient bool) (int, float64, float64, error) {
	return s.giftRepo.GetUserGiftStats(ctx, userID, isRecipient)
}
