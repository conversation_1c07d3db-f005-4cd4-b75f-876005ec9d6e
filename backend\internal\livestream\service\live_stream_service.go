package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	"github.com/greatnigeria/internal/livestream/repository"
	"github.com/greatnigeria/pkg/common/logger"
)

// LiveStreamService defines the interface for live stream business logic
type LiveStreamService interface {
	// Stream operations
	CreateStream(ctx context.Context, creatorID uint, title, description, thumbnailURL string, scheduledStart time.Time, isPrivate bool, categories, tags string) (*repository.LiveStream, error)
	GetStreamByID(ctx context.Context, id uint) (*repository.LiveStream, error)
	UpdateStream(ctx context.Context, id uint, title, description, thumbnailURL string, scheduledStart time.Time, isPrivate bool, categories, tags string) (*repository.LiveStream, error)
	DeleteStream(ctx context.Context, id uint, creatorID uint) error
	
	// Stream status operations
	StartStream(ctx context.Context, id uint, creatorID uint) (*repository.LiveStream, error)
	EndStream(ctx context.Context, id uint, creatorID uint) (*repository.LiveStream, error)
	
	// Stream queries
	GetActiveStreams(ctx context.Context, page, limit int) ([]repository.LiveStream, int, error)
	GetStreamsByCreator(ctx context.Context, creatorID uint, page, limit int) ([]repository.LiveStream, int, error)
	GetScheduledStreams(ctx context.Context, page, limit int) ([]repository.LiveStream, int, error)
	SearchStreams(ctx context.Context, query string, page, limit int) ([]repository.LiveStream, int, error)
	
	// Stream viewer operations
	AddViewer(ctx context.Context, streamID, userID uint) error
	RemoveViewer(ctx context.Context, streamID, userID uint) error
	GetStreamViewers(ctx context.Context, streamID uint, page, limit int) ([]repository.LiveStreamViewer, int, error)
	GetViewerCount(ctx context.Context, streamID uint) (int, error)
	
	// Stream key operations
	GenerateStreamKey(ctx context.Context, streamID uint, creatorID uint) (string, error)
	ValidateStreamKey(ctx context.Context, streamKey string) (*repository.LiveStream, error)
}

// LiveStreamServiceImpl implements the LiveStreamService interface
type LiveStreamServiceImpl struct {
	repo   repository.LiveStreamRepository
	logger *logger.Logger
}

// NewLiveStreamService creates a new instance of the live stream service
func NewLiveStreamService(repo repository.LiveStreamRepository, logger *logger.Logger) LiveStreamService {
	return &LiveStreamServiceImpl{
		repo:   repo,
		logger: logger,
	}
}

// CreateStream creates a new live stream
func (s *LiveStreamServiceImpl) CreateStream(ctx context.Context, creatorID uint, title, description, thumbnailURL string, scheduledStart time.Time, isPrivate bool, categories, tags string) (*repository.LiveStream, error) {
	// Validate inputs
	if title == "" {
		return nil, errors.New("title is required")
	}
	
	if scheduledStart.Before(time.Now()) {
		return nil, errors.New("scheduled start time must be in the future")
	}
	
	// Generate stream key
	streamKey, err := s.generateRandomKey()
	if err != nil {
		return nil, err
	}
	
	// Generate playback URL
	playbackURL := fmt.Sprintf("https://stream.greatnigeria.net/live/%s", streamKey)
	
	// Create stream
	stream := &repository.LiveStream{
		CreatorID:      creatorID,
		Title:          title,
		Description:    description,
		ThumbnailURL:   thumbnailURL,
		Status:         "scheduled",
		ScheduledStart: scheduledStart,
		StreamKey:      streamKey,
		PlaybackURL:    playbackURL,
		IsPrivate:      isPrivate,
		AllowGifting:   true,
		Categories:     categories,
		Tags:           tags,
	}
	
	if err := s.repo.CreateStream(ctx, stream); err != nil {
		return nil, err
	}
	
	s.logger.Infof("Created new stream: %s by creator %d, scheduled for %s", title, creatorID, scheduledStart.Format(time.RFC3339))
	
	return stream, nil
}

// GetStreamByID retrieves a live stream by its ID
func (s *LiveStreamServiceImpl) GetStreamByID(ctx context.Context, id uint) (*repository.LiveStream, error) {
	return s.repo.GetStreamByID(ctx, id)
}

// UpdateStream updates a live stream
func (s *LiveStreamServiceImpl) UpdateStream(ctx context.Context, id uint, title, description, thumbnailURL string, scheduledStart time.Time, isPrivate bool, categories, tags string) (*repository.LiveStream, error) {
	// Get existing stream
	stream, err := s.repo.GetStreamByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	// Validate inputs
	if title == "" {
		return nil, errors.New("title is required")
	}
	
	// Only allow updating scheduled streams
	if stream.Status != "scheduled" {
		return nil, errors.New("only scheduled streams can be updated")
	}
	
	// Update fields
	stream.Title = title
	stream.Description = description
	stream.ThumbnailURL = thumbnailURL
	stream.ScheduledStart = scheduledStart
	stream.IsPrivate = isPrivate
	stream.Categories = categories
	stream.Tags = tags
	
	if err := s.repo.UpdateStream(ctx, stream); err != nil {
		return nil, err
	}
	
	s.logger.Infof("Updated stream %d: %s", id, title)
	
	return stream, nil
}

// DeleteStream deletes a live stream
func (s *LiveStreamServiceImpl) DeleteStream(ctx context.Context, id uint, creatorID uint) error {
	// Get existing stream
	stream, err := s.repo.GetStreamByID(ctx, id)
	if err != nil {
		return err
	}
	
	// Verify ownership
	if stream.CreatorID != creatorID {
		return errors.New("unauthorized: you are not the creator of this stream")
	}
	
	// Only allow deleting scheduled streams
	if stream.Status != "scheduled" {
		return errors.New("only scheduled streams can be deleted")
	}
	
	if err := s.repo.DeleteStream(ctx, id); err != nil {
		return err
	}
	
	s.logger.Infof("Deleted stream %d by creator %d", id, creatorID)
	
	return nil
}

// StartStream marks a stream as live
func (s *LiveStreamServiceImpl) StartStream(ctx context.Context, id uint, creatorID uint) (*repository.LiveStream, error) {
	// Get existing stream
	stream, err := s.repo.GetStreamByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	// Verify ownership
	if stream.CreatorID != creatorID {
		return errors.New("unauthorized: you are not the creator of this stream")
	}
	
	// Only allow starting scheduled streams
	if stream.Status != "scheduled" {
		return nil, errors.New("only scheduled streams can be started")
	}
	
	if err := s.repo.StartStream(ctx, id); err != nil {
		return nil, err
	}
	
	// Get updated stream
	updatedStream, err := s.repo.GetStreamByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	s.logger.Infof("Started stream %d: %s by creator %d", id, stream.Title, creatorID)
	
	return updatedStream, nil
}

// EndStream marks a stream as ended
func (s *LiveStreamServiceImpl) EndStream(ctx context.Context, id uint, creatorID uint) (*repository.LiveStream, error) {
	// Get existing stream
	stream, err := s.repo.GetStreamByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	// Verify ownership
	if stream.CreatorID != creatorID {
		return errors.New("unauthorized: you are not the creator of this stream")
	}
	
	// Only allow ending live streams
	if stream.Status != "live" {
		return nil, errors.New("only live streams can be ended")
	}
	
	if err := s.repo.EndStream(ctx, id); err != nil {
		return nil, err
	}
	
	// Get updated stream
	updatedStream, err := s.repo.GetStreamByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	s.logger.Infof("Ended stream %d: %s by creator %d", id, stream.Title, creatorID)
	
	return updatedStream, nil
}

// GetActiveStreams retrieves all active (live) streams
func (s *LiveStreamServiceImpl) GetActiveStreams(ctx context.Context, page, limit int) ([]repository.LiveStream, int, error) {
	return s.repo.GetActiveStreams(ctx, page, limit)
}

// GetStreamsByCreator retrieves streams by creator
func (s *LiveStreamServiceImpl) GetStreamsByCreator(ctx context.Context, creatorID uint, page, limit int) ([]repository.LiveStream, int, error) {
	return s.repo.GetStreamsByCreator(ctx, creatorID, page, limit)
}

// GetScheduledStreams retrieves scheduled streams
func (s *LiveStreamServiceImpl) GetScheduledStreams(ctx context.Context, page, limit int) ([]repository.LiveStream, int, error) {
	return s.repo.GetScheduledStreams(ctx, page, limit)
}

// SearchStreams searches for streams
func (s *LiveStreamServiceImpl) SearchStreams(ctx context.Context, query string, page, limit int) ([]repository.LiveStream, int, error) {
	return s.repo.SearchStreams(ctx, query, page, limit)
}

// AddViewer adds a viewer to a stream
func (s *LiveStreamServiceImpl) AddViewer(ctx context.Context, streamID, userID uint) error {
	// Get stream to verify it's live
	stream, err := s.repo.GetStreamByID(ctx, streamID)
	if err != nil {
		return err
	}
	
	if stream.Status != "live" {
		return errors.New("cannot join a stream that is not live")
	}
	
	// Check if private stream
	if stream.IsPrivate {
		// TODO: Implement access control for private streams
		// For now, just allow all users
	}
	
	return s.repo.AddViewer(ctx, streamID, userID)
}

// RemoveViewer removes a viewer from a stream
func (s *LiveStreamServiceImpl) RemoveViewer(ctx context.Context, streamID, userID uint) error {
	return s.repo.RemoveViewer(ctx, streamID, userID)
}

// GetStreamViewers retrieves viewers of a stream
func (s *LiveStreamServiceImpl) GetStreamViewers(ctx context.Context, streamID uint, page, limit int) ([]repository.LiveStreamViewer, int, error) {
	return s.repo.GetStreamViewers(ctx, streamID, page, limit)
}

// GetViewerCount retrieves the current viewer count of a stream
func (s *LiveStreamServiceImpl) GetViewerCount(ctx context.Context, streamID uint) (int, error) {
	return s.repo.GetViewerCount(ctx, streamID)
}

// GenerateStreamKey generates a new stream key for a stream
func (s *LiveStreamServiceImpl) GenerateStreamKey(ctx context.Context, streamID uint, creatorID uint) (string, error) {
	// Get existing stream
	stream, err := s.repo.GetStreamByID(ctx, streamID)
	if err != nil {
		return "", err
	}
	
	// Verify ownership
	if stream.CreatorID != creatorID {
		return "", errors.New("unauthorized: you are not the creator of this stream")
	}
	
	// Generate new stream key
	streamKey, err := s.generateRandomKey()
	if err != nil {
		return "", err
	}
	
	// Update stream key
	stream.StreamKey = streamKey
	stream.PlaybackURL = fmt.Sprintf("https://stream.greatnigeria.net/live/%s", streamKey)
	
	if err := s.repo.UpdateStream(ctx, stream); err != nil {
		return "", err
	}
	
	s.logger.Infof("Generated new stream key for stream %d by creator %d", streamID, creatorID)
	
	return streamKey, nil
}

// ValidateStreamKey validates a stream key
func (s *LiveStreamServiceImpl) ValidateStreamKey(ctx context.Context, streamKey string) (*repository.LiveStream, error) {
	// TODO: Implement this method to validate stream keys for RTMP server
	// This would typically query the database to find a stream with the given key
	// and verify that the stream is scheduled or live
	
	return nil, errors.New("not implemented")
}

// generateRandomKey generates a random stream key
func (s *LiveStreamServiceImpl) generateRandomKey() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
