package handlers

import (
        "context"
        "net/http"
        "strconv"

        "github.com/gin-gonic/gin"
        "github.com/greatnigeria/internal/payment/models"
        "github.com/greatnigeria/pkg/common/errors"
        "github.com/greatnigeria/pkg/common/logger"
        pkgmodels "github.com/greatnigeria/pkg/models"
)

// ReceiptService defines the interface for receipt operations needed by PaymentHandler
type ReceiptService interface {
        GenerateReceiptForPayment(ctx context.Context, paymentID uint) (*models.Receipt, error)
}

// PaymentHandler handles payment-related requests
type PaymentHandler struct {
        paymentService PaymentService
        receiptService ReceiptService
        logger         *logger.Logger
}

// PaymentService defines the interface for payment service operations
type PaymentService interface {
        CreatePaymentIntent(userID uint, req *pkgmodels.PaymentIntentRequest) (*pkgmodels.PaymentResponse, error)
        ProcessPayment(userID uint, req *pkgmodels.PaymentProcessRequest) (*pkgmodels.PaymentResponse, error)
        GetTransactions(userID uint, page, limit int) ([]pkgmodels.PaymentResponse, int, error)
        GetTransaction(userID, transactionID uint) (*pkgmodels.PaymentResponse, error)
        GetAllTransactions(page, limit int) ([]pkgmodels.PaymentResponse, int, error)
        HandlePaystackWebhook(body []byte) (uint, error)
        HandleFlutterwaveWebhook(body []byte) (uint, error)
        HandleSquadWebhook(body []byte) (uint, error)
}

// NewPaymentHandler creates a new payment handler
func NewPaymentHandler(paymentService PaymentService, receiptService ReceiptService, logger *logger.Logger) *PaymentHandler {
        return &PaymentHandler{
                paymentService: paymentService,
                receiptService: receiptService,
                logger:         logger,
        }
}

// CreatePaymentIntent handles creating a payment intent
func (h *PaymentHandler) CreatePaymentIntent(c *gin.Context) {
        var req models.PaymentIntentRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest(err.Error()))
                return
        }

        userID, _ := c.Get("user_id")

        // Validate payment type
        if req.PaymentType != models.PaymentTypePremium && req.PaymentType != models.PaymentTypeDonation {
                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid payment type"))
                return
        }

        // Validate gateway
        if req.Gateway != models.PaymentGatewayPaystack && 
           req.Gateway != models.PaymentGatewayFlutterwave && 
           req.Gateway != models.PaymentGatewaySquad {
                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid payment gateway"))
                return
        }

        // Validate currency
        if req.Currency != models.CurrencyNGN && req.Currency != models.CurrencyUSD {
                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid currency. Supported currencies are NGN and USD"))
                return
        }

        // Validate amount for premium payment
        if req.PaymentType == models.PaymentTypePremium {
                if req.PlanID == nil {
                        // Validate premium payment amount
                        if req.Currency == models.CurrencyNGN && req.Amount < 1000 {
                                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Minimum premium payment amount is 1000 NGN"))
                                return
                        }
                        if req.Currency == models.CurrencyUSD && req.Amount < 10 {
                                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Minimum premium payment amount is 10 USD"))
                                return
                        }
                }
        }

        // Create payment intent
        intent, err := h.paymentService.CreatePaymentIntent(userID.(uint), &req)
        if err != nil {
                if e, ok := err.(*errors.APIError); ok {
                        c.JSON(e.Status, e)
                        return
                }
                h.logger.WithError(err).Error("Failed to create payment intent")
                c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to create payment intent"))
                return
        }

        c.JSON(http.StatusOK, intent)
}

// ProcessPayment handles processing a payment
func (h *PaymentHandler) ProcessPayment(c *gin.Context) {
        var req pkgmodels.PaymentProcessRequest
        if err := c.ShouldBindJSON(&req); err != nil {
                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest(err.Error()))
                return
        }

        userID, _ := c.Get("user_id")

        // Process payment
        payment, err := h.paymentService.ProcessPayment(userID.(uint), &req)
        if err != nil {
                if e, ok := err.(*errors.APIError); ok {
                        c.JSON(e.Status, e)
                        return
                }
                h.logger.WithError(err).Error("Failed to process payment")
                c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to process payment"))
                return
        }

        // If the payment was successful, generate a receipt asynchronously
        if payment.Status == string(models.StatusSucceeded) && h.receiptService != nil {
                // Extract payment ID from the response (assuming it's available)
                if payment.ID > 0 {
                        go func(paymentID uint) {
                                // Use background context for the asynchronous operation
                                ctx := context.Background()
                                receipt, err := h.receiptService.GenerateReceiptForPayment(ctx, paymentID)
                                if err != nil {
                                        h.logger.WithError(err).Error("Failed to generate receipt for payment")
                                } else {
                                        h.logger.Infof("Receipt generated successfully with number: %s", receipt.ReceiptNumber)
                                }
                        }(payment.ID)
                }
        }

        c.JSON(http.StatusOK, payment)
}

// GetTransactions handles getting a user's payment transactions
func (h *PaymentHandler) GetTransactions(c *gin.Context) {
        userID, _ := c.Get("user_id")

        // Parse pagination parameters
        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

        // Ensure valid pagination
        if page < 1 {
                page = 1
        }
        if limit < 1 || limit > 100 {
                limit = 20
        }

        transactions, total, err := h.paymentService.GetTransactions(userID.(uint), page, limit)
        if err != nil {
                if e, ok := err.(*errors.APIError); ok {
                        c.JSON(e.Status, e)
                        return
                }
                h.logger.WithError(err).Error("Failed to get transactions")
                c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get transactions"))
                return
        }

        c.JSON(http.StatusOK, gin.H{
                "transactions": transactions,
                "pagination": gin.H{
                        "page":       page,
                        "limit":      limit,
                        "total":      total,
                        "total_pages": (total + limit - 1) / limit,
                },
        })
}

// GetTransaction handles getting a single transaction
func (h *PaymentHandler) GetTransaction(c *gin.Context) {
        idParam := c.Param("id")
        id, err := strconv.ParseUint(idParam, 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid transaction ID"))
                return
        }

        userID, _ := c.Get("user_id")

        transaction, err := h.paymentService.GetTransaction(userID.(uint), uint(id))
        if err != nil {
                if e, ok := err.(*errors.APIError); ok {
                        c.JSON(e.Status, e)
                        return
                }
                h.logger.WithError(err).Error("Failed to get transaction")
                c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get transaction"))
                return
        }

        c.JSON(http.StatusOK, transaction)
}

// GetAllTransactions handles getting all transactions (admin only)
func (h *PaymentHandler) GetAllTransactions(c *gin.Context) {
        // Parse pagination parameters
        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

        // Ensure valid pagination
        if page < 1 {
                page = 1
        }
        if limit < 1 || limit > 100 {
                limit = 20
        }

        transactions, total, err := h.paymentService.GetAllTransactions(page, limit)
        if err != nil {
                if e, ok := err.(*errors.APIError); ok {
                        c.JSON(e.Status, e)
                        return
                }
                h.logger.WithError(err).Error("Failed to get all transactions")
                c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to get all transactions"))
                return
        }

        c.JSON(http.StatusOK, gin.H{
                "transactions": transactions,
                "pagination": gin.H{
                        "page":       page,
                        "limit":      limit,
                        "total":      total,
                        "total_pages": (total + limit - 1) / limit,
                },
        })
}

// PaystackWebhook handles Paystack webhook events
func (h *PaymentHandler) PaystackWebhook(c *gin.Context) {
        // Read request body
        body, err := c.GetRawData()
        if err != nil {
                h.logger.WithError(err).Error("Failed to read Paystack webhook body")
                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid webhook payload"))
                return
        }

        // Verify Paystack signature (in a real-world implementation)
        // ...

        // Process webhook - could return payment ID for successful payment
        paymentID, err := h.paymentService.HandlePaystackWebhook(body)
        if err != nil {
                h.logger.WithError(err).Error("Failed to process Paystack webhook")
                c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to process webhook"))
                return
        }

        // If this is a confirmed payment event and we have a payment ID, generate a receipt asynchronously
        if paymentID > 0 && h.receiptService != nil {
                go func(pid uint) {
                        ctx := context.Background()
                        receipt, err := h.receiptService.GenerateReceiptForPayment(ctx, pid)
                        if err != nil {
                                h.logger.WithError(err).Error("Failed to generate receipt for webhook payment")
                        } else {
                                h.logger.Infof("Receipt generated successfully for webhook payment with number: %s", receipt.ReceiptNumber)
                        }
                }(paymentID)
        }

        // Return success response
        c.JSON(http.StatusOK, gin.H{"message": "Webhook processed successfully"})
}

// FlutterwaveWebhook handles Flutterwave webhook events
func (h *PaymentHandler) FlutterwaveWebhook(c *gin.Context) {
        // Read request body
        body, err := c.GetRawData()
        if err != nil {
                h.logger.WithError(err).Error("Failed to read Flutterwave webhook body")
                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid webhook payload"))
                return
        }

        // Verify Flutterwave signature (in a real-world implementation)
        // ...

        // Process webhook - could return payment ID for successful payment
        paymentID, err := h.paymentService.HandleFlutterwaveWebhook(body)
        if err != nil {
                h.logger.WithError(err).Error("Failed to process Flutterwave webhook")
                c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to process webhook"))
                return
        }

        // If this is a confirmed payment event and we have a payment ID, generate a receipt asynchronously
        if paymentID > 0 && h.receiptService != nil {
                go func(pid uint) {
                        ctx := context.Background()
                        receipt, err := h.receiptService.GenerateReceiptForPayment(ctx, pid)
                        if err != nil {
                                h.logger.WithError(err).Error("Failed to generate receipt for webhook payment")
                        } else {
                                h.logger.Infof("Receipt generated successfully for webhook payment with number: %s", receipt.ReceiptNumber)
                        }
                }(paymentID)
        }

        // Return success response
        c.JSON(http.StatusOK, gin.H{"message": "Webhook processed successfully"})
}

// SquadWebhook handles Squad webhook events
func (h *PaymentHandler) SquadWebhook(c *gin.Context) {
        // Read request body
        body, err := c.GetRawData()
        if err != nil {
                h.logger.WithError(err).Error("Failed to read Squad webhook body")
                c.JSON(http.StatusBadRequest, errors.ErrInvalidRequest("Invalid webhook payload"))
                return
        }

        // Verify Squad signature (in a real-world implementation)
        // ...

        // Process webhook - could return payment ID for successful payment
        paymentID, err := h.paymentService.HandleSquadWebhook(body)
        if err != nil {
                h.logger.WithError(err).Error("Failed to process Squad webhook")
                c.JSON(http.StatusInternalServerError, errors.ErrInternalServer("Failed to process webhook"))
                return
        }

        // If this is a confirmed payment event and we have a payment ID, generate a receipt asynchronously
        if paymentID > 0 && h.receiptService != nil {
                go func(pid uint) {
                        ctx := context.Background()
                        receipt, err := h.receiptService.GenerateReceiptForPayment(ctx, pid)
                        if err != nil {
                                h.logger.WithError(err).Error("Failed to generate receipt for webhook payment")
                        } else {
                                h.logger.Infof("Receipt generated successfully for webhook payment with number: %s", receipt.ReceiptNumber)
                        }
                }(paymentID)
        }

        // Return success response
        c.JSON(http.StatusOK, gin.H{"message": "Webhook processed successfully"})
}
