package handlers

import (
        "fmt"
        "io"
        "net/http"
        "strconv"

        "github.com/gin-gonic/gin"
        "github.com/greatnigeria/internal/payment/models"
        "github.com/greatnigeria/internal/payment/service"
)

// ReceiptHandler handles receipt-related HTTP requests
type ReceiptHandler struct {
        receiptService service.ReceiptService
}

// NewReceiptHandler creates a new receipt handler
func NewReceiptHandler(receiptService service.ReceiptService) *ReceiptHandler {
        return &ReceiptHandler{
                receiptService: receiptService,
        }
}

// GenerateReceiptForPayment generates a receipt for a payment
func (h *ReceiptHandler) GenerateReceiptForPayment(c *gin.Context) {
        paymentID, err := strconv.ParseUint(c.Param("paymentId"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payment ID"})
                return
        }

        receipt, err := h.receiptService.GenerateReceiptForPayment(c.Request.Context(), uint(paymentID))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate receipt: %v", err)})
                return
        }

        c.JSON(http.StatusOK, gin.H{
                "success": true,
                "message": "Receipt generation initiated",
                "data": receipt,
        })
}

// GenerateReceiptForSubscription generates a receipt for a subscription
func (h *ReceiptHandler) GenerateReceiptForSubscription(c *gin.Context) {
        subscriptionID, err := strconv.ParseUint(c.Param("subscriptionId"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid subscription ID"})
                return
        }

        receipt, err := h.receiptService.GenerateReceiptForSubscription(c.Request.Context(), uint(subscriptionID))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate receipt: %v", err)})
                return
        }

        c.JSON(http.StatusOK, gin.H{
                "success": true,
                "message": "Receipt generation initiated",
                "data": receipt,
        })
}

// GenerateReceiptForRefund generates a receipt for a refund
func (h *ReceiptHandler) GenerateReceiptForRefund(c *gin.Context) {
        refundID, err := strconv.ParseUint(c.Param("refundId"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid refund ID"})
                return
        }

        receipt, err := h.receiptService.GenerateReceiptForRefund(c.Request.Context(), uint(refundID))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate receipt: %v", err)})
                return
        }

        c.JSON(http.StatusOK, gin.H{
                "success": true,
                "message": "Receipt generation initiated",
                "data": receipt,
        })
}

// GetReceiptByID retrieves a receipt by its ID
func (h *ReceiptHandler) GetReceiptByID(c *gin.Context) {
        receiptID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid receipt ID"})
                return
        }

        receipt, err := h.receiptService.GetReceiptByID(c.Request.Context(), uint(receiptID))
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Receipt not found: %v", err)})
                return
        }

        c.JSON(http.StatusOK, gin.H{
                "success": true,
                "data": receipt,
        })
}

// GetReceiptByNumber retrieves a receipt by its receipt number
func (h *ReceiptHandler) GetReceiptByNumber(c *gin.Context) {
        receiptNumber := c.Param("number")
        if receiptNumber == "" {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Receipt number is required"})
                return
        }

        receipt, err := h.receiptService.GetReceiptByNumber(c.Request.Context(), receiptNumber)
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Receipt not found: %v", err)})
                return
        }

        c.JSON(http.StatusOK, gin.H{
                "success": true,
                "data": receipt,
        })
}

// GetUserReceipts retrieves all receipts for the current user
func (h *ReceiptHandler) GetUserReceipts(c *gin.Context) {
        // Get user ID from the authenticated user context
        userID, exists := c.Get("userId")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }

        page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
        pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

        receipts, total, err := h.receiptService.GetReceiptsByUserID(c.Request.Context(), userID.(uint), page, pageSize)
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get receipts: %v", err)})
                return
        }

        c.JSON(http.StatusOK, gin.H{
                "success": true,
                "data": receipts,
                "pagination": gin.H{
                        "total":    total,
                        "page":     page,
                        "pageSize": pageSize,
                        "pages":    (total + int64(pageSize) - 1) / int64(pageSize),
                },
        })
}

// DownloadReceiptPDF downloads the PDF for a receipt
func (h *ReceiptHandler) DownloadReceiptPDF(c *gin.Context) {
        receiptID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid receipt ID"})
                return
        }

        // Verify user permission
        userID, exists := c.Get("userId")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }

        // Check if receipt belongs to the user
        receipt, err := h.receiptService.GetReceiptByID(c.Request.Context(), uint(receiptID))
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Receipt not found: %v", err)})
                return
        }

        if receipt.UserID != userID.(uint) {
                c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to access this receipt"})
                return
        }

        // Check receipt status
        if receipt.Status != models.ReceiptStatusGenerated && receipt.Status != models.ReceiptStatusDelivered {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Receipt PDF not yet generated"})
                return
        }

        // Download PDF
        pdfFile, filename, err := h.receiptService.GetReceiptPDF(c.Request.Context(), uint(receiptID))
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get receipt PDF: %v", err)})
                return
        }
        defer pdfFile.Close()

        c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
        c.Header("Content-Type", "application/pdf")
        c.Status(http.StatusOK)
        c.Stream(func(w io.Writer) bool {
                _, err := io.Copy(w, pdfFile)
                return err == nil
        })
}

// EmailReceiptToUser sends a receipt to a user by email
func (h *ReceiptHandler) EmailReceiptToUser(c *gin.Context) {
        receiptID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid receipt ID"})
                return
        }

        // Parse request body
        var request struct {
                EmailAddress string `json:"emailAddress" binding:"required,email"`
        }
        if err := c.ShouldBindJSON(&request); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
                return
        }

        // Verify user permission
        userID, exists := c.Get("userId")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }

        // Check if receipt belongs to the user
        receipt, err := h.receiptService.GetReceiptByID(c.Request.Context(), uint(receiptID))
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Receipt not found: %v", err)})
                return
        }

        if receipt.UserID != userID.(uint) {
                c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to access this receipt"})
                return
        }

        // Send email
        if err := h.receiptService.EmailReceiptToUser(c.Request.Context(), uint(receiptID), request.EmailAddress); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to email receipt: %v", err)})
                return
        }

        c.JSON(http.StatusOK, gin.H{
                "success": true,
                "message": fmt.Sprintf("Receipt emailed to %s", request.EmailAddress),
        })
}

// Templates and customization handlers

// CreateReceiptTemplate creates a new receipt template
func (h *ReceiptHandler) CreateReceiptTemplate(c *gin.Context) {
        // Verify admin permission
        userID, exists := c.Get("userId")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }

        // Check admin role (simplified, replace with actual admin check)
        isAdmin, exists := c.Get("isAdmin")
        if !exists || !isAdmin.(bool) {
                c.JSON(http.StatusForbidden, gin.H{"error": "Administrator privileges required"})
                return
        }

        // Parse request body
        var request struct {
                Name            string `json:"name" binding:"required"`
                TemplateContent string `json:"templateContent" binding:"required"`
                HeaderImagePath string `json:"headerImagePath"`
                FooterText      string `json:"footerText"`
                CustomCSS       string `json:"customCSS"`
                IsDefault       bool   `json:"isDefault"`
        }
        if err := c.ShouldBindJSON(&request); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
                return
        }

        // Create template
        template, err := h.receiptService.CreateReceiptTemplate(
                c.Request.Context(),
                request.Name,
                request.TemplateContent,
                request.HeaderImagePath,
                request.FooterText,
                request.CustomCSS,
                request.IsDefault,
                userID.(uint),
        )
        if err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create receipt template: %v", err)})
                return
        }

        c.JSON(http.StatusCreated, gin.H{
                "success": true,
                "message": "Receipt template created successfully",
                "data":    template,
        })
}

// CreateReceiptCustomization creates a customization for a receipt
func (h *ReceiptHandler) CreateReceiptCustomization(c *gin.Context) {
        receiptID, err := strconv.ParseUint(c.Param("id"), 10, 64)
        if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid receipt ID"})
                return
        }

        // Verify user permission
        userID, exists := c.Get("userId")
        if !exists {
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }

        // Check if receipt belongs to the user
        receipt, err := h.receiptService.GetReceiptByID(c.Request.Context(), uint(receiptID))
        if err != nil {
                c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Receipt not found: %v", err)})
                return
        }

        if receipt.UserID != userID.(uint) {
                c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to access this receipt"})
                return
        }

        // Parse request body
        var customization models.ReceiptCustomization
        if err := c.ShouldBindJSON(&customization); err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
                return
        }

        // Create customization
        if err := h.receiptService.CreateReceiptCustomization(c.Request.Context(), uint(receiptID), &customization); err != nil {
                c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create receipt customization: %v", err)})
                return
        }

        c.JSON(http.StatusCreated, gin.H{
                "success": true,
                "message": "Receipt customization created successfully",
                "data":    customization,
        })
}

// RegisterRoutes registers the receipt handler routes
func (h *ReceiptHandler) RegisterRoutes(router *gin.RouterGroup, authMiddleware gin.HandlerFunc, adminMiddleware gin.HandlerFunc) {
        receipts := router.Group("/receipts")
        {
                // Public routes
                receipts.GET("/view/:number", h.GetReceiptByNumber)

                // User routes (requires authentication)
                authenticated := receipts.Group("/")
                authenticated.Use(authMiddleware)
                {
                        authenticated.GET("", h.GetUserReceipts)
                        authenticated.GET("/:id", h.GetReceiptByID)
                        authenticated.GET("/:id/download", h.DownloadReceiptPDF)
                        authenticated.POST("/:id/email", h.EmailReceiptToUser)
                        authenticated.POST("/:id/customization", h.CreateReceiptCustomization)
                        
                        // Generate receipts
                        authenticated.POST("/payment/:paymentId", h.GenerateReceiptForPayment)
                        authenticated.POST("/subscription/:subscriptionId", h.GenerateReceiptForSubscription)
                        authenticated.POST("/refund/:refundId", h.GenerateReceiptForRefund)
                }

                // Admin routes (requires admin privileges)
                admin := receipts.Group("/admin")
                admin.Use(authMiddleware, adminMiddleware)
                {
                        admin.POST("/templates", h.CreateReceiptTemplate)
                }
        }
}