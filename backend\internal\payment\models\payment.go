package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
)

// PaymentProvider represents the payment processor used
type PaymentProvider string

const (
	// ProviderPaystack for Paystack payments
	ProviderPaystack PaymentProvider = "paystack"
	
	// ProviderFlutterwave for Flutterwave payments
	ProviderFlutterwave PaymentProvider = "flutterwave"
	
	// ProviderSquad for Squad payments
	ProviderSquad PaymentProvider = "squad"
)

// PaymentStatus represents the current status of a payment
type PaymentStatus string

const (
	// StatusPending means the payment is initiated but not completed
	StatusPending PaymentStatus = "pending"
	
	// StatusProcessing means the payment is being processed
	StatusProcessing PaymentStatus = "processing"
	
	// StatusSucceeded means the payment was successful
	StatusSucceeded PaymentStatus = "succeeded"
	
	// StatusFailed means the payment failed
	StatusFailed PaymentStatus = "failed"
	
	// StatusCancelled means the payment was cancelled
	StatusCancelled PaymentStatus = "cancelled"
	
	// StatusRefunded means the payment was refunded
	StatusRefunded PaymentStatus = "refunded"
	
	// StatusDisputed means the payment is being disputed
	StatusDisputed PaymentStatus = "disputed"
)

// SubscriptionStatus represents the current status of a subscription
type SubscriptionStatus string

const (
	// SubscriptionStatusActive means the subscription is active
	SubscriptionStatusActive SubscriptionStatus = "active"
	
	// SubscriptionStatusCancelled means the subscription was cancelled
	SubscriptionStatusCancelled SubscriptionStatus = "cancelled"
	
	// SubscriptionStatusPaused means the subscription is paused
	SubscriptionStatusPaused SubscriptionStatus = "paused"
	
	// SubscriptionStatusExpired means the subscription has expired
	SubscriptionStatusExpired SubscriptionStatus = "expired"
	
	// SubscriptionStatusTrialing means the subscription is in trial period
	SubscriptionStatusTrialing SubscriptionStatus = "trialing"
	
	// SubscriptionStatusPastDue means the subscription payment is past due
	SubscriptionStatusPastDue SubscriptionStatus = "past_due"
	
	// SubscriptionStatusIncomplete means the subscription setup is incomplete
	SubscriptionStatusIncomplete SubscriptionStatus = "incomplete"
	
	// SubscriptionStatusUnpaid means the subscription has unpaid invoices
	SubscriptionStatusUnpaid SubscriptionStatus = "unpaid"
)

// PlanInterval represents the billing interval for a subscription plan
type PlanInterval string

const (
	// IntervalDaily for daily billing
	IntervalDaily PlanInterval = "daily"
	
	// IntervalWeekly for weekly billing
	IntervalWeekly PlanInterval = "weekly"
	
	// IntervalMonthly for monthly billing
	IntervalMonthly PlanInterval = "monthly"
	
	// IntervalQuarterly for quarterly billing
	IntervalQuarterly PlanInterval = "quarterly"
	
	// IntervalBiannually for bi-annual billing
	IntervalBiannually PlanInterval = "biannually"
	
	// IntervalAnnually for annual billing
	IntervalAnnually PlanInterval = "annually"
)

// Currency represents the currency used for a payment
type Currency string

const (
	// CurrencyNGN for Nigerian Naira
	CurrencyNGN Currency = "NGN"
	
	// CurrencyUSD for US Dollar
	CurrencyUSD Currency = "USD"
)

// JSONB type for storing JSON in PostgreSQL
type JSONB map[string]interface{}

// Value implements the driver.Valuer interface for JSONB
func (j JSONB) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan implements the sql.Scanner interface for JSONB
func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}
	
	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return errors.New("type assertion to []byte failed")
	}
	
	return json.Unmarshal(data, &j)
}

// PaymentIntent represents a user's intent to make a payment
type PaymentIntent struct {
	gorm.Model
	UserID            uint            `json:"userId" gorm:"index"`
	Amount            int             `json:"amount"` // in smallest currency unit (kobo/cents)
	Currency          Currency        `json:"currency" gorm:"type:varchar(3)"`
	Description       string          `json:"description"`
	Status            PaymentStatus   `json:"status" gorm:"type:varchar(20)"`
	Provider          PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	PaymentMethodType string          `json:"paymentMethodType" gorm:"type:varchar(50)"`
	ClientSecret      string          `json:"clientSecret" gorm:"type:varchar(255)"`
	ProviderReference string          `json:"providerReference" gorm:"type:varchar(100);index"`
	SuccessURL        string          `json:"successUrl" gorm:"type:varchar(255)"`
	CancelURL         string          `json:"cancelUrl" gorm:"type:varchar(255)"`
	MetaData          JSONB           `json:"metaData" gorm:"type:jsonb"`
	ExpiresAt         time.Time       `json:"expiresAt"`
}

// Payment represents a completed payment
type Payment struct {
	gorm.Model
	UserID                 uint            `json:"userId" gorm:"index"`
	PaymentIntentID        uint            `json:"paymentIntentId" gorm:"index"`
	SubscriptionID         *uint           `json:"subscriptionId" gorm:"index"`
	Amount                 int             `json:"amount"` // in smallest currency unit (kobo/cents)
	AmountCaptured         int             `json:"amountCaptured"`
	AmountRefunded         int             `json:"amountRefunded"`
	Currency               Currency        `json:"currency" gorm:"type:varchar(3)"`
	Status                 PaymentStatus   `json:"status" gorm:"type:varchar(20)"`
	Provider               PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	ProviderReference      string          `json:"providerReference" gorm:"type:varchar(100);index"`
	ProviderPaymentID      string          `json:"providerPaymentId" gorm:"type:varchar(100);index"`
	ReceiptNumber          string          `json:"receiptNumber" gorm:"type:varchar(50)"`
	ReceiptURL             string          `json:"receiptUrl" gorm:"type:varchar(255)"`
	Description            string          `json:"description"`
	StatementDescriptor    string          `json:"statementDescriptor"`
	PaymentMethodType      string          `json:"paymentMethodType" gorm:"type:varchar(50)"`
	PaymentMethodDetails   JSONB           `json:"paymentMethodDetails" gorm:"type:jsonb"`
	BillingDetails         JSONB           `json:"billingDetails" gorm:"type:jsonb"`
	MetaData               JSONB           `json:"metaData" gorm:"type:jsonb"`
	FailureCode            string          `json:"failureCode" gorm:"type:varchar(100)"`
	FailureMessage         string          `json:"failureMessage" gorm:"type:text"`
	LastPaymentError       string          `json:"lastPaymentError" gorm:"type:text"`
	PaidAt                 *time.Time      `json:"paidAt"`
	RefundedAt             *time.Time      `json:"refundedAt"`
	DisputedAt             *time.Time      `json:"disputedAt"`
	CancelledAt            *time.Time      `json:"cancelledAt"`
}

// SubscriptionPlan defines a recurring payment plan
type SubscriptionPlan struct {
	gorm.Model
	Name                  string       `json:"name" gorm:"type:varchar(100)"`
	Description           string       `json:"description" gorm:"type:text"`
	Amount                int          `json:"amount"` // in smallest currency unit (kobo/cents)
	Currency              Currency     `json:"currency" gorm:"type:varchar(3)"`
	Interval              PlanInterval `json:"interval" gorm:"type:varchar(20)"`
	IntervalCount         int          `json:"intervalCount"` // e.g., 1 for monthly, 3 for quarterly
	TrialPeriodDays       int          `json:"trialPeriodDays"`
	ProviderPlanID        string       `json:"providerPlanId" gorm:"type:varchar(100);index"`
	ProviderPaystackID    string       `json:"providerPaystackId" gorm:"type:varchar(100)"`
	ProviderFlutterwaveID string       `json:"providerFlutterwaveId" gorm:"type:varchar(100)"`
	ProviderSquadID       string       `json:"providerSquadId" gorm:"type:varchar(100)"`
	Features              JSONB        `json:"features" gorm:"type:jsonb"`
	MetaData              JSONB        `json:"metaData" gorm:"type:jsonb"`
	IsActive              bool         `json:"isActive" gorm:"default:true"`
	CreatedBy             uint         `json:"createdBy"`
	UpdatedBy             uint         `json:"updatedBy"`
}

// Subscription represents a user's subscription to a plan
type Subscription struct {
	gorm.Model
	UserID                 uint               `json:"userId" gorm:"index"`
	PlanID                 uint               `json:"planId" gorm:"index"`
	Plan                   SubscriptionPlan   `json:"plan" gorm:"-"`
	Status                 SubscriptionStatus `json:"status" gorm:"type:varchar(20)"`
	Provider               PaymentProvider    `json:"provider" gorm:"type:varchar(20)"`
	ProviderSubscriptionID string             `json:"providerSubscriptionId" gorm:"type:varchar(100);index"`
	CurrentPeriodStart     time.Time          `json:"currentPeriodStart"`
	CurrentPeriodEnd       time.Time          `json:"currentPeriodEnd"`
	CancelAtPeriodEnd      bool               `json:"cancelAtPeriodEnd"`
	CanceledAt             *time.Time         `json:"canceledAt"`
	TrialStart             *time.Time         `json:"trialStart"`
	TrialEnd               *time.Time         `json:"trialEnd"`
	DefaultPaymentMethodID string             `json:"defaultPaymentMethodId" gorm:"type:varchar(100)"`
	LatestPaymentID        *uint              `json:"latestPaymentId"`
	NextPaymentAttempt     *time.Time         `json:"nextPaymentAttempt"`
	FailedPaymentsCount    int                `json:"failedPaymentsCount" gorm:"default:0"`
	MetaData               JSONB              `json:"metaData" gorm:"type:jsonb"`
}

// PaymentMethod represents a stored payment method
type PaymentMethod struct {
	gorm.Model
	UserID               uint            `json:"userId" gorm:"index"`
	Provider             PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	ProviderPaymentID    string          `json:"providerPaymentId" gorm:"type:varchar(100);index"`
	Type                 string          `json:"type" gorm:"type:varchar(50)"`
	BillingName          string          `json:"billingName" gorm:"type:varchar(255)"`
	BillingEmail         string          `json:"billingEmail" gorm:"type:varchar(255)"`
	BillingPhone         string          `json:"billingPhone" gorm:"type:varchar(50)"`
	BillingAddressLine1  string          `json:"billingAddressLine1" gorm:"type:varchar(255)"`
	BillingAddressLine2  string          `json:"billingAddressLine2" gorm:"type:varchar(255)"`
	BillingCity          string          `json:"billingCity" gorm:"type:varchar(100)"`
	BillingState         string          `json:"billingState" gorm:"type:varchar(100)"`
	BillingPostalCode    string          `json:"billingPostalCode" gorm:"type:varchar(20)"`
	BillingCountry       string          `json:"billingCountry" gorm:"type:varchar(100)"`
	CardLast4            string          `json:"cardLast4" gorm:"type:varchar(4)"`
	CardExpiryMonth      string          `json:"cardExpiryMonth" gorm:"type:varchar(2)"`
	CardExpiryYear       string          `json:"cardExpiryYear" gorm:"type:varchar(4)"`
	CardBrand            string          `json:"cardBrand" gorm:"type:varchar(50)"`
	BankAccountLast4     string          `json:"bankAccountLast4" gorm:"type:varchar(4)"`
	BankAccountHolderName string         `json:"bankAccountHolderName" gorm:"type:varchar(255)"`
	BankAccountHolderType string         `json:"bankAccountHolderType" gorm:"type:varchar(50)"`
	BankName             string          `json:"bankName" gorm:"type:varchar(100)"`
	BankCode             string          `json:"bankCode" gorm:"type:varchar(20)"`
	IsDefault            bool            `json:"isDefault" gorm:"default:false"`
	MetaData             JSONB           `json:"metaData" gorm:"type:jsonb"`
	ExpiresAt            *time.Time      `json:"expiresAt"`
}

// Refund represents a payment refund
type Refund struct {
	gorm.Model
	UserID             uint            `json:"userId" gorm:"index"`
	PaymentID          uint            `json:"paymentId" gorm:"index"`
	Amount             int             `json:"amount"` // in smallest currency unit (kobo/cents)
	Currency           Currency        `json:"currency" gorm:"type:varchar(3)"`
	Status             string          `json:"status" gorm:"type:varchar(20)"`
	Reason             string          `json:"reason" gorm:"type:varchar(255)"`
	Provider           PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	ProviderRefundID   string          `json:"providerRefundId" gorm:"type:varchar(100);index"`
	ReceiptNumber      string          `json:"receiptNumber" gorm:"type:varchar(50)"`
	ReceiptURL         string          `json:"receiptUrl" gorm:"type:varchar(255)"`
	FailureReason      string          `json:"failureReason" gorm:"type:text"`
	MetaData           JSONB           `json:"metaData" gorm:"type:jsonb"`
	ProcessedBy        uint            `json:"processedBy"`
	ApprovedBy         *uint           `json:"approvedBy"`
	ApprovedAt         *time.Time      `json:"approvedAt"`
}

// Dispute represents a payment dispute
type Dispute struct {
	gorm.Model
	UserID                uint            `json:"userId" gorm:"index"`
	PaymentID             uint            `json:"paymentId" gorm:"index"`
	Amount                int             `json:"amount"` // in smallest currency unit (kobo/cents)
	Currency              Currency        `json:"currency" gorm:"type:varchar(3)"`
	Status                string          `json:"status" gorm:"type:varchar(20)"`
	Reason                string          `json:"reason" gorm:"type:varchar(255)"`
	Provider              PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	ProviderDisputeID     string          `json:"providerDisputeID" gorm:"type:varchar(100);index"`
	Evidence              JSONB           `json:"evidence" gorm:"type:jsonb"`
	EvidenceDetails       string          `json:"evidenceDetails" gorm:"type:text"`
	DueBy                 time.Time       `json:"dueBy"`
	IsRefundPending       bool            `json:"isRefundPending" gorm:"default:false"`
	RefundID              *uint           `json:"refundId"`
	MetaData              JSONB           `json:"metaData" gorm:"type:jsonb"`
	AssignedTo            *uint           `json:"assignedTo"`
	ResolvedAt            *time.Time      `json:"resolvedAt"`
	ResolvedBy            *uint           `json:"resolvedBy"`
	ResolutionNotes       string          `json:"resolutionNotes" gorm:"type:text"`
}

// Payout represents money paid out to a creator (for gifting/donations)
type Payout struct {
	gorm.Model
	UserID            uint            `json:"userId" gorm:"index"` // creator receiving payout
	Amount            int             `json:"amount"` // in smallest currency unit (kobo/cents)
	Currency          Currency        `json:"currency" gorm:"type:varchar(3)"`
	Status            string          `json:"status" gorm:"type:varchar(20)"`
	Provider          PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	ProviderPayoutID  string          `json:"providerPayoutId" gorm:"type:varchar(100);index"`
	Description       string          `json:"description"`
	PayoutMethod      string          `json:"payoutMethod" gorm:"type:varchar(50)"`
	PayoutDetails     JSONB           `json:"payoutDetails" gorm:"type:jsonb"`
	ArrivalDate       *time.Time      `json:"arrivalDate"`
	FailureCode       string          `json:"failureCode" gorm:"type:varchar(100)"`
	FailureMessage    string          `json:"failureMessage" gorm:"type:text"`
	MetaData          JSONB           `json:"metaData" gorm:"type:jsonb"`
	ProcessedBy       uint            `json:"processedBy"`
	ApprovedBy        *uint           `json:"approvedBy"`
	ApprovedAt        *time.Time      `json:"approvedAt"`
}

// Promotion represents a discount code or promotion
type Promotion struct {
	gorm.Model
	Code                  string    `json:"code" gorm:"type:varchar(50);uniqueIndex"`
	Description           string    `json:"description" gorm:"type:text"`
	DiscountType          string    `json:"discountType" gorm:"type:varchar(20)"` // percentage, fixed_amount
	DiscountAmount        int       `json:"discountAmount"`                       // percentage or fixed amount value
	Currency              Currency  `json:"currency" gorm:"type:varchar(3)"`
	MinimumAmount         int       `json:"minimumAmount"` // minimum purchase amount to apply
	MaxUsageCount         int       `json:"maxUsageCount"` // maximum number of times it can be used
	UsageCount            int       `json:"usageCount" gorm:"default:0"`
	MaxUsagePerUser       int       `json:"maxUsagePerUser"`
	StartDate             time.Time `json:"startDate"`
	EndDate               time.Time `json:"endDate"`
	IsActive              bool      `json:"isActive" gorm:"default:true"`
	ApplicableProductIDs  string    `json:"applicableProductIDs" gorm:"type:text"` // comma-separated list of product IDs
	ApplicablePlanIDs     string    `json:"applicablePlanIDs" gorm:"type:text"`    // comma-separated list of plan IDs
	CreatedBy             uint      `json:"createdBy"`
	UpdatedBy             uint      `json:"updatedBy"`
}

// PromotionUsage tracks usage of promotions by users
type PromotionUsage struct {
	gorm.Model
	UserID       uint      `json:"userId" gorm:"index"`
	PromotionID  uint      `json:"promotionId" gorm:"index"`
	PaymentID    *uint     `json:"paymentId"`
	DiscountType string    `json:"discountType" gorm:"type:varchar(20)"`
	DiscountAmount int     `json:"discountAmount"`
	Currency     Currency  `json:"currency" gorm:"type:varchar(3)"`
	AppliedAt    time.Time `json:"appliedAt"`
}

// Gift represents a gift purchase (virtual gifting system)
type Gift struct {
	gorm.Model
	SenderUserID   uint            `json:"senderUserId" gorm:"index"`
	RecipientUserID uint           `json:"recipientUserId" gorm:"index"`
	ContentID      uint            `json:"contentId" gorm:"index"` // associated content (if applicable)
	ContentType    string          `json:"contentType" gorm:"type:varchar(50)"`
	GiftType       string          `json:"giftType" gorm:"type:varchar(50)"` // e.g., "badge", "award", "donation"
	Amount         int             `json:"amount"`                           // in smallest currency unit (kobo/cents)
	CreatorAmount  int             `json:"creatorAmount"`                    // amount for creator (after platform fees)
	Currency       Currency        `json:"currency" gorm:"type:varchar(3)"`
	PaymentID      uint            `json:"paymentId" gorm:"index"`
	PayoutID       *uint           `json:"payoutId"`
	Provider       PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	Status         string          `json:"status" gorm:"type:varchar(20)"`
	Message        string          `json:"message" gorm:"type:text"`
	IsAnonymous    bool            `json:"isAnonymous" gorm:"default:false"`
	MetaData       JSONB           `json:"metaData" gorm:"type:jsonb"`
	CompletedAt    *time.Time      `json:"completedAt"`
}

// Invoice represents a billing invoice for subscriptions
type Invoice struct {
	gorm.Model
	UserID            uint            `json:"userId" gorm:"index"`
	SubscriptionID    *uint           `json:"subscriptionId" gorm:"index"`
	Amount            int             `json:"amount"` // in smallest currency unit (kobo/cents)
	AmountPaid        int             `json:"amountPaid"`
	AmountRemaining   int             `json:"amountRemaining"`
	Currency          Currency        `json:"currency" gorm:"type:varchar(3)"`
	Status            string          `json:"status" gorm:"type:varchar(20)"`
	Provider          PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	ProviderInvoiceID string          `json:"providerInvoiceId" gorm:"type:varchar(100);index"`
	Description       string          `json:"description"`
	InvoiceNumber     string          `json:"invoiceNumber" gorm:"type:varchar(50)"`
	InvoiceURL        string          `json:"invoiceUrl" gorm:"type:varchar(255)"`
	InvoicePDF        string          `json:"invoicePdf" gorm:"type:varchar(255)"`
	PaymentID         *uint           `json:"paymentId"`
	AttemptCount      int             `json:"attemptCount" gorm:"default:0"`
	NextAttempt       *time.Time      `json:"nextAttempt"`
	PeriodStart       time.Time       `json:"periodStart"`
	PeriodEnd         time.Time       `json:"periodEnd"`
	DueDate           time.Time       `json:"dueDate"`
	PaidAt            *time.Time      `json:"paidAt"`
	MetaData          JSONB           `json:"metaData" gorm:"type:jsonb"`
	Lines             JSONB           `json:"lines" gorm:"type:jsonb"` // Invoice line items
}

// VirtualAccount represents a virtual account for a user
type VirtualAccount struct {
	gorm.Model
	UserID                 uint            `json:"userId" gorm:"index"`
	Provider               PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	ProviderVirtualAcctID  string          `json:"providerVirtualAcctId" gorm:"type:varchar(100);index"`
	AccountNumber          string          `json:"accountNumber" gorm:"type:varchar(50)"`
	AccountName            string          `json:"accountName" gorm:"type:varchar(255)"`
	BankName               string          `json:"bankName" gorm:"type:varchar(100)"`
	BankCode               string          `json:"bankCode" gorm:"type:varchar(20)"`
	Currency               Currency        `json:"currency" gorm:"type:varchar(3)"`
	Status                 string          `json:"status" gorm:"type:varchar(20)"`
	IsActive               bool            `json:"isActive" gorm:"default:true"`
	Reference              string          `json:"reference" gorm:"type:varchar(100)"`
	ExpiresAt              *time.Time      `json:"expiresAt"`
	MetaData               JSONB           `json:"metaData" gorm:"type:jsonb"`
}

// WebhookEvent represents an event received from payment providers
type WebhookEvent struct {
	gorm.Model
	Provider        PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	EventType       string          `json:"eventType" gorm:"type:varchar(100)"`
	EventID         string          `json:"eventId" gorm:"type:varchar(100)"`
	ResourceType    string          `json:"resourceType" gorm:"type:varchar(50)"`
	ResourceID      string          `json:"resourceId" gorm:"type:varchar(100)"`
	PaymentID       *uint           `json:"paymentId"`
	SubscriptionID  *uint           `json:"subscriptionId"`
	UserID          *uint           `json:"userId"`
	Payload         JSONB           `json:"payload" gorm:"type:jsonb"`
	ProcessedAt     *time.Time      `json:"processedAt"`
	IsProcessed     bool            `json:"isProcessed" gorm:"default:false"`
	ProcessingError string          `json:"processingError" gorm:"type:text"`
}

// PaymentAnalytics stores analytics data for payments
type PaymentAnalytics struct {
	gorm.Model
	Date                time.Time       `json:"date" gorm:"index"`
	Provider            PaymentProvider `json:"provider" gorm:"type:varchar(20)"`
	TotalTransactions   int             `json:"totalTransactions"`
	SuccessfulPayments  int             `json:"successfulPayments"`
	FailedPayments      int             `json:"failedPayments"`
	TotalRevenue        int             `json:"totalRevenue"`
	TotalRefunds        int             `json:"totalRefunds"`
	NewSubscriptions    int             `json:"newSubscriptions"`
	CancelledSubs       int             `json:"cancelledSubs"`
	ActiveSubscriptions int             `json:"activeSubscriptions"`
	ConversionRate      float64         `json:"conversionRate"`
	AverageOrderValue   float64         `json:"averageOrderValue"`
	Currency            Currency        `json:"currency" gorm:"type:varchar(3)"`
}