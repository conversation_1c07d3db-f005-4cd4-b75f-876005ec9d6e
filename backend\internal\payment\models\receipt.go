package models

import (
	"time"

	"gorm.io/gorm"
)

// ReceiptType represents the type of receipt
type ReceiptType string

const (
	// ReceiptTypePayment for regular payment receipts
	ReceiptTypePayment ReceiptType = "payment"
	
	// ReceiptTypeSubscription for subscription payment receipts
	ReceiptTypeSubscription ReceiptType = "subscription"
	
	// ReceiptTypeRefund for refund receipts
	ReceiptTypeRefund ReceiptType = "refund"
)

// ReceiptStatus represents the status of a receipt
type ReceiptStatus string

const (
	// ReceiptStatusPending means the receipt is being generated
	ReceiptStatusPending ReceiptStatus = "pending"
	
	// ReceiptStatusGenerated means the receipt has been successfully generated
	ReceiptStatusGenerated ReceiptStatus = "generated"
	
	// ReceiptStatusFailed means the receipt generation failed
	ReceiptStatusFailed ReceiptStatus = "failed"
	
	// ReceiptStatusDelivered means the receipt has been delivered to the user
	ReceiptStatusDelivered ReceiptStatus = "delivered"
)

// Receipt represents a payment receipt
type Receipt struct {
	gorm.Model
	UserID          uint           `json:"userId" gorm:"index"`
	PaymentID       *uint          `json:"paymentId" gorm:"index"`
	SubscriptionID  *uint          `json:"subscriptionId" gorm:"index"`
	RefundID        *uint          `json:"refundId" gorm:"index"`
	Type            ReceiptType    `json:"type" gorm:"type:varchar(20)"`
	Status          ReceiptStatus  `json:"status" gorm:"type:varchar(20)"`
	ReceiptNumber   string         `json:"receiptNumber" gorm:"type:varchar(50);uniqueIndex"`
	Amount          int            `json:"amount"` // in smallest currency unit (kobo/cents)
	Currency        Currency       `json:"currency" gorm:"type:varchar(3)"`
	Description     string         `json:"description" gorm:"type:varchar(255)"`
	PDFPath         string         `json:"pdfPath" gorm:"type:varchar(255)"`
	PublicURL       string         `json:"publicUrl" gorm:"type:varchar(255)"`
	ContentHash     string         `json:"contentHash" gorm:"type:varchar(64)"` // SHA-256 hash of the PDF for integrity checking
	EmailedTo       string         `json:"emailedTo" gorm:"type:varchar(255)"`
	EmailedAt       *time.Time     `json:"emailedAt"`
	GeneratedAt     *time.Time     `json:"generatedAt"`
	IssuedAt        time.Time      `json:"issuedAt"` // Business date of the receipt
	MetaData        JSONB          `json:"metaData" gorm:"type:jsonb"`
	ErrorMessage    string         `json:"errorMessage" gorm:"type:text"`
}

// ReceiptTemplate represents a configurable template for receipts
type ReceiptTemplate struct {
	gorm.Model
	Name              string   `json:"name" gorm:"type:varchar(100)"`
	Type              string   `json:"type" gorm:"type:varchar(20)"` // html, pdf, etc.
	TemplateContent   string   `json:"templateContent" gorm:"type:text"` // HTML template or JSON structure
	HeaderImagePath   string   `json:"headerImagePath" gorm:"type:varchar(255)"`
	FooterText        string   `json:"footerText" gorm:"type:text"`
	CustomCSS         string   `json:"customCss" gorm:"type:text"`
	IsDefault         bool     `json:"isDefault" gorm:"default:false"`
	IsActive          bool     `json:"isActive" gorm:"default:true"`
	CreatedBy         uint     `json:"createdBy"`
	UpdatedBy         uint     `json:"updatedBy"`
	LastUsed          *time.Time `json:"lastUsed"`
}

// ReceiptCustomization allows per-receipt customization
type ReceiptCustomization struct {
	gorm.Model
	ReceiptID        uint     `json:"receiptId" gorm:"uniqueIndex"`
	CompanyName      string   `json:"companyName" gorm:"type:varchar(255)"`
	CompanyAddress   string   `json:"companyAddress" gorm:"type:text"`
	CompanyLogo      string   `json:"companyLogo" gorm:"type:varchar(255)"`
	CompanyEmail     string   `json:"companyEmail" gorm:"type:varchar(255)"`
	CompanyPhone     string   `json:"companyPhone" gorm:"type:varchar(50)"`
	CompanyWebsite   string   `json:"companyWebsite" gorm:"type:varchar(255)"`
	CustomMessage    string   `json:"customMessage" gorm:"type:text"`
	SignatureName    string   `json:"signatureName" gorm:"type:varchar(255)"`
	SignatureImage   string   `json:"signatureImage" gorm:"type:varchar(255)"`
	ThemeColor       string   `json:"themeColor" gorm:"type:varchar(20)"`
	FontName         string   `json:"fontName" gorm:"type:varchar(50)"`
	IncludeTaxBreakdown bool  `json:"includeTaxBreakdown" gorm:"default:true"`
}