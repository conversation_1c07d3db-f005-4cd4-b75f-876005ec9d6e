package repository

import (
	"time"

	"github.com/greatnigeria/internal/payment/models"
	"gorm.io/gorm"
)

// PaymentRepository defines the interface for payment operations
type PaymentRepository interface {
	// Payment Intents
	CreatePaymentIntent(intent *models.PaymentIntent) error
	GetPaymentIntentByID(id uint) (*models.PaymentIntent, error)
	GetPaymentIntentByProviderReference(reference string) (*models.PaymentIntent, error)
	UpdatePaymentIntent(intent *models.PaymentIntent) error
	GetUserPaymentIntents(userID uint, limit, offset int) ([]models.PaymentIntent, error)
	
	// Payments
	CreatePayment(payment *models.Payment) error
	GetPaymentByID(id uint) (*models.Payment, error)
	GetPaymentByProviderReference(reference string) (*models.Payment, error)
	UpdatePayment(payment *models.Payment) error
	GetUserPayments(userID uint, limit, offset int) ([]models.Payment, error)
	GetPaymentsBySubscriptionID(subscriptionID uint) ([]models.Payment, error)
	GetRecentSuccessfulPayments(limit int) ([]models.Payment, error)
	GetPaymentAnalytics(startDate, endDate time.Time) (map[string]interface{}, error)
	
	// Subscription Plans
	CreateSubscriptionPlan(plan *models.SubscriptionPlan) error
	GetSubscriptionPlanByID(id uint) (*models.SubscriptionPlan, error)
	GetSubscriptionPlanByProviderID(providerID string) (*models.SubscriptionPlan, error)
	UpdateSubscriptionPlan(plan *models.SubscriptionPlan) error
	DeleteSubscriptionPlan(id uint) error
	GetAllSubscriptionPlans(includeInactive bool) ([]models.SubscriptionPlan, error)
	
	// Subscriptions
	CreateSubscription(subscription *models.Subscription) error
	GetSubscriptionByID(id uint) (*models.Subscription, error)
	GetSubscriptionByProviderID(providerID string) (*models.Subscription, error)
	UpdateSubscription(subscription *models.Subscription) error
	GetUserSubscriptions(userID uint, active bool) ([]models.Subscription, error)
	GetActiveSubscriptionForUser(userID uint) (*models.Subscription, error)
	GetSubscriptionsByPlanID(planID uint, active bool) ([]models.Subscription, error)
	GetExpiringSubscriptions(days int) ([]models.Subscription, error)
	
	// Payment Methods
	CreatePaymentMethod(method *models.PaymentMethod) error
	GetPaymentMethodByID(id uint) (*models.PaymentMethod, error)
	UpdatePaymentMethod(method *models.PaymentMethod) error
	DeletePaymentMethod(id uint) error
	GetUserPaymentMethods(userID uint) ([]models.PaymentMethod, error)
	GetDefaultPaymentMethod(userID uint) (*models.PaymentMethod, error)
	SetDefaultPaymentMethod(userID, paymentMethodID uint) error
	
	// Refunds
	CreateRefund(refund *models.Refund) error
	GetRefundByID(id uint) (*models.Refund, error)
	UpdateRefund(refund *models.Refund) error
	GetRefundsByPaymentID(paymentID uint) ([]models.Refund, error)
	GetUserRefunds(userID uint, limit, offset int) ([]models.Refund, error)
	
	// Disputes
	CreateDispute(dispute *models.Dispute) error
	GetDisputeByID(id uint) (*models.Dispute, error)
	UpdateDispute(dispute *models.Dispute) error
	GetDisputesByPaymentID(paymentID uint) ([]models.Dispute, error)
	GetUserDisputes(userID uint, limit, offset int) ([]models.Dispute, error)
	GetOpenDisputes(limit, offset int) ([]models.Dispute, error)
	
	// Payouts
	CreatePayout(payout *models.Payout) error
	GetPayoutByID(id uint) (*models.Payout, error)
	UpdatePayout(payout *models.Payout) error
	GetUserPayouts(userID uint, limit, offset int) ([]models.Payout, error)
	GetPendingPayouts(limit, offset int) ([]models.Payout, error)
	
	// Promotions
	CreatePromotion(promotion *models.Promotion) error
	GetPromotionByID(id uint) (*models.Promotion, error)
	GetPromotionByCode(code string) (*models.Promotion, error)
	UpdatePromotion(promotion *models.Promotion) error
	DeletePromotion(id uint) error
	GetActivePromotions() ([]models.Promotion, error)
	RecordPromotionUsage(usage *models.PromotionUsage) error
	GetPromotionUsage(promotionID, userID uint) ([]models.PromotionUsage, error)
	
	// Gifts
	CreateGift(gift *models.Gift) error
	GetGiftByID(id uint) (*models.Gift, error)
	UpdateGift(gift *models.Gift) error
	GetUserSentGifts(userID uint, limit, offset int) ([]models.Gift, error)
	GetUserReceivedGifts(userID uint, limit, offset int) ([]models.Gift, error)
	GetContentGifts(contentID uint, contentType string, limit, offset int) ([]models.Gift, error)
	
	// Invoices
	CreateInvoice(invoice *models.Invoice) error
	GetInvoiceByID(id uint) (*models.Invoice, error)
	UpdateInvoice(invoice *models.Invoice) error
	GetUserInvoices(userID uint, limit, offset int) ([]models.Invoice, error)
	GetUnpaidInvoices(limit, offset int) ([]models.Invoice, error)
	GetInvoicesBySubscriptionID(subscriptionID uint) ([]models.Invoice, error)
	
	// Virtual Accounts
	CreateVirtualAccount(account *models.VirtualAccount) error
	GetVirtualAccountByID(id uint) (*models.VirtualAccount, error)
	UpdateVirtualAccount(account *models.VirtualAccount) error
	GetUserVirtualAccounts(userID uint) ([]models.VirtualAccount, error)
	GetVirtualAccountByAccountNumber(accountNumber string) (*models.VirtualAccount, error)
	
	// Webhook Events
	CreateWebhookEvent(event *models.WebhookEvent) error
	GetWebhookEventByID(id uint) (*models.WebhookEvent, error)
	UpdateWebhookEvent(event *models.WebhookEvent) error
	GetUnprocessedWebhookEvents(limit int) ([]models.WebhookEvent, error)
	
	// Analytics
	SavePaymentAnalytics(analytics *models.PaymentAnalytics) error
	GetPaymentAnalyticsByDateRange(startDate, endDate time.Time) ([]models.PaymentAnalytics, error)
	GetPaymentAnalyticsByProvider(provider models.PaymentProvider, startDate, endDate time.Time) ([]models.PaymentAnalytics, error)
}

// GormPaymentRepository implements PaymentRepository using GORM
type GormPaymentRepository struct {
	db *gorm.DB
}

// NewGormPaymentRepository creates a new payment repository
func NewGormPaymentRepository(db *gorm.DB) *GormPaymentRepository {
	return &GormPaymentRepository{
		db: db,
	}
}

// CreatePaymentIntent creates a new payment intent
func (r *GormPaymentRepository) CreatePaymentIntent(intent *models.PaymentIntent) error {
	return r.db.Create(intent).Error
}

// GetPaymentIntentByID retrieves a payment intent by ID
func (r *GormPaymentRepository) GetPaymentIntentByID(id uint) (*models.PaymentIntent, error) {
	var intent models.PaymentIntent
	if err := r.db.First(&intent, id).Error; err != nil {
		return nil, err
	}
	return &intent, nil
}

// GetPaymentIntentByProviderReference retrieves a payment intent by provider reference
func (r *GormPaymentRepository) GetPaymentIntentByProviderReference(reference string) (*models.PaymentIntent, error) {
	var intent models.PaymentIntent
	if err := r.db.Where("provider_reference = ?", reference).First(&intent).Error; err != nil {
		return nil, err
	}
	return &intent, nil
}

// UpdatePaymentIntent updates a payment intent
func (r *GormPaymentRepository) UpdatePaymentIntent(intent *models.PaymentIntent) error {
	return r.db.Save(intent).Error
}

// GetUserPaymentIntents retrieves payment intents for a user
func (r *GormPaymentRepository) GetUserPaymentIntents(userID uint, limit, offset int) ([]models.PaymentIntent, error) {
	var intents []models.PaymentIntent
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&intents).Error; err != nil {
		return nil, err
	}
	
	return intents, nil
}

// CreatePayment creates a new payment
func (r *GormPaymentRepository) CreatePayment(payment *models.Payment) error {
	return r.db.Create(payment).Error
}

// GetPaymentByID retrieves a payment by ID
func (r *GormPaymentRepository) GetPaymentByID(id uint) (*models.Payment, error) {
	var payment models.Payment
	if err := r.db.First(&payment, id).Error; err != nil {
		return nil, err
	}
	return &payment, nil
}

// GetPaymentByProviderReference retrieves a payment by provider reference
func (r *GormPaymentRepository) GetPaymentByProviderReference(reference string) (*models.Payment, error) {
	var payment models.Payment
	if err := r.db.Where("provider_reference = ?", reference).First(&payment).Error; err != nil {
		return nil, err
	}
	return &payment, nil
}

// UpdatePayment updates a payment
func (r *GormPaymentRepository) UpdatePayment(payment *models.Payment) error {
	return r.db.Save(payment).Error
}

// GetUserPayments retrieves payments for a user
func (r *GormPaymentRepository) GetUserPayments(userID uint, limit, offset int) ([]models.Payment, error) {
	var payments []models.Payment
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&payments).Error; err != nil {
		return nil, err
	}
	
	return payments, nil
}

// GetPaymentsBySubscriptionID retrieves payments for a subscription
func (r *GormPaymentRepository) GetPaymentsBySubscriptionID(subscriptionID uint) ([]models.Payment, error) {
	var payments []models.Payment
	if err := r.db.Where("subscription_id = ?", subscriptionID).Order("created_at DESC").Find(&payments).Error; err != nil {
		return nil, err
	}
	return payments, nil
}

// GetRecentSuccessfulPayments retrieves recent successful payments
func (r *GormPaymentRepository) GetRecentSuccessfulPayments(limit int) ([]models.Payment, error) {
	var payments []models.Payment
	if err := r.db.Where("status = ?", models.StatusSucceeded).Order("created_at DESC").Limit(limit).Find(&payments).Error; err != nil {
		return nil, err
	}
	return payments, nil
}

// GetPaymentAnalytics retrieves payment analytics
func (r *GormPaymentRepository) GetPaymentAnalytics(startDate, endDate time.Time) (map[string]interface{}, error) {
	var totalPayments int64
	var successfulPayments int64
	var failedPayments int64
	var totalRevenue int64
	var totalRefunds int64
	
	// Count total payments
	if err := r.db.Model(&models.Payment{}).Where("created_at BETWEEN ? AND ?", startDate, endDate).Count(&totalPayments).Error; err != nil {
		return nil, err
	}
	
	// Count successful payments
	if err := r.db.Model(&models.Payment{}).Where("status = ? AND created_at BETWEEN ? AND ?", models.StatusSucceeded, startDate, endDate).Count(&successfulPayments).Error; err != nil {
		return nil, err
	}
	
	// Count failed payments
	if err := r.db.Model(&models.Payment{}).Where("status = ? AND created_at BETWEEN ? AND ?", models.StatusFailed, startDate, endDate).Count(&failedPayments).Error; err != nil {
		return nil, err
	}
	
	// Sum total revenue
	if err := r.db.Model(&models.Payment{}).Where("status = ? AND created_at BETWEEN ? AND ?", models.StatusSucceeded, startDate, endDate).Select("COALESCE(SUM(amount), 0)").Row().Scan(&totalRevenue); err != nil {
		return nil, err
	}
	
	// Sum total refunds
	if err := r.db.Model(&models.Refund{}).Where("created_at BETWEEN ? AND ?", startDate, endDate).Select("COALESCE(SUM(amount), 0)").Row().Scan(&totalRefunds); err != nil {
		return nil, err
	}
	
	// Calculate average order value
	var averageOrderValue float64
	if successfulPayments > 0 {
		averageOrderValue = float64(totalRevenue) / float64(successfulPayments)
	}
	
	// Calculate conversion rate
	var conversionRate float64
	if totalPayments > 0 {
		conversionRate = float64(successfulPayments) / float64(totalPayments)
	}
	
	// Get subscription counts
	var newSubscriptions int64
	var cancelledSubscriptions int64
	var activeSubscriptions int64
	
	if err := r.db.Model(&models.Subscription{}).Where("created_at BETWEEN ? AND ?", startDate, endDate).Count(&newSubscriptions).Error; err != nil {
		return nil, err
	}
	
	if err := r.db.Model(&models.Subscription{}).Where("canceled_at BETWEEN ? AND ?", startDate, endDate).Count(&cancelledSubscriptions).Error; err != nil {
		return nil, err
	}
	
	if err := r.db.Model(&models.Subscription{}).Where("status = ? AND current_period_end >= ?", models.SubscriptionStatusActive, time.Now()).Count(&activeSubscriptions).Error; err != nil {
		return nil, err
	}
	
	// Compile results
	return map[string]interface{}{
		"totalPayments":       totalPayments,
		"successfulPayments":  successfulPayments,
		"failedPayments":      failedPayments,
		"totalRevenue":        totalRevenue,
		"totalRefunds":        totalRefunds,
		"averageOrderValue":   averageOrderValue,
		"conversionRate":      conversionRate,
		"newSubscriptions":    newSubscriptions,
		"cancelledSubscriptions": cancelledSubscriptions,
		"activeSubscriptions": activeSubscriptions,
		"startDate":           startDate,
		"endDate":             endDate,
	}, nil
}

// CreateSubscriptionPlan creates a new subscription plan
func (r *GormPaymentRepository) CreateSubscriptionPlan(plan *models.SubscriptionPlan) error {
	return r.db.Create(plan).Error
}

// GetSubscriptionPlanByID retrieves a subscription plan by ID
func (r *GormPaymentRepository) GetSubscriptionPlanByID(id uint) (*models.SubscriptionPlan, error) {
	var plan models.SubscriptionPlan
	if err := r.db.First(&plan, id).Error; err != nil {
		return nil, err
	}
	return &plan, nil
}

// GetSubscriptionPlanByProviderID retrieves a subscription plan by provider ID
func (r *GormPaymentRepository) GetSubscriptionPlanByProviderID(providerID string) (*models.SubscriptionPlan, error) {
	var plan models.SubscriptionPlan
	if err := r.db.Where("provider_plan_id = ?", providerID).First(&plan).Error; err != nil {
		return nil, err
	}
	return &plan, nil
}

// UpdateSubscriptionPlan updates a subscription plan
func (r *GormPaymentRepository) UpdateSubscriptionPlan(plan *models.SubscriptionPlan) error {
	return r.db.Save(plan).Error
}

// DeleteSubscriptionPlan deletes a subscription plan
func (r *GormPaymentRepository) DeleteSubscriptionPlan(id uint) error {
	return r.db.Delete(&models.SubscriptionPlan{}, id).Error
}

// GetAllSubscriptionPlans retrieves all subscription plans
func (r *GormPaymentRepository) GetAllSubscriptionPlans(includeInactive bool) ([]models.SubscriptionPlan, error) {
	var plans []models.SubscriptionPlan
	query := r.db
	
	if !includeInactive {
		query = query.Where("is_active = ?", true)
	}
	
	if err := query.Order("amount ASC").Find(&plans).Error; err != nil {
		return nil, err
	}
	
	return plans, nil
}

// CreateSubscription creates a new subscription
func (r *GormPaymentRepository) CreateSubscription(subscription *models.Subscription) error {
	return r.db.Create(subscription).Error
}

// GetSubscriptionByID retrieves a subscription by ID
func (r *GormPaymentRepository) GetSubscriptionByID(id uint) (*models.Subscription, error) {
	var subscription models.Subscription
	if err := r.db.First(&subscription, id).Error; err != nil {
		return nil, err
	}
	
	// Load related plan
	var plan models.SubscriptionPlan
	if err := r.db.First(&plan, subscription.PlanID).Error; err != nil {
		return nil, err
	}
	
	subscription.Plan = plan
	
	return &subscription, nil
}

// GetSubscriptionByProviderID retrieves a subscription by provider ID
func (r *GormPaymentRepository) GetSubscriptionByProviderID(providerID string) (*models.Subscription, error) {
	var subscription models.Subscription
	if err := r.db.Where("provider_subscription_id = ?", providerID).First(&subscription).Error; err != nil {
		return nil, err
	}
	
	// Load related plan
	var plan models.SubscriptionPlan
	if err := r.db.First(&plan, subscription.PlanID).Error; err != nil {
		return nil, err
	}
	
	subscription.Plan = plan
	
	return &subscription, nil
}

// UpdateSubscription updates a subscription
func (r *GormPaymentRepository) UpdateSubscription(subscription *models.Subscription) error {
	return r.db.Save(subscription).Error
}

// GetUserSubscriptions retrieves subscriptions for a user
func (r *GormPaymentRepository) GetUserSubscriptions(userID uint, active bool) ([]models.Subscription, error) {
	var subscriptions []models.Subscription
	query := r.db.Where("user_id = ?", userID)
	
	if active {
		query = query.Where("status = ?", models.SubscriptionStatusActive)
	}
	
	if err := query.Order("created_at DESC").Find(&subscriptions).Error; err != nil {
		return nil, err
	}
	
	// Load related plans
	for i := range subscriptions {
		var plan models.SubscriptionPlan
		if err := r.db.First(&plan, subscriptions[i].PlanID).Error; err != nil {
			continue
		}
		subscriptions[i].Plan = plan
	}
	
	return subscriptions, nil
}

// GetActiveSubscriptionForUser retrieves the active subscription for a user
func (r *GormPaymentRepository) GetActiveSubscriptionForUser(userID uint) (*models.Subscription, error) {
	var subscription models.Subscription
	if err := r.db.Where("user_id = ? AND status = ? AND current_period_end >= ?", 
		userID, models.SubscriptionStatusActive, time.Now()).
		Order("current_period_end DESC").
		First(&subscription).Error; err != nil {
		return nil, err
	}
	
	// Load related plan
	var plan models.SubscriptionPlan
	if err := r.db.First(&plan, subscription.PlanID).Error; err != nil {
		return nil, err
	}
	
	subscription.Plan = plan
	
	return &subscription, nil
}

// GetSubscriptionsByPlanID retrieves subscriptions for a plan
func (r *GormPaymentRepository) GetSubscriptionsByPlanID(planID uint, active bool) ([]models.Subscription, error) {
	var subscriptions []models.Subscription
	query := r.db.Where("plan_id = ?", planID)
	
	if active {
		query = query.Where("status = ?", models.SubscriptionStatusActive)
	}
	
	if err := query.Order("created_at DESC").Find(&subscriptions).Error; err != nil {
		return nil, err
	}
	
	return subscriptions, nil
}

// GetExpiringSubscriptions retrieves subscriptions that are expiring soon
func (r *GormPaymentRepository) GetExpiringSubscriptions(days int) ([]models.Subscription, error) {
	var subscriptions []models.Subscription
	expiryDate := time.Now().AddDate(0, 0, days)
	
	if err := r.db.Where("status = ? AND current_period_end <= ? AND current_period_end >= ?", 
		models.SubscriptionStatusActive, expiryDate, time.Now()).
		Order("current_period_end ASC").
		Find(&subscriptions).Error; err != nil {
		return nil, err
	}
	
	// Load related plans
	for i := range subscriptions {
		var plan models.SubscriptionPlan
		if err := r.db.First(&plan, subscriptions[i].PlanID).Error; err != nil {
			continue
		}
		subscriptions[i].Plan = plan
	}
	
	return subscriptions, nil
}

// CreatePaymentMethod creates a new payment method
func (r *GormPaymentRepository) CreatePaymentMethod(method *models.PaymentMethod) error {
	// If this is set as default, unset any existing default payment methods
	if method.IsDefault {
		if err := r.db.Model(&models.PaymentMethod{}).
			Where("user_id = ? AND is_default = ?", method.UserID, true).
			Update("is_default", false).Error; err != nil {
			return err
		}
	}
	
	return r.db.Create(method).Error
}

// GetPaymentMethodByID retrieves a payment method by ID
func (r *GormPaymentRepository) GetPaymentMethodByID(id uint) (*models.PaymentMethod, error) {
	var method models.PaymentMethod
	if err := r.db.First(&method, id).Error; err != nil {
		return nil, err
	}
	return &method, nil
}

// UpdatePaymentMethod updates a payment method
func (r *GormPaymentRepository) UpdatePaymentMethod(method *models.PaymentMethod) error {
	// If this is set as default, unset any existing default payment methods
	if method.IsDefault {
		if err := r.db.Model(&models.PaymentMethod{}).
			Where("user_id = ? AND is_default = ? AND id != ?", method.UserID, true, method.ID).
			Update("is_default", false).Error; err != nil {
			return err
		}
	}
	
	return r.db.Save(method).Error
}

// DeletePaymentMethod deletes a payment method
func (r *GormPaymentRepository) DeletePaymentMethod(id uint) error {
	return r.db.Delete(&models.PaymentMethod{}, id).Error
}

// GetUserPaymentMethods retrieves payment methods for a user
func (r *GormPaymentRepository) GetUserPaymentMethods(userID uint) ([]models.PaymentMethod, error) {
	var methods []models.PaymentMethod
	if err := r.db.Where("user_id = ?", userID).Order("is_default DESC, created_at DESC").Find(&methods).Error; err != nil {
		return nil, err
	}
	return methods, nil
}

// GetDefaultPaymentMethod retrieves the default payment method for a user
func (r *GormPaymentRepository) GetDefaultPaymentMethod(userID uint) (*models.PaymentMethod, error) {
	var method models.PaymentMethod
	if err := r.db.Where("user_id = ? AND is_default = ?", userID, true).First(&method).Error; err != nil {
		return nil, err
	}
	return &method, nil
}

// SetDefaultPaymentMethod sets a payment method as default for a user
func (r *GormPaymentRepository) SetDefaultPaymentMethod(userID, paymentMethodID uint) error {
	// Begin transaction
	tx := r.db.Begin()
	
	// Unset any existing default payment methods
	if err := tx.Model(&models.PaymentMethod{}).
		Where("user_id = ? AND is_default = ?", userID, true).
		Update("is_default", false).Error; err != nil {
		tx.Rollback()
		return err
	}
	
	// Set the new default payment method
	if err := tx.Model(&models.PaymentMethod{}).
		Where("id = ? AND user_id = ?", paymentMethodID, userID).
		Update("is_default", true).Error; err != nil {
		tx.Rollback()
		return err
	}
	
	// Commit transaction
	return tx.Commit().Error
}

// CreateRefund creates a new refund
func (r *GormPaymentRepository) CreateRefund(refund *models.Refund) error {
	return r.db.Create(refund).Error
}

// GetRefundByID retrieves a refund by ID
func (r *GormPaymentRepository) GetRefundByID(id uint) (*models.Refund, error) {
	var refund models.Refund
	if err := r.db.First(&refund, id).Error; err != nil {
		return nil, err
	}
	return &refund, nil
}

// UpdateRefund updates a refund
func (r *GormPaymentRepository) UpdateRefund(refund *models.Refund) error {
	return r.db.Save(refund).Error
}

// GetRefundsByPaymentID retrieves refunds for a payment
func (r *GormPaymentRepository) GetRefundsByPaymentID(paymentID uint) ([]models.Refund, error) {
	var refunds []models.Refund
	if err := r.db.Where("payment_id = ?", paymentID).Order("created_at DESC").Find(&refunds).Error; err != nil {
		return nil, err
	}
	return refunds, nil
}

// GetUserRefunds retrieves refunds for a user
func (r *GormPaymentRepository) GetUserRefunds(userID uint, limit, offset int) ([]models.Refund, error) {
	var refunds []models.Refund
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&refunds).Error; err != nil {
		return nil, err
	}
	
	return refunds, nil
}

// CreateDispute creates a new dispute
func (r *GormPaymentRepository) CreateDispute(dispute *models.Dispute) error {
	return r.db.Create(dispute).Error
}

// GetDisputeByID retrieves a dispute by ID
func (r *GormPaymentRepository) GetDisputeByID(id uint) (*models.Dispute, error) {
	var dispute models.Dispute
	if err := r.db.First(&dispute, id).Error; err != nil {
		return nil, err
	}
	return &dispute, nil
}

// UpdateDispute updates a dispute
func (r *GormPaymentRepository) UpdateDispute(dispute *models.Dispute) error {
	return r.db.Save(dispute).Error
}

// GetDisputesByPaymentID retrieves disputes for a payment
func (r *GormPaymentRepository) GetDisputesByPaymentID(paymentID uint) ([]models.Dispute, error) {
	var disputes []models.Dispute
	if err := r.db.Where("payment_id = ?", paymentID).Order("created_at DESC").Find(&disputes).Error; err != nil {
		return nil, err
	}
	return disputes, nil
}

// GetUserDisputes retrieves disputes for a user
func (r *GormPaymentRepository) GetUserDisputes(userID uint, limit, offset int) ([]models.Dispute, error) {
	var disputes []models.Dispute
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&disputes).Error; err != nil {
		return nil, err
	}
	
	return disputes, nil
}

// GetOpenDisputes retrieves open disputes
func (r *GormPaymentRepository) GetOpenDisputes(limit, offset int) ([]models.Dispute, error) {
	var disputes []models.Dispute
	query := r.db.Where("resolved_at IS NULL").Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&disputes).Error; err != nil {
		return nil, err
	}
	
	return disputes, nil
}

// CreatePayout creates a new payout
func (r *GormPaymentRepository) CreatePayout(payout *models.Payout) error {
	return r.db.Create(payout).Error
}

// GetPayoutByID retrieves a payout by ID
func (r *GormPaymentRepository) GetPayoutByID(id uint) (*models.Payout, error) {
	var payout models.Payout
	if err := r.db.First(&payout, id).Error; err != nil {
		return nil, err
	}
	return &payout, nil
}

// UpdatePayout updates a payout
func (r *GormPaymentRepository) UpdatePayout(payout *models.Payout) error {
	return r.db.Save(payout).Error
}

// GetUserPayouts retrieves payouts for a user
func (r *GormPaymentRepository) GetUserPayouts(userID uint, limit, offset int) ([]models.Payout, error) {
	var payouts []models.Payout
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&payouts).Error; err != nil {
		return nil, err
	}
	
	return payouts, nil
}

// GetPendingPayouts retrieves pending payouts
func (r *GormPaymentRepository) GetPendingPayouts(limit, offset int) ([]models.Payout, error) {
	var payouts []models.Payout
	query := r.db.Where("status = ? AND approved_at IS NULL", "pending").Order("created_at ASC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&payouts).Error; err != nil {
		return nil, err
	}
	
	return payouts, nil
}

// CreatePromotion creates a new promotion
func (r *GormPaymentRepository) CreatePromotion(promotion *models.Promotion) error {
	return r.db.Create(promotion).Error
}

// GetPromotionByID retrieves a promotion by ID
func (r *GormPaymentRepository) GetPromotionByID(id uint) (*models.Promotion, error) {
	var promotion models.Promotion
	if err := r.db.First(&promotion, id).Error; err != nil {
		return nil, err
	}
	return &promotion, nil
}

// GetPromotionByCode retrieves a promotion by code
func (r *GormPaymentRepository) GetPromotionByCode(code string) (*models.Promotion, error) {
	var promotion models.Promotion
	if err := r.db.Where("code = ?", code).First(&promotion).Error; err != nil {
		return nil, err
	}
	return &promotion, nil
}

// UpdatePromotion updates a promotion
func (r *GormPaymentRepository) UpdatePromotion(promotion *models.Promotion) error {
	return r.db.Save(promotion).Error
}

// DeletePromotion deletes a promotion
func (r *GormPaymentRepository) DeletePromotion(id uint) error {
	return r.db.Delete(&models.Promotion{}, id).Error
}

// GetActivePromotions retrieves active promotions
func (r *GormPaymentRepository) GetActivePromotions() ([]models.Promotion, error) {
	var promotions []models.Promotion
	now := time.Now()
	if err := r.db.Where("is_active = ? AND start_date <= ? AND end_date >= ?", true, now, now).Find(&promotions).Error; err != nil {
		return nil, err
	}
	return promotions, nil
}

// RecordPromotionUsage records usage of a promotion
func (r *GormPaymentRepository) RecordPromotionUsage(usage *models.PromotionUsage) error {
	// Begin transaction
	tx := r.db.Begin()
	
	// Record usage
	if err := tx.Create(usage).Error; err != nil {
		tx.Rollback()
		return err
	}
	
	// Increment usage count on promotion
	if err := tx.Model(&models.Promotion{}).
		Where("id = ?", usage.PromotionID).
		UpdateColumn("usage_count", gorm.Expr("usage_count + 1")).Error; err != nil {
		tx.Rollback()
		return err
	}
	
	// Commit transaction
	return tx.Commit().Error
}

// GetPromotionUsage retrieves promotion usage
func (r *GormPaymentRepository) GetPromotionUsage(promotionID, userID uint) ([]models.PromotionUsage, error) {
	var usages []models.PromotionUsage
	query := r.db.Where("promotion_id = ?", promotionID)
	
	if userID > 0 {
		query = query.Where("user_id = ?", userID)
	}
	
	if err := query.Order("applied_at DESC").Find(&usages).Error; err != nil {
		return nil, err
	}
	
	return usages, nil
}

// CreateGift creates a new gift
func (r *GormPaymentRepository) CreateGift(gift *models.Gift) error {
	return r.db.Create(gift).Error
}

// GetGiftByID retrieves a gift by ID
func (r *GormPaymentRepository) GetGiftByID(id uint) (*models.Gift, error) {
	var gift models.Gift
	if err := r.db.First(&gift, id).Error; err != nil {
		return nil, err
	}
	return &gift, nil
}

// UpdateGift updates a gift
func (r *GormPaymentRepository) UpdateGift(gift *models.Gift) error {
	return r.db.Save(gift).Error
}

// GetUserSentGifts retrieves gifts sent by a user
func (r *GormPaymentRepository) GetUserSentGifts(userID uint, limit, offset int) ([]models.Gift, error) {
	var gifts []models.Gift
	query := r.db.Where("sender_user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&gifts).Error; err != nil {
		return nil, err
	}
	
	return gifts, nil
}

// GetUserReceivedGifts retrieves gifts received by a user
func (r *GormPaymentRepository) GetUserReceivedGifts(userID uint, limit, offset int) ([]models.Gift, error) {
	var gifts []models.Gift
	query := r.db.Where("recipient_user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&gifts).Error; err != nil {
		return nil, err
	}
	
	return gifts, nil
}

// GetContentGifts retrieves gifts for a content
func (r *GormPaymentRepository) GetContentGifts(contentID uint, contentType string, limit, offset int) ([]models.Gift, error) {
	var gifts []models.Gift
	query := r.db.Where("content_id = ? AND content_type = ?", contentID, contentType).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&gifts).Error; err != nil {
		return nil, err
	}
	
	return gifts, nil
}

// CreateInvoice creates a new invoice
func (r *GormPaymentRepository) CreateInvoice(invoice *models.Invoice) error {
	return r.db.Create(invoice).Error
}

// GetInvoiceByID retrieves an invoice by ID
func (r *GormPaymentRepository) GetInvoiceByID(id uint) (*models.Invoice, error) {
	var invoice models.Invoice
	if err := r.db.First(&invoice, id).Error; err != nil {
		return nil, err
	}
	return &invoice, nil
}

// UpdateInvoice updates an invoice
func (r *GormPaymentRepository) UpdateInvoice(invoice *models.Invoice) error {
	return r.db.Save(invoice).Error
}

// GetUserInvoices retrieves invoices for a user
func (r *GormPaymentRepository) GetUserInvoices(userID uint, limit, offset int) ([]models.Invoice, error) {
	var invoices []models.Invoice
	query := r.db.Where("user_id = ?", userID).Order("created_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&invoices).Error; err != nil {
		return nil, err
	}
	
	return invoices, nil
}

// GetUnpaidInvoices retrieves unpaid invoices
func (r *GormPaymentRepository) GetUnpaidInvoices(limit, offset int) ([]models.Invoice, error) {
	var invoices []models.Invoice
	query := r.db.Where("status = ? AND due_date <= ? AND paid_at IS NULL", "unpaid", time.Now()).Order("due_date ASC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	if err := query.Find(&invoices).Error; err != nil {
		return nil, err
	}
	
	return invoices, nil
}

// GetInvoicesBySubscriptionID retrieves invoices for a subscription
func (r *GormPaymentRepository) GetInvoicesBySubscriptionID(subscriptionID uint) ([]models.Invoice, error) {
	var invoices []models.Invoice
	if err := r.db.Where("subscription_id = ?", subscriptionID).Order("period_start DESC").Find(&invoices).Error; err != nil {
		return nil, err
	}
	return invoices, nil
}

// CreateVirtualAccount creates a new virtual account
func (r *GormPaymentRepository) CreateVirtualAccount(account *models.VirtualAccount) error {
	return r.db.Create(account).Error
}

// GetVirtualAccountByID retrieves a virtual account by ID
func (r *GormPaymentRepository) GetVirtualAccountByID(id uint) (*models.VirtualAccount, error) {
	var account models.VirtualAccount
	if err := r.db.First(&account, id).Error; err != nil {
		return nil, err
	}
	return &account, nil
}

// UpdateVirtualAccount updates a virtual account
func (r *GormPaymentRepository) UpdateVirtualAccount(account *models.VirtualAccount) error {
	return r.db.Save(account).Error
}

// GetUserVirtualAccounts retrieves virtual accounts for a user
func (r *GormPaymentRepository) GetUserVirtualAccounts(userID uint) ([]models.VirtualAccount, error) {
	var accounts []models.VirtualAccount
	if err := r.db.Where("user_id = ?", userID).Order("created_at DESC").Find(&accounts).Error; err != nil {
		return nil, err
	}
	return accounts, nil
}

// GetVirtualAccountByAccountNumber retrieves a virtual account by account number
func (r *GormPaymentRepository) GetVirtualAccountByAccountNumber(accountNumber string) (*models.VirtualAccount, error) {
	var account models.VirtualAccount
	if err := r.db.Where("account_number = ?", accountNumber).First(&account).Error; err != nil {
		return nil, err
	}
	return &account, nil
}

// CreateWebhookEvent creates a new webhook event
func (r *GormPaymentRepository) CreateWebhookEvent(event *models.WebhookEvent) error {
	return r.db.Create(event).Error
}

// GetWebhookEventByID retrieves a webhook event by ID
func (r *GormPaymentRepository) GetWebhookEventByID(id uint) (*models.WebhookEvent, error) {
	var event models.WebhookEvent
	if err := r.db.First(&event, id).Error; err != nil {
		return nil, err
	}
	return &event, nil
}

// UpdateWebhookEvent updates a webhook event
func (r *GormPaymentRepository) UpdateWebhookEvent(event *models.WebhookEvent) error {
	return r.db.Save(event).Error
}

// GetUnprocessedWebhookEvents retrieves unprocessed webhook events
func (r *GormPaymentRepository) GetUnprocessedWebhookEvents(limit int) ([]models.WebhookEvent, error) {
	var events []models.WebhookEvent
	query := r.db.Where("is_processed = ?", false).Order("created_at ASC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	if err := query.Find(&events).Error; err != nil {
		return nil, err
	}
	
	return events, nil
}

// SavePaymentAnalytics saves payment analytics
func (r *GormPaymentRepository) SavePaymentAnalytics(analytics *models.PaymentAnalytics) error {
	return r.db.Create(analytics).Error
}

// GetPaymentAnalyticsByDateRange retrieves payment analytics by date range
func (r *GormPaymentRepository) GetPaymentAnalyticsByDateRange(startDate, endDate time.Time) ([]models.PaymentAnalytics, error) {
	var analytics []models.PaymentAnalytics
	if err := r.db.Where("date BETWEEN ? AND ?", startDate, endDate).Order("date ASC").Find(&analytics).Error; err != nil {
		return nil, err
	}
	return analytics, nil
}

// GetPaymentAnalyticsByProvider retrieves payment analytics by provider
func (r *GormPaymentRepository) GetPaymentAnalyticsByProvider(provider models.PaymentProvider, startDate, endDate time.Time) ([]models.PaymentAnalytics, error) {
	var analytics []models.PaymentAnalytics
	if err := r.db.Where("provider = ? AND date BETWEEN ? AND ?", provider, startDate, endDate).Order("date ASC").Find(&analytics).Error; err != nil {
		return nil, err
	}
	return analytics, nil
}