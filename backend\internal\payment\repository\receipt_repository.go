package repository

import (
	"context"

	"github.com/greatnigeria/internal/payment/models"
)

// ReceiptRepository defines the interface for receipt data operations
type ReceiptRepository interface {
	// CreateReceipt creates a new receipt in the database
	CreateReceipt(ctx context.Context, receipt *models.Receipt) error
	
	// GetReceiptByID retrieves a receipt by its ID
	GetReceiptByID(ctx context.Context, id uint) (*models.Receipt, error)
	
	// GetReceiptByNumber retrieves a receipt by its receipt number
	GetReceiptByNumber(ctx context.Context, receiptNumber string) (*models.Receipt, error)
	
	// GetReceiptsByUserID retrieves all receipts for a user
	GetReceiptsByUserID(ctx context.Context, userID uint, limit, offset int) ([]*models.Receipt, int64, error)
	
	// GetReceiptByPaymentID retrieves a receipt for a payment
	GetReceiptByPaymentID(ctx context.Context, paymentID uint) (*models.Receipt, error)
	
	// GetReceiptBySubscriptionID retrieves a receipt for a subscription payment
	GetReceiptBySubscriptionID(ctx context.Context, subscriptionID uint) (*models.Receipt, error)
	
	// GetReceiptByRefundID retrieves a receipt for a refund
	GetReceiptByRefundID(ctx context.Context, refundID uint) (*models.Receipt, error)
	
	// UpdateReceiptStatus updates the status of a receipt
	UpdateReceiptStatus(ctx context.Context, id uint, status models.ReceiptStatus) error
	
	// UpdateReceiptPDFInfo updates the PDF path and hash
	UpdateReceiptPDFInfo(ctx context.Context, id uint, pdfPath, publicURL, contentHash string) error
	
	// UpdateReceiptEmailInfo updates the emailed information
	UpdateReceiptEmailInfo(ctx context.Context, id uint, emailedTo string) error
	
	// DeleteReceipt deletes a receipt by its ID
	DeleteReceipt(ctx context.Context, id uint) error
	
	// CreateReceiptTemplate creates a new receipt template
	CreateReceiptTemplate(ctx context.Context, template *models.ReceiptTemplate) error
	
	// GetReceiptTemplateByID retrieves a receipt template by its ID
	GetReceiptTemplateByID(ctx context.Context, id uint) (*models.ReceiptTemplate, error)
	
	// GetDefaultReceiptTemplate retrieves the default receipt template
	GetDefaultReceiptTemplate(ctx context.Context) (*models.ReceiptTemplate, error)
	
	// UpdateReceiptTemplate updates a receipt template
	UpdateReceiptTemplate(ctx context.Context, template *models.ReceiptTemplate) error
	
	// DeleteReceiptTemplate deletes a receipt template by its ID
	DeleteReceiptTemplate(ctx context.Context, id uint) error
	
	// CreateReceiptCustomization creates a new receipt customization
	CreateReceiptCustomization(ctx context.Context, customization *models.ReceiptCustomization) error
	
	// GetReceiptCustomizationByReceiptID retrieves a receipt customization by its receipt ID
	GetReceiptCustomizationByReceiptID(ctx context.Context, receiptID uint) (*models.ReceiptCustomization, error)
	
	// UpdateReceiptCustomization updates a receipt customization
	UpdateReceiptCustomization(ctx context.Context, customization *models.ReceiptCustomization) error
	
	// DeleteReceiptCustomization deletes a receipt customization by its ID
	DeleteReceiptCustomization(ctx context.Context, id uint) error
}