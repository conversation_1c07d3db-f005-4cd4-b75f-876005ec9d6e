package service

import (
        "errors"
        "fmt"
        "time"
        "crypto/rand"
        "encoding/hex"
        "encoding/json"
        "strings"
        
        "github.com/greatnigeria/internal/payment/models"
        "github.com/greatnigeria/internal/payment/repository"
        "github.com/greatnigeria/internal/payment/service/providers"
)

// PaymentService defines the interface for payment operations
type PaymentService interface {
        // Payment intent operations
        CreatePaymentIntent(userID uint, amount int, currency models.Currency, description, successURL, cancelURL string, metaData map[string]interface{}, provider models.PaymentProvider) (*models.PaymentIntent, string, error)
        GetPaymentIntent(id uint) (*models.PaymentIntent, error)
        ProcessPaymentResult(reference string, providerName models.PaymentProvider) (*models.Payment, error)
        
        // Payment operations
        GetPayment(id uint) (*models.Payment, error)
        GetUserPayments(userID uint, page, pageSize int) ([]models.Payment, error)
        CapturePayment(id uint, amount int) (*models.Payment, error)
        
        // Subscription plan operations
        CreateSubscriptionPlan(name, description string, amount int, interval models.PlanInterval, currency models.Currency, trialPeriodDays int, features map[string]interface{}, createdBy uint) (*models.SubscriptionPlan, error)
        GetSubscriptionPlanByID(id uint) (*models.SubscriptionPlan, error)
        UpdateSubscriptionPlan(id uint, name, description string, amount int, interval models.PlanInterval, currency models.Currency, trialPeriodDays int, features map[string]interface{}, isActive bool, updatedBy uint) (*models.SubscriptionPlan, error)
        GetSubscriptionPlans(includeInactive bool) ([]models.SubscriptionPlan, error)
        
        // Subscription operations
        CreateSubscription(userID, planID uint, paymentMethodID uint, metaData map[string]interface{}) (*models.Subscription, error)
        GetSubscription(id uint) (*models.Subscription, error)
        CancelSubscription(id uint, cancelAtPeriodEnd bool) (*models.Subscription, error)
        GetUserSubscriptions(userID uint, active bool) ([]models.Subscription, error)
        GetActiveSubscriptionForUser(userID uint) (*models.Subscription, error)
        
        // Payment method operations
        CreatePaymentMethod(userID uint, paymentType string, billingDetails map[string]string, cardDetails map[string]string, bankDetails map[string]string, isDefault bool) (*models.PaymentMethod, error)
        GetPaymentMethod(id uint) (*models.PaymentMethod, error)
        GetUserPaymentMethods(userID uint) ([]models.PaymentMethod, error)
        UpdatePaymentMethod(id uint, isDefault bool) (*models.PaymentMethod, error)
        DeletePaymentMethod(id uint) error
        
        // Refund operations
        CreateRefund(paymentID uint, amount int, reason string, processedBy uint) (*models.Refund, error)
        GetRefund(id uint) (*models.Refund, error)
        GetRefundsByPayment(paymentID uint) ([]models.Refund, error)
        
        // Dispute operations
        CreateDispute(paymentID uint, amount int, reason string, evidence map[string]interface{}, userID uint) (*models.Dispute, error)
        GetDispute(id uint) (*models.Dispute, error)
        UpdateDisputeStatus(id uint, status string, resolutionNotes string, resolvedBy uint) (*models.Dispute, error)
        
        // Promotion operations
        CreatePromotion(code, description string, discountType string, discountAmount int, minimumAmount int, maxUsageCount, maxUsagePerUser int, startDate, endDate time.Time, applicableProductIDs, applicablePlanIDs []uint, createdBy uint) (*models.Promotion, error)
        GetPromotion(id uint) (*models.Promotion, error)
        GetPromotionByCode(code string) (*models.Promotion, error)
        ApplyPromotion(code string, userID uint, amount int, currency models.Currency, productID, planID uint) (int, error)
        
        // Virtual account operations
        CreateVirtualAccount(userID uint, provider models.PaymentProvider, details map[string]string) (*models.VirtualAccount, error)
        GetVirtualAccount(id uint) (*models.VirtualAccount, error)
        GetUserVirtualAccounts(userID uint) ([]models.VirtualAccount, error)
        
        // Virtual gifting system
        CreateGift(senderUserID, recipientUserID, contentID uint, contentType string, giftType string, amount int, message string, isAnonymous bool) (*models.Gift, error)
        GetGift(id uint) (*models.Gift, error)
        GetUserSentGifts(userID uint, page, pageSize int) ([]models.Gift, error)
        GetUserReceivedGifts(userID uint, page, pageSize int) ([]models.Gift, error)
        ProcessGiftPayment(giftID uint, paymentMethodID uint) (*models.Payment, error)
        
        // Webhook processing
        ProcessWebhook(provider models.PaymentProvider, eventType string, eventID string, payload map[string]interface{}) error
        
        // Analytics
        GetPaymentAnalytics(startDate, endDate time.Time) (map[string]interface{}, error)
}

// PaymentServiceImpl implements PaymentService interface
type PaymentServiceImpl struct {
        repo         repository.PaymentRepository
        paystackProvider *providers.PaystackProvider
        flutterwaveProvider *providers.FlutterwaveProvider
        squadProvider *providers.SquadProvider
}

// NewPaymentService creates a new payment service
func NewPaymentService(
        repo repository.PaymentRepository,
        paystackProvider *providers.PaystackProvider,
        flutterwaveProvider *providers.FlutterwaveProvider,
        squadProvider *providers.SquadProvider,
) PaymentService {
        return &PaymentServiceImpl{
                repo:         repo,
                paystackProvider: paystackProvider,
                flutterwaveProvider: flutterwaveProvider,
                squadProvider: squadProvider,
        }
}

// generateReference generates a unique reference string
func generateReference() (string, error) {
        bytes := make([]byte, 16)
        if _, err := rand.Read(bytes); err != nil {
                return "", err
        }
        return hex.EncodeToString(bytes), nil
}

// CreatePaymentIntent creates a payment intent
func (s *PaymentServiceImpl) CreatePaymentIntent(
        userID uint,
        amount int,
        currency models.Currency,
        description, successURL, cancelURL string,
        metaData map[string]interface{},
        provider models.PaymentProvider,
) (*models.PaymentIntent, string, error) {
        // Generate a unique reference
        reference, err := generateReference()
        if err != nil {
                return nil, "", fmt.Errorf("error generating reference: %w", err)
        }
        
        // Convert metadata to JSON
        metaDataStr := ""
        if metaData != nil {
                metaDataJSON, err := json.Marshal(metaData)
                if err != nil {
                        return nil, "", fmt.Errorf("error marshaling metadata: %w", err)
                }
                metaDataStr = string(metaDataJSON)
        }
        
        // Create payment intent in database
        intent := &models.PaymentIntent{
                UserID:            userID,
                Amount:            amount,
                Currency:          currency,
                Description:       description,
                Status:            models.StatusPending,
                Provider:          provider,
                PaymentMethodType: "card", // Default to card for now
                ProviderReference: reference,
                SuccessURL:        successURL,
                CancelURL:         cancelURL,
                MetaData:          models.JSONB(metaData),
                ExpiresAt:         time.Now().Add(24 * time.Hour), // Expire after 24 hours
        }
        
        if err := s.repo.CreatePaymentIntent(intent); err != nil {
                return nil, "", fmt.Errorf("error creating payment intent: %w", err)
        }
        
        // Initialize payment with provider
        var paymentURL string
        switch provider {
        case models.ProviderPaystack:
                if s.paystackProvider == nil {
                        return nil, "", errors.New("paystack provider not configured")
                }
                
                // Mock email for testing
                email := fmt.Sprintf("<EMAIL>", userID)
                
                resp, err := s.paystackProvider.InitializeTransaction(
                        amount,
                        email,
                        reference,
                        successURL,
                        metaData,
                )
                
                if err != nil {
                        return nil, "", fmt.Errorf("error initializing paystack transaction: %w", err)
                }
                
                paymentURL = resp.AuthorizationURL
                
                // Update client secret
                intent.ClientSecret = resp.AccessCode
                if err := s.repo.UpdatePaymentIntent(intent); err != nil {
                        return nil, "", fmt.Errorf("error updating payment intent: %w", err)
                }
                
        case models.ProviderFlutterwave:
                if s.flutterwaveProvider == nil {
                        return nil, "", errors.New("flutterwave provider not configured")
                }
                
                // Mock email for testing
                email := fmt.Sprintf("<EMAIL>", userID)
                name := fmt.Sprintf("User %d", userID)
                
                url, err := s.flutterwaveProvider.InitializePayment(
                        amount,
                        string(currency),
                        reference,
                        email,
                        name,
                        successURL,
                        description,
                        metaData,
                )
                
                if err != nil {
                        return nil, "", fmt.Errorf("error initializing flutterwave payment: %w", err)
                }
                
                paymentURL = url
                
        case models.ProviderSquad:
                if s.squadProvider == nil {
                        return nil, "", errors.New("squad provider not configured")
                }
                
                // Mock email for testing
                email := fmt.Sprintf("<EMAIL>", userID)
                name := fmt.Sprintf("User %d", userID)
                
                resp, err := s.squadProvider.InitializeTransaction(
                        float64(amount),
                        email,
                        currency,
                        successURL,
                        successURL, // Use success URL as return URL
                        reference,
                        name,
                        metaData,
                )
                
                if err != nil {
                        return nil, "", fmt.Errorf("error initializing squad transaction: %w", err)
                }
                
                paymentURL = resp.CheckoutURL
                
        default:
                return nil, "", fmt.Errorf("unsupported payment provider: %s", provider)
        }
        
        return intent, paymentURL, nil
}

// GetPaymentIntent gets a payment intent by ID
func (s *PaymentServiceImpl) GetPaymentIntent(id uint) (*models.PaymentIntent, error) {
        return s.repo.GetPaymentIntentByID(id)
}

// ProcessPaymentResult processes a payment result from a provider callback
func (s *PaymentServiceImpl) ProcessPaymentResult(reference string, providerName models.PaymentProvider) (*models.Payment, error) {
        // Find payment intent
        intent, err := s.repo.GetPaymentIntentByProviderReference(reference)
        if err != nil {
                return nil, fmt.Errorf("error getting payment intent: %w", err)
        }
        
        if intent == nil {
                return nil, errors.New("payment intent not found")
        }
        
        // Check if this intent has already been processed
        if intent.Status != models.StatusPending && intent.Status != models.StatusProcessing {
                return nil, fmt.Errorf("payment intent has already been processed: %s", intent.Status)
        }
        
        // Update intent status to processing
        intent.Status = models.StatusProcessing
        if err := s.repo.UpdatePaymentIntent(intent); err != nil {
                return nil, fmt.Errorf("error updating payment intent status: %w", err)
        }
        
        // Verify payment with provider
        var paymentStatus models.PaymentStatus
        var paymentAmount int
        var paymentMethodType string
        var paymentMethodDetails models.JSONB
        var paymentProviderID string
        var receiptURL string
        
        switch providerName {
        case models.ProviderPaystack:
                if s.paystackProvider == nil {
                        return nil, errors.New("paystack provider not configured")
                }
                
                result, err := s.paystackProvider.VerifyTransaction(reference)
                if err != nil {
                        intent.Status = models.StatusFailed
                        if err := s.repo.UpdatePaymentIntent(intent); err != nil {
                                return nil, fmt.Errorf("error updating payment intent status: %w", err)
                        }
                        return nil, fmt.Errorf("error verifying paystack transaction: %w", err)
                }
                
                // Check if payment was successful
                if strings.ToLower(result.Status) == "success" {
                        paymentStatus = models.StatusSucceeded
                } else {
                        paymentStatus = models.StatusFailed
                }
                
                paymentAmount = result.Amount
                paymentMethodType = result.Channel
                
                // Extract payment method details
                var authData map[string]interface{}
                json.Unmarshal(result.Authorization, &authData)
                paymentMethodDetails = models.JSONB(authData)
                
                paymentProviderID = fmt.Sprintf("%d", result.ID)
                
        case models.ProviderFlutterwave:
                if s.flutterwaveProvider == nil {
                        return nil, errors.New("flutterwave provider not configured")
                }
                
                result, err := s.flutterwaveProvider.VerifyTransactionByReference(reference)
                if err != nil {
                        intent.Status = models.StatusFailed
                        if err := s.repo.UpdatePaymentIntent(intent); err != nil {
                                return nil, fmt.Errorf("error updating payment intent status: %w", err)
                        }
                        return nil, fmt.Errorf("error verifying flutterwave transaction: %w", err)
                }
                
                // Check if payment was successful
                if strings.ToLower(result.Status) == "successful" {
                        paymentStatus = models.StatusSucceeded
                } else {
                        paymentStatus = models.StatusFailed
                }
                
                paymentAmount = result.Amount
                paymentMethodType = result.PaymentType
                
                // Create payment method details
                paymentMethodDetails = models.JSONB(map[string]interface{}{
                        "customer_name":  result.Customer.Name,
                        "payment_type":   result.PaymentType,
                        "customer_email": result.Customer.Email,
                })
                
                paymentProviderID = fmt.Sprintf("%d", result.ID)
                
        case models.ProviderSquad:
                if s.squadProvider == nil {
                        return nil, errors.New("squad provider not configured")
                }
                
                result, err := s.squadProvider.VerifyTransaction(reference)
                if err != nil {
                        intent.Status = models.StatusFailed
                        if err := s.repo.UpdatePaymentIntent(intent); err != nil {
                                return nil, fmt.Errorf("error updating payment intent status: %w", err)
                        }
                        return nil, fmt.Errorf("error verifying squad transaction: %w", err)
                }
                
                // Check if payment was successful
                if strings.ToLower(result.Status) == "success" {
                        paymentStatus = models.StatusSucceeded
                } else {
                        paymentStatus = models.StatusFailed
                }
                
                paymentAmount = int(result.Amount)
                paymentMethodType = result.PaymentMethod
                
                // Create payment method details
                paymentMethodDetails = models.JSONB(map[string]interface{}{
                        "customer_name":  result.CustomerName,
                        "payment_method": result.PaymentMethod,
                        "channel":        result.Channel,
                        "customer_email": result.CustomerEmail,
                })
                
                paymentProviderID = result.PaymentReference
                
        default:
                return nil, fmt.Errorf("unsupported payment provider: %s", providerName)
        }
        
        // Update intent status
        intent.Status = paymentStatus
        if err := s.repo.UpdatePaymentIntent(intent); err != nil {
                return nil, fmt.Errorf("error updating payment intent status: %w", err)
        }
        
        // Create payment record
        var paidAt *time.Time
        if paymentStatus == models.StatusSucceeded {
                now := time.Now()
                paidAt = &now
        }
        
        payment := &models.Payment{
                UserID:                intent.UserID,
                PaymentIntentID:       intent.ID,
                Amount:                paymentAmount,
                AmountCaptured:        paymentAmount,
                Currency:              intent.Currency,
                Status:                paymentStatus,
                Provider:              providerName,
                ProviderReference:     reference,
                ProviderPaymentID:     paymentProviderID,
                ReceiptURL:            receiptURL,
                Description:           intent.Description,
                PaymentMethodType:     paymentMethodType,
                PaymentMethodDetails:  paymentMethodDetails,
                MetaData:              intent.MetaData,
                PaidAt:                paidAt,
        }
        
        if err := s.repo.CreatePayment(payment); err != nil {
                return nil, fmt.Errorf("error creating payment record: %w", err)
        }
        
        return payment, nil
}

// GetPayment gets a payment by ID
func (s *PaymentServiceImpl) GetPayment(id uint) (*models.Payment, error) {
        return s.repo.GetPaymentByID(id)
}

// GetUserPayments gets payments for a user
func (s *PaymentServiceImpl) GetUserPayments(userID uint, page, pageSize int) ([]models.Payment, error) {
        return s.repo.GetUserPayments(userID, page, pageSize)
}

// CapturePayment captures a payment
func (s *PaymentServiceImpl) CapturePayment(id uint, amount int) (*models.Payment, error) {
        payment, err := s.repo.GetPaymentByID(id)
        if err != nil {
                return nil, fmt.Errorf("error getting payment: %w", err)
        }
        
        if payment.Status != models.StatusSucceeded {
                return nil, errors.New("payment is not successful and cannot be captured")
        }
        
        if payment.AmountCaptured > 0 {
                return nil, errors.New("payment has already been captured")
        }
        
        if amount <= 0 || amount > payment.Amount {
                return nil, errors.New("invalid capture amount")
        }
        
        // Update payment captured amount
        payment.AmountCaptured = amount
        if err := s.repo.UpdatePayment(payment); err != nil {
                return nil, fmt.Errorf("error updating payment: %w", err)
        }
        
        return payment, nil
}

// CreateSubscriptionPlan creates a subscription plan
func (s *PaymentServiceImpl) CreateSubscriptionPlan(
        name, description string,
        amount int,
        interval models.PlanInterval,
        currency models.Currency,
        trialPeriodDays int,
        features map[string]interface{},
        createdBy uint,
) (*models.SubscriptionPlan, error) {
        // Convert features to JSON
        featuresJSON, err := json.Marshal(features)
        if err != nil {
                return nil, fmt.Errorf("error marshaling features: %w", err)
        }
        
        // Create plan in database
        plan := &models.SubscriptionPlan{
                Name:            name,
                Description:     description,
                Amount:          amount,
                Currency:        currency,
                Interval:        interval,
                IntervalCount:   1, // Default interval count
                TrialPeriodDays: trialPeriodDays,
                Features:        models.JSONB(features),
                IsActive:        true,
                CreatedBy:       createdBy,
                UpdatedBy:       createdBy,
        }
        
        // Create provider plans
        if s.paystackProvider != nil {
                // Map our interval to Paystack interval format
                paystackInterval := ""
                switch interval {
                case models.IntervalDaily:
                        paystackInterval = "daily"
                case models.IntervalWeekly:
                        paystackInterval = "weekly"
                case models.IntervalMonthly:
                        paystackInterval = "monthly"
                case models.IntervalQuarterly:
                        paystackInterval = "quarterly"
                case models.IntervalBiannually:
                        paystackInterval = "biannually"
                case models.IntervalAnnually:
                        paystackInterval = "annually"
                default:
                        paystackInterval = "monthly"
                }
                
                paystackPlan, err := s.paystackProvider.CreatePlan(
                        name,
                        description,
                        amount,
                        paystackInterval,
                        currency,
                )
                
                if err != nil {
                        return nil, fmt.Errorf("error creating paystack plan: %w", err)
                }
                
                plan.ProviderPlanID = paystackPlan.PlanCode
                plan.ProviderPaystackID = paystackPlan.PlanCode
        }
        
        if s.flutterwaveProvider != nil {
                // Map our interval to Flutterwave interval format
                flutterwaveInterval := ""
                switch interval {
                case models.IntervalDaily:
                        flutterwaveInterval = "daily"
                case models.IntervalWeekly:
                        flutterwaveInterval = "weekly"
                case models.IntervalMonthly:
                        flutterwaveInterval = "monthly"
                case models.IntervalQuarterly:
                        flutterwaveInterval = "quarterly"
                case models.IntervalAnnually:
                        flutterwaveInterval = "yearly"
                default:
                        flutterwaveInterval = "monthly"
                }
                
                flutterwavePlan, err := s.flutterwaveProvider.CreatePlan(
                        name,
                        description,
                        amount,
                        flutterwaveInterval,
                        currency,
                )
                
                if err != nil {
                        return nil, fmt.Errorf("error creating flutterwave plan: %w", err)
                }
                
                plan.ProviderFlutterwaveID = flutterwavePlan.PlanCode
        }
        
        if s.squadProvider != nil {
                // Map our interval to Squad interval format
                squadInterval := ""
                switch interval {
                case models.IntervalDaily:
                        squadInterval = "daily"
                case models.IntervalWeekly:
                        squadInterval = "weekly"
                case models.IntervalMonthly:
                        squadInterval = "monthly"
                case models.IntervalQuarterly:
                        squadInterval = "quarterly"
                case models.IntervalAnnually:
                        squadInterval = "yearly"
                default:
                        squadInterval = "monthly"
                }
                
                squadPlan, err := s.squadProvider.CreatePlan(
                        name,
                        squadInterval,
                        amount,
                        currency,
                )
                
                if err != nil {
                        return nil, fmt.Errorf("error creating squad plan: %w", err)
                }
                
                plan.ProviderSquadID = squadPlan.ID
        }
        
        if err := s.repo.CreateSubscriptionPlan(plan); err != nil {
                return nil, fmt.Errorf("error creating subscription plan: %w", err)
        }
        
        return plan, nil
}

// GetSubscriptionPlanByID gets a subscription plan by ID
func (s *PaymentServiceImpl) GetSubscriptionPlanByID(id uint) (*models.SubscriptionPlan, error) {
        return s.repo.GetSubscriptionPlanByID(id)
}

// UpdateSubscriptionPlan updates a subscription plan
func (s *PaymentServiceImpl) UpdateSubscriptionPlan(
        id uint,
        name, description string,
        amount int,
        interval models.PlanInterval,
        currency models.Currency,
        trialPeriodDays int,
        features map[string]interface{},
        isActive bool,
        updatedBy uint,
) (*models.SubscriptionPlan, error) {
        // Get existing plan
        plan, err := s.repo.GetSubscriptionPlanByID(id)
        if err != nil {
                return nil, fmt.Errorf("error getting subscription plan: %w", err)
        }
        
        // Update plan values
        plan.Name = name
        plan.Description = description
        plan.Amount = amount
        plan.Interval = interval
        plan.Currency = currency
        plan.TrialPeriodDays = trialPeriodDays
        plan.Features = models.JSONB(features)
        plan.IsActive = isActive
        plan.UpdatedBy = updatedBy
        
        // Update provider plans
        if s.paystackProvider != nil && plan.ProviderPaystackID != "" {
                _, err := s.paystackProvider.UpdatePlan(
                        plan.ProviderPaystackID,
                        name,
                        description,
                        amount,
                )
                
                if err != nil {
                        return nil, fmt.Errorf("error updating paystack plan: %w", err)
                }
        }
        
        if err := s.repo.UpdateSubscriptionPlan(plan); err != nil {
                return nil, fmt.Errorf("error updating subscription plan: %w", err)
        }
        
        return plan, nil
}

// GetSubscriptionPlans gets all subscription plans
func (s *PaymentServiceImpl) GetSubscriptionPlans(includeInactive bool) ([]models.SubscriptionPlan, error) {
        return s.repo.GetAllSubscriptionPlans(includeInactive)
}

// CreateSubscription creates a subscription
func (s *PaymentServiceImpl) CreateSubscription(
        userID, planID uint,
        paymentMethodID uint,
        metaData map[string]interface{},
) (*models.Subscription, error) {
        // Get plan
        plan, err := s.repo.GetSubscriptionPlanByID(planID)
        if err != nil {
                return nil, fmt.Errorf("error getting subscription plan: %w", err)
        }
        
        if !plan.IsActive {
                return nil, errors.New("subscription plan is not active")
        }
        
        // Get payment method
        paymentMethod, err := s.repo.GetPaymentMethodByID(paymentMethodID)
        if err != nil {
                return nil, fmt.Errorf("error getting payment method: %w", err)
        }
        
        if paymentMethod.UserID != userID {
                return nil, errors.New("payment method does not belong to user")
        }
        
        // Generate a reference
        reference, err := generateReference()
        if err != nil {
                return nil, fmt.Errorf("error generating reference: %w", err)
        }
        
        // Create payment intent
        intent, _, err := s.CreatePaymentIntent(
                userID,
                plan.Amount,
                plan.Currency,
                fmt.Sprintf("Subscription to %s", plan.Name),
                "", // No success URL for subscriptions
                "", // No cancel URL for subscriptions
                metaData,
                paymentMethod.Provider,
        )
        
        if err != nil {
                return nil, fmt.Errorf("error creating payment intent: %w", err)
        }
        
        // Process payment
        payment, err := s.ProcessPaymentResult(intent.ProviderReference, paymentMethod.Provider)
        if err != nil {
                return nil, fmt.Errorf("error processing payment: %w", err)
        }
        
        if payment.Status != models.StatusSucceeded {
                return nil, errors.New("payment failed")
        }
        
        // Calculate current period dates
        now := time.Now()
        var currentPeriodEnd time.Time
        
        switch plan.Interval {
        case models.IntervalDaily:
                currentPeriodEnd = now.AddDate(0, 0, plan.IntervalCount)
        case models.IntervalWeekly:
                currentPeriodEnd = now.AddDate(0, 0, 7*plan.IntervalCount)
        case models.IntervalMonthly:
                currentPeriodEnd = now.AddDate(0, plan.IntervalCount, 0)
        case models.IntervalQuarterly:
                currentPeriodEnd = now.AddDate(0, 3*plan.IntervalCount, 0)
        case models.IntervalBiannually:
                currentPeriodEnd = now.AddDate(0, 6*plan.IntervalCount, 0)
        case models.IntervalAnnually:
                currentPeriodEnd = now.AddDate(plan.IntervalCount, 0, 0)
        default:
                currentPeriodEnd = now.AddDate(0, 1, 0) // Default to monthly
        }
        
        // Create subscription
        subscription := &models.Subscription{
                UserID:             userID,
                PlanID:             planID,
                Status:             models.SubscriptionStatusActive,
                Provider:           paymentMethod.Provider,
                CurrentPeriodStart: now,
                CurrentPeriodEnd:   currentPeriodEnd,
                CancelAtPeriodEnd:  false,
                DefaultPaymentMethodID: paymentMethod.ProviderPaymentID,
                LatestPaymentID:    &payment.ID,
                MetaData:           models.JSONB(metaData),
        }
        
        // Add trial period if configured
        if plan.TrialPeriodDays > 0 {
                trialStart := now
                trialEnd := now.AddDate(0, 0, plan.TrialPeriodDays)
                subscription.TrialStart = &trialStart
                subscription.TrialEnd = &trialEnd
                subscription.Status = models.SubscriptionStatusTrialing
        }
        
        // Create provider subscription
        switch paymentMethod.Provider {
        case models.ProviderPaystack:
                if s.paystackProvider == nil {
                        return nil, errors.New("paystack provider not configured")
                }
                
                // Mock email for testing
                email := fmt.Sprintf("<EMAIL>", userID)
                
                resp, err := s.paystackProvider.CreateSubscription(
                        email,
                        plan.ProviderPaystackID,
                        metaData,
                )
                
                if err != nil {
                        return nil, fmt.Errorf("error creating paystack subscription: %w", err)
                }
                
                subscription.ProviderSubscriptionID = resp.SubscriptionCode
        }
        
        if err := s.repo.CreateSubscription(subscription); err != nil {
                return nil, fmt.Errorf("error creating subscription: %w", err)
        }
        
        // Update payment with subscription ID
        payment.SubscriptionID = &subscription.ID
        if err := s.repo.UpdatePayment(payment); err != nil {
                return nil, fmt.Errorf("error updating payment with subscription ID: %w", err)
        }
        
        return subscription, nil
}

// GetSubscription gets a subscription by ID
func (s *PaymentServiceImpl) GetSubscription(id uint) (*models.Subscription, error) {
        return s.repo.GetSubscriptionByID(id)
}

// CancelSubscription cancels a subscription
func (s *PaymentServiceImpl) CancelSubscription(id uint, cancelAtPeriodEnd bool) (*models.Subscription, error) {
        // Get subscription
        subscription, err := s.repo.GetSubscriptionByID(id)
        if err != nil {
                return nil, fmt.Errorf("error getting subscription: %w", err)
        }
        
        if subscription.Status != models.SubscriptionStatusActive && subscription.Status != models.SubscriptionStatusTrialing {
                return nil, errors.New("subscription is not active or trialing")
        }
        
        // Update subscription status
        if cancelAtPeriodEnd {
                subscription.CancelAtPeriodEnd = true
        } else {
                subscription.Status = models.SubscriptionStatusCancelled
                now := time.Now()
                subscription.CanceledAt = &now
        }
        
        // Cancel provider subscription
        switch subscription.Provider {
        case models.ProviderPaystack:
                if s.paystackProvider == nil {
                        return nil, errors.New("paystack provider not configured")
                }
                
                // Paystack requires the email token for cancellation
                // In a real implementation, you would store this or retrieve it from the provider
                // For this example, we'll assume it's available through a provider call
                
                // Mock email token for testing
                emailToken := "dummy_email_token"
                
                _, err := s.paystackProvider.DisableSubscription(
                        subscription.ProviderSubscriptionID,
                        emailToken,
                )
                
                if err != nil {
                        return nil, fmt.Errorf("error cancelling paystack subscription: %w", err)
                }
        }
        
        if err := s.repo.UpdateSubscription(subscription); err != nil {
                return nil, fmt.Errorf("error updating subscription: %w", err)
        }
        
        return subscription, nil
}

// GetUserSubscriptions gets subscriptions for a user
func (s *PaymentServiceImpl) GetUserSubscriptions(userID uint, active bool) ([]models.Subscription, error) {
        return s.repo.GetUserSubscriptions(userID, active)
}

// GetActiveSubscriptionForUser gets the active subscription for a user
func (s *PaymentServiceImpl) GetActiveSubscriptionForUser(userID uint) (*models.Subscription, error) {
        return s.repo.GetActiveSubscriptionForUser(userID)
}

// CreatePaymentMethod creates a payment method
func (s *PaymentServiceImpl) CreatePaymentMethod(
        userID uint,
        paymentType string,
        billingDetails map[string]string,
        cardDetails map[string]string,
        bankDetails map[string]string,
        isDefault bool,
) (*models.PaymentMethod, error) {
        // Determine provider based on availability
        var provider models.PaymentProvider
        if s.paystackProvider != nil {
                provider = models.ProviderPaystack
        } else if s.flutterwaveProvider != nil {
                provider = models.ProviderFlutterwave
        } else if s.squadProvider != nil {
                provider = models.ProviderSquad
        } else {
                return nil, errors.New("no payment provider configured")
        }
        
        // Create payment method
        providerPaymentID := fmt.Sprintf("pm_%s", generateRandomString(16))
        
        method := &models.PaymentMethod{
                UserID:               userID,
                Provider:             provider,
                ProviderPaymentID:    providerPaymentID,
                Type:                 paymentType,
                BillingName:          billingDetails["name"],
                BillingEmail:         billingDetails["email"],
                BillingPhone:         billingDetails["phone"],
                BillingAddressLine1:  billingDetails["address_line1"],
                BillingAddressLine2:  billingDetails["address_line2"],
                BillingCity:          billingDetails["city"],
                BillingState:         billingDetails["state"],
                BillingPostalCode:    billingDetails["postal_code"],
                BillingCountry:       billingDetails["country"],
                IsDefault:            isDefault,
        }
        
        // Add card details if provided
        if paymentType == "card" && cardDetails != nil {
                method.CardLast4 = cardDetails["last4"]
                method.CardExpiryMonth = cardDetails["expiry_month"]
                method.CardExpiryYear = cardDetails["expiry_year"]
                method.CardBrand = cardDetails["brand"]
        }
        
        // Add bank details if provided
        if paymentType == "bank_account" && bankDetails != nil {
                method.BankAccountLast4 = bankDetails["last4"]
                method.BankAccountHolderName = bankDetails["account_holder_name"]
                method.BankAccountHolderType = bankDetails["account_holder_type"]
                method.BankName = bankDetails["bank_name"]
                method.BankCode = bankDetails["bank_code"]
        }
        
        if err := s.repo.CreatePaymentMethod(method); err != nil {
                return nil, fmt.Errorf("error creating payment method: %w", err)
        }
        
        return method, nil
}

// generateRandomString generates a random alphanumeric string
func generateRandomString(length int) string {
        bytes := make([]byte, length/2)
        if _, err := rand.Read(bytes); err != nil {
                return ""
        }
        return hex.EncodeToString(bytes)
}

// GetPaymentMethod gets a payment method by ID
func (s *PaymentServiceImpl) GetPaymentMethod(id uint) (*models.PaymentMethod, error) {
        return s.repo.GetPaymentMethodByID(id)
}

// GetUserPaymentMethods gets payment methods for a user
func (s *PaymentServiceImpl) GetUserPaymentMethods(userID uint) ([]models.PaymentMethod, error) {
        return s.repo.GetUserPaymentMethods(userID)
}

// UpdatePaymentMethod updates a payment method
func (s *PaymentServiceImpl) UpdatePaymentMethod(id uint, isDefault bool) (*models.PaymentMethod, error) {
        // Get payment method
        method, err := s.repo.GetPaymentMethodByID(id)
        if err != nil {
                return nil, fmt.Errorf("error getting payment method: %w", err)
        }
        
        // Update payment method
        method.IsDefault = isDefault
        
        if err := s.repo.UpdatePaymentMethod(method); err != nil {
                return nil, fmt.Errorf("error updating payment method: %w", err)
        }
        
        return method, nil
}

// DeletePaymentMethod deletes a payment method
func (s *PaymentServiceImpl) DeletePaymentMethod(id uint) error {
        return s.repo.DeletePaymentMethod(id)
}

// CreateRefund creates a refund
func (s *PaymentServiceImpl) CreateRefund(paymentID uint, amount int, reason string, processedBy uint) (*models.Refund, error) {
        // Get payment
        payment, err := s.repo.GetPaymentByID(paymentID)
        if err != nil {
                return nil, fmt.Errorf("error getting payment: %w", err)
        }
        
        if payment.Status != models.StatusSucceeded {
                return nil, errors.New("payment is not successful and cannot be refunded")
        }
        
        if amount <= 0 || amount > payment.Amount {
                return nil, errors.New("invalid refund amount")
        }
        
        // Check if refunds exceed payment amount
        refunds, err := s.repo.GetRefundsByPaymentID(paymentID)
        if err != nil {
                return nil, fmt.Errorf("error getting existing refunds: %w", err)
        }
        
        totalRefunded := 0
        for _, refund := range refunds {
                totalRefunded += refund.Amount
        }
        
        if totalRefunded+amount > payment.Amount {
                return nil, errors.New("refund amount exceeds available amount")
        }
        
        // Create refund in provider
        var refundProviderID string
        now := time.Now()
        
        switch payment.Provider {
        case models.ProviderPaystack:
                if s.paystackProvider == nil {
                        return nil, errors.New("paystack provider not configured")
                }
                
                result, err := s.paystackProvider.InitiateRefund(
                        payment.ProviderReference,
                        amount,
                        reason,
                )
                
                if err != nil {
                        return nil, fmt.Errorf("error initiating paystack refund: %w", err)
                }
                
                refundProviderID = result.Reference
                
        case models.ProviderFlutterwave:
                if s.flutterwaveProvider == nil {
                        return nil, errors.New("flutterwave provider not configured")
                }
                
                result, err := s.flutterwaveProvider.InitiateRefund(
                        payment.ProviderPaymentID,
                        amount,
                        reason,
                )
                
                if err != nil {
                        return nil, fmt.Errorf("error initiating flutterwave refund: %w", err)
                }
                
                refundProviderID = fmt.Sprintf("%d", result.ID)
                
        case models.ProviderSquad:
                if s.squadProvider == nil {
                        return nil, errors.New("squad provider not configured")
                }
                
                result, err := s.squadProvider.InitiateRefund(
                        payment.ProviderReference,
                        float64(amount),
                        reason,
                )
                
                if err != nil {
                        return nil, fmt.Errorf("error initiating squad refund: %w", err)
                }
                
                refundProviderID = result.RefundReference
        }
        
        // Create refund in database
        refund := &models.Refund{
                UserID:             payment.UserID,
                PaymentID:          paymentID,
                Amount:             amount,
                Currency:           payment.Currency,
                Status:             "succeeded",
                Reason:             reason,
                Provider:           payment.Provider,
                ProviderRefundID:   refundProviderID,
                ProcessedBy:        processedBy,
        }
        
        if err := s.repo.CreateRefund(refund); err != nil {
                return nil, fmt.Errorf("error creating refund: %w", err)
        }
        
        // Update payment amount refunded
        payment.AmountRefunded += amount
        
        if payment.AmountRefunded == payment.Amount {
                payment.Status = models.StatusRefunded
                payment.RefundedAt = &now
        }
        
        if err := s.repo.UpdatePayment(payment); err != nil {
                return nil, fmt.Errorf("error updating payment: %w", err)
        }
        
        return refund, nil
}

// GetRefund gets a refund by ID
func (s *PaymentServiceImpl) GetRefund(id uint) (*models.Refund, error) {
        return s.repo.GetRefundByID(id)
}

// GetRefundsByPayment gets refunds for a payment
func (s *PaymentServiceImpl) GetRefundsByPayment(paymentID uint) ([]models.Refund, error) {
        return s.repo.GetRefundsByPaymentID(paymentID)
}

// CreateDispute creates a dispute
func (s *PaymentServiceImpl) CreateDispute(
        paymentID uint,
        amount int,
        reason string,
        evidence map[string]interface{},
        userID uint,
) (*models.Dispute, error) {
        // Get payment
        payment, err := s.repo.GetPaymentByID(paymentID)
        if err != nil {
                return nil, fmt.Errorf("error getting payment: %w", err)
        }
        
        if payment.UserID != userID {
                return nil, errors.New("payment does not belong to user")
        }
        
        // Convert evidence to JSON
        evidenceJSON, err := json.Marshal(evidence)
        if err != nil {
                return nil, fmt.Errorf("error marshaling evidence: %w", err)
        }
        
        // Create dispute
        dispute := &models.Dispute{
                UserID:          userID,
                PaymentID:       paymentID,
                Amount:          amount,
                Currency:        payment.Currency,
                Status:          "open",
                Reason:          reason,
                Provider:        payment.Provider,
                ProviderDisputeID: fmt.Sprintf("dp_%s", generateRandomString(16)),
                Evidence:        models.JSONB(evidence),
                DueBy:           time.Now().AddDate(0, 0, 14), // Due in 14 days
        }
        
        if err := s.repo.CreateDispute(dispute); err != nil {
                return nil, fmt.Errorf("error creating dispute: %w", err)
        }
        
        return dispute, nil
}

// GetDispute gets a dispute by ID
func (s *PaymentServiceImpl) GetDispute(id uint) (*models.Dispute, error) {
        return s.repo.GetDisputeByID(id)
}

// UpdateDisputeStatus updates a dispute status
func (s *PaymentServiceImpl) UpdateDisputeStatus(
        id uint,
        status string,
        resolutionNotes string,
        resolvedBy uint,
) (*models.Dispute, error) {
        // Get dispute
        dispute, err := s.repo.GetDisputeByID(id)
        if err != nil {
                return nil, fmt.Errorf("error getting dispute: %w", err)
        }
        
        // Update dispute
        dispute.Status = status
        dispute.ResolutionNotes = resolutionNotes
        
        if status == "resolved" || status == "lost" || status == "won" {
                now := time.Now()
                dispute.ResolvedAt = &now
                dispute.ResolvedBy = &resolvedBy
        }
        
        if err := s.repo.UpdateDispute(dispute); err != nil {
                return nil, fmt.Errorf("error updating dispute: %w", err)
        }
        
        return dispute, nil
}

// CreatePromotion creates a promotion
func (s *PaymentServiceImpl) CreatePromotion(
        code, description string,
        discountType string,
        discountAmount int,
        minimumAmount int,
        maxUsageCount, maxUsagePerUser int,
        startDate, endDate time.Time,
        applicableProductIDs, applicablePlanIDs []uint,
        createdBy uint,
) (*models.Promotion, error) {
        // Validate discount type
        if discountType != "percentage" && discountType != "fixed_amount" {
                return nil, errors.New("invalid discount type")
        }
        
        // Check if code already exists
        existingPromo, _ := s.repo.GetPromotionByCode(code)
        if existingPromo != nil {
                return nil, errors.New("promotion code already exists")
        }
        
        // Convert applicable IDs to comma-separated strings
        var productsStr, plansStr string
        
        if len(applicableProductIDs) > 0 {
                productStrs := make([]string, len(applicableProductIDs))
                for i, id := range applicableProductIDs {
                        productStrs[i] = fmt.Sprintf("%d", id)
                }
                productsStr = strings.Join(productStrs, ",")
        }
        
        if len(applicablePlanIDs) > 0 {
                planStrs := make([]string, len(applicablePlanIDs))
                for i, id := range applicablePlanIDs {
                        planStrs[i] = fmt.Sprintf("%d", id)
                }
                plansStr = strings.Join(planStrs, ",")
        }
        
        // Create promotion
        promotion := &models.Promotion{
                Code:                 code,
                Description:          description,
                DiscountType:         discountType,
                DiscountAmount:       discountAmount,
                MinimumAmount:        minimumAmount,
                MaxUsageCount:        maxUsageCount,
                MaxUsagePerUser:      maxUsagePerUser,
                StartDate:            startDate,
                EndDate:              endDate,
                IsActive:             true,
                ApplicableProductIDs: productsStr,
                ApplicablePlanIDs:    plansStr,
                CreatedBy:            createdBy,
                UpdatedBy:            createdBy,
        }
        
        if err := s.repo.CreatePromotion(promotion); err != nil {
                return nil, fmt.Errorf("error creating promotion: %w", err)
        }
        
        return promotion, nil
}

// GetPromotion gets a promotion by ID
func (s *PaymentServiceImpl) GetPromotion(id uint) (*models.Promotion, error) {
        return s.repo.GetPromotionByID(id)
}

// GetPromotionByCode gets a promotion by code
func (s *PaymentServiceImpl) GetPromotionByCode(code string) (*models.Promotion, error) {
        return s.repo.GetPromotionByCode(code)
}

// ApplyPromotion applies a promotion code
func (s *PaymentServiceImpl) ApplyPromotion(
        code string,
        userID uint,
        amount int,
        currency models.Currency,
        productID, planID uint,
) (int, error) {
        // Get promotion
        promotion, err := s.repo.GetPromotionByCode(code)
        if err != nil {
                return 0, fmt.Errorf("error getting promotion: %w", err)
        }
        
        if promotion == nil {
                return 0, errors.New("promotion code not found")
        }
        
        // Check if promotion is active
        now := time.Now()
        if !promotion.IsActive || now.Before(promotion.StartDate) || now.After(promotion.EndDate) {
                return 0, errors.New("promotion code is not active")
        }
        
        // Check if minimum amount is met
        if amount < promotion.MinimumAmount {
                return 0, fmt.Errorf("minimum amount of %d not met", promotion.MinimumAmount)
        }
        
        // Check usage count
        if promotion.MaxUsageCount > 0 && promotion.UsageCount >= promotion.MaxUsageCount {
                return 0, errors.New("promotion code has reached maximum usage")
        }
        
        // Check user usage count
        if promotion.MaxUsagePerUser > 0 {
                usages, err := s.repo.GetPromotionUsage(promotion.ID, userID)
                if err != nil {
                        return 0, fmt.Errorf("error checking user promotion usage: %w", err)
                }
                
                if len(usages) >= promotion.MaxUsagePerUser {
                        return 0, errors.New("you have reached maximum usage for this promotion")
                }
        }
        
        // Check product/plan applicability
        if productID > 0 && promotion.ApplicableProductIDs != "" {
                products := strings.Split(promotion.ApplicableProductIDs, ",")
                found := false
                for _, p := range products {
                        pid, _ := strconv.ParseUint(p, 10, 64)
                        if uint(pid) == productID {
                                found = true
                                break
                        }
                }
                
                if !found {
                        return 0, errors.New("promotion code not applicable to this product")
                }
        }
        
        if planID > 0 && promotion.ApplicablePlanIDs != "" {
                plans := strings.Split(promotion.ApplicablePlanIDs, ",")
                found := false
                for _, p := range plans {
                        pid, _ := strconv.ParseUint(p, 10, 64)
                        if uint(pid) == planID {
                                found = true
                                break
                        }
                }
                
                if !found {
                        return 0, errors.New("promotion code not applicable to this plan")
                }
        }
        
        // Calculate discount
        var discountedAmount int
        if promotion.DiscountType == "percentage" {
                // Ensure percentage is between 0 and 100
                percentage := promotion.DiscountAmount
                if percentage < 0 {
                        percentage = 0
                } else if percentage > 100 {
                        percentage = 100
                }
                
                discount := amount * percentage / 100
                discountedAmount = amount - discount
        } else {
                // Fixed amount discount
                discount := promotion.DiscountAmount
                if discount > amount {
                        discount = amount
                }
                
                discountedAmount = amount - discount
        }
        
        // Record usage
        usage := &models.PromotionUsage{
                UserID:       userID,
                PromotionID:  promotion.ID,
                DiscountType: promotion.DiscountType,
                DiscountAmount: promotion.DiscountAmount,
                Currency:     currency,
                AppliedAt:    now,
        }
        
        if err := s.repo.RecordPromotionUsage(usage); err != nil {
                return 0, fmt.Errorf("error recording promotion usage: %w", err)
        }
        
        return discountedAmount, nil
}

// CreateVirtualAccount creates a virtual account
func (s *PaymentServiceImpl) CreateVirtualAccount(
        userID uint,
        provider models.PaymentProvider,
        details map[string]string,
) (*models.VirtualAccount, error) {
        // Check if provider is supported
        if provider != models.ProviderSquad {
                return nil, errors.New("virtual accounts are only supported with Squad")
        }
        
        if s.squadProvider == nil {
                return nil, errors.New("squad provider not configured")
        }
        
        // Create virtual account in provider
        firstName := details["first_name"]
        lastName := details["last_name"]
        email := details["email"]
        mobile := details["mobile"]
        businessName := details["business_name"]
        bvn := details["bvn"]
        
        result, err := s.squadProvider.CreateVirtualAccount(
                businessName,
                firstName,
                lastName,
                email,
                mobile,
                models.CurrencyNGN,
                bvn,
        )
        
        if err != nil {
                return nil, fmt.Errorf("error creating squad virtual account: %w", err)
        }
        
        // Create virtual account in database
        account := &models.VirtualAccount{
                UserID:                userID,
                Provider:              provider,
                ProviderVirtualAcctID: result.ID,
                AccountNumber:         result.AccountNumber,
                AccountName:           result.AccountName,
                BankName:              result.BankName,
                BankCode:              result.BankCode,
                Currency:              models.CurrencyNGN,
                Status:                "active",
                IsActive:              true,
                Reference:             generateRandomString(16),
        }
        
        if err := s.repo.CreateVirtualAccount(account); err != nil {
                return nil, fmt.Errorf("error creating virtual account: %w", err)
        }
        
        return account, nil
}

// GetVirtualAccount gets a virtual account by ID
func (s *PaymentServiceImpl) GetVirtualAccount(id uint) (*models.VirtualAccount, error) {
        return s.repo.GetVirtualAccountByID(id)
}

// GetUserVirtualAccounts gets virtual accounts for a user
func (s *PaymentServiceImpl) GetUserVirtualAccounts(userID uint) ([]models.VirtualAccount, error) {
        return s.repo.GetUserVirtualAccounts(userID)
}

// CreateGift creates a gift
func (s *PaymentServiceImpl) CreateGift(
        senderUserID, recipientUserID, contentID uint,
        contentType string,
        giftType string,
        amount int,
        message string,
        isAnonymous bool,
) (*models.Gift, error) {
        // Validate gift type
        validGiftTypes := map[string]bool{
                "badge":   true,
                "award":   true,
                "donation": true,
        }
        
        if !validGiftTypes[giftType] {
                return nil, errors.New("invalid gift type")
        }
        
        // Calculate creator amount (70% of gift amount)
        creatorAmount := int(float64(amount) * 0.7)
        
        // Create gift
        gift := &models.Gift{
                SenderUserID:   senderUserID,
                RecipientUserID: recipientUserID,
                ContentID:      contentID,
                ContentType:    contentType,
                GiftType:       giftType,
                Amount:         amount,
                CreatorAmount:  creatorAmount,
                Currency:       models.CurrencyNGN, // Default to NGN
                Status:         "pending",
                Message:        message,
                IsAnonymous:    isAnonymous,
        }
        
        if err := s.repo.CreateGift(gift); err != nil {
                return nil, fmt.Errorf("error creating gift: %w", err)
        }
        
        return gift, nil
}

// GetGift gets a gift by ID
func (s *PaymentServiceImpl) GetGift(id uint) (*models.Gift, error) {
        return s.repo.GetGiftByID(id)
}

// GetUserSentGifts gets gifts sent by a user
func (s *PaymentServiceImpl) GetUserSentGifts(userID uint, page, pageSize int) ([]models.Gift, error) {
        return s.repo.GetUserSentGifts(userID, page, pageSize)
}

// GetUserReceivedGifts gets gifts received by a user
func (s *PaymentServiceImpl) GetUserReceivedGifts(userID uint, page, pageSize int) ([]models.Gift, error) {
        return s.repo.GetUserReceivedGifts(userID, page, pageSize)
}

// ProcessGiftPayment processes payment for a gift
func (s *PaymentServiceImpl) ProcessGiftPayment(giftID uint, paymentMethodID uint) (*models.Payment, error) {
        // Get gift
        gift, err := s.repo.GetGiftByID(giftID)
        if err != nil {
                return nil, fmt.Errorf("error getting gift: %w", err)
        }
        
        if gift.Status != "pending" {
                return nil, errors.New("gift is not pending payment")
        }
        
        // Get payment method
        paymentMethod, err := s.repo.GetPaymentMethodByID(paymentMethodID)
        if err != nil {
                return nil, fmt.Errorf("error getting payment method: %w", err)
        }
        
        if paymentMethod.UserID != gift.SenderUserID {
                return nil, errors.New("payment method does not belong to sender")
        }
        
        // Create payment intent
        metaData := map[string]interface{}{
                "gift_id":        gift.ID,
                "gift_type":      gift.GiftType,
                "content_id":     gift.ContentID,
                "content_type":   gift.ContentType,
                "recipient_id":   gift.RecipientUserID,
                "is_anonymous":   gift.IsAnonymous,
        }
        
        intent, _, err := s.CreatePaymentIntent(
                gift.SenderUserID,
                gift.Amount,
                gift.Currency,
                fmt.Sprintf("%s gift to user %d", gift.GiftType, gift.RecipientUserID),
                "", // No success URL for gifts
                "", // No cancel URL for gifts
                metaData,
                paymentMethod.Provider,
        )
        
        if err != nil {
                return nil, fmt.Errorf("error creating payment intent: %w", err)
        }
        
        // Process payment
        payment, err := s.ProcessPaymentResult(intent.ProviderReference, paymentMethod.Provider)
        if err != nil {
                return nil, fmt.Errorf("error processing payment: %w", err)
        }
        
        if payment.Status != models.StatusSucceeded {
                return nil, errors.New("payment failed")
        }
        
        // Update gift
        gift.PaymentID = payment.ID
        gift.Provider = paymentMethod.Provider
        gift.Status = "completed"
        now := time.Now()
        gift.CompletedAt = &now
        
        if err := s.repo.UpdateGift(gift); err != nil {
                return nil, fmt.Errorf("error updating gift: %w", err)
        }
        
        return payment, nil
}

// ProcessWebhook processes a webhook from a payment provider
func (s *PaymentServiceImpl) ProcessWebhook(
        provider models.PaymentProvider,
        eventType string,
        eventID string,
        payload map[string]interface{},
) error {
        // Store webhook event
        event := &models.WebhookEvent{
                Provider:     provider,
                EventType:    eventType,
                EventID:      eventID,
                ResourceType: "unknown",
                Payload:      models.JSONB(payload),
        }
        
        // Extract resource information based on provider and event type
        switch provider {
        case models.ProviderPaystack:
                // Handle Paystack webhook
                if eventType == "charge.success" {
                        if data, ok := payload["data"].(map[string]interface{}); ok {
                                if reference, ok := data["reference"].(string); ok {
                                        event.ResourceType = "payment"
                                        event.ResourceID = reference
                                        
                                        // Process payment
                                        _, err := s.ProcessPaymentResult(reference, provider)
                                        if err != nil {
                                                event.ProcessingError = err.Error()
                                        } else {
                                                event.IsProcessed = true
                                                now := time.Now()
                                                event.ProcessedAt = &now
                                        }
                                }
                        }
                }
                
        case models.ProviderFlutterwave:
                // Handle Flutterwave webhook
                if eventType == "charge.completed" {
                        if data, ok := payload["data"].(map[string]interface{}); ok {
                                if txRef, ok := data["tx_ref"].(string); ok {
                                        event.ResourceType = "payment"
                                        event.ResourceID = txRef
                                        
                                        // Process payment
                                        _, err := s.ProcessPaymentResult(txRef, provider)
                                        if err != nil {
                                                event.ProcessingError = err.Error()
                                        } else {
                                                event.IsProcessed = true
                                                now := time.Now()
                                                event.ProcessedAt = &now
                                        }
                                }
                        }
                }
                
        case models.ProviderSquad:
                // Handle Squad webhook
                if eventType == "payment_completed" {
                        if data, ok := payload["data"].(map[string]interface{}); ok {
                                if txRef, ok := data["transaction_reference"].(string); ok {
                                        event.ResourceType = "payment"
                                        event.ResourceID = txRef
                                        
                                        // Process payment
                                        _, err := s.ProcessPaymentResult(txRef, provider)
                                        if err != nil {
                                                event.ProcessingError = err.Error()
                                        } else {
                                                event.IsProcessed = true
                                                now := time.Now()
                                                event.ProcessedAt = &now
                                        }
                                }
                        }
                }
        }
        
        if err := s.repo.CreateWebhookEvent(event); err != nil {
                return fmt.Errorf("error creating webhook event: %w", err)
        }
        
        return nil
}

// GetPaymentAnalytics gets payment analytics
func (s *PaymentServiceImpl) GetPaymentAnalytics(startDate, endDate time.Time) (map[string]interface{}, error) {
        return s.repo.GetPaymentAnalytics(startDate, endDate)
}