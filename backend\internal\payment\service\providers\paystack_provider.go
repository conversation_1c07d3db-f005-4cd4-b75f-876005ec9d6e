package providers

import (
        "bytes"
        "encoding/json"
        "errors"
        "fmt"
        "io"
        "net/http"
        "time"

        "github.com/greatnigeria/internal/payment/models"
)

const (
        // PaystackBaseURL is the base URL for Paystack API
        PaystackBaseURL = "https://api.paystack.co"
        
        // RequestTimeout is the timeout for HTTP requests
        RequestTimeout = 30 * time.Second
)

// PaystackProvider implements the payment provider interface for Paystack
type PaystackProvider struct {
        secretKey string
        publicKey string
        client    *http.Client
}

// NewPaystackProvider creates a new Paystack provider
func NewPaystackProvider(secretKey, publicKey string) *PaystackProvider {
        return &PaystackProvider{
                secretKey: secretKey,
                publicKey: publicKey,
                client: &http.Client{
                        Timeout: RequestTimeout,
                },
        }
}

// PaystackResponse represents a generic Paystack API response
type PaystackResponse struct {
        Status  bool            `json:"status"`
        Message string          `json:"message"`
        Data    json.RawMessage `json:"data"`
}

// PaystackErrorResponse represents a Paystack API error response
type PaystackErrorResponse struct {
        Status  bool   `json:"status"`
        Message string `json:"message"`
}

// PaystackTransactionInitResponse represents the response for initializing a transaction
type PaystackTransactionInitResponse struct {
        AuthorizationURL string `json:"authorization_url"`
        AccessCode       string `json:"access_code"`
        Reference        string `json:"reference"`
}

// PaystackTransactionVerifyResponse represents the response for verifying a transaction
type PaystackTransactionVerifyResponse struct {
        ID              int             `json:"id"`
        Status          string          `json:"status"`
        Reference       string          `json:"reference"`
        Amount          int             `json:"amount"`
        PaidAt          string          `json:"paid_at"`
        Channel         string          `json:"channel"`
        Currency        string          `json:"currency"`
        Authorization   json.RawMessage `json:"authorization"`
        Customer        json.RawMessage `json:"customer"`
        Plan            interface{}     `json:"plan"`
        SubAccount      string          `json:"subaccount"`
        TransactionDate string          `json:"transaction_date"`
        MetaData        interface{}     `json:"metadata"`
}

// PaystackSubscriptionResponse represents a subscription response
type PaystackSubscriptionResponse struct {
        ID                 int             `json:"id"`
        CustomerID         int             `json:"customer"`
        PlanID             int             `json:"plan"`
        Status             string          `json:"status"`
        SubscriptionCode   string          `json:"subscription_code"`
        EmailToken         string          `json:"email_token"`
        Amount             int             `json:"amount"`
        CreatedAt          string          `json:"createdAt"`
        NextPaymentDate    string          `json:"next_payment_date"`
        Authorization      json.RawMessage `json:"authorization"`
        Plan               json.RawMessage `json:"plan_object"`
        Customer           json.RawMessage `json:"customer"`
        InvoiceLimit       int             `json:"invoice_limit"`
        CancelledAt        interface{}     `json:"cancelled_at"`
        Integration        int             `json:"integration"`
}

// PaystackPlanResponse represents a plan response
type PaystackPlanResponse struct {
        ID                   int         `json:"id"`
        Name                 string      `json:"name"`
        Description          string      `json:"description"`
        Amount               int         `json:"amount"`
        Interval             string      `json:"interval"`
        PlanCode             string      `json:"plan_code"`
        SendInvoices         bool        `json:"send_invoices"`
        SendSMS              bool        `json:"send_sms"`
        Currency             string      `json:"currency"`
        Integration          int         `json:"integration"`
        Domain               string      `json:"domain"`
        CreatedAt            string      `json:"created_at"`
        UpdatedAt            string      `json:"updated_at"`
        IsDeleted            bool        `json:"is_deleted"`
        InvoiceLimit         int         `json:"invoice_limit"`
        HostedPage           interface{} `json:"hosted_page"`
        HostedPageURL        interface{} `json:"hosted_page_url"`
        HostedPageSummary    interface{} `json:"hosted_page_summary"`
        Subscription         interface{} `json:"subscription"`
        Subscriptions        interface{} `json:"subscriptions"`
        MigrationSuccessful  bool        `json:"migration_successful"`
}

// PaystackRefundResponse represents a refund response
type PaystackRefundResponse struct {
        ID           int    `json:"id"`
        Reference    string `json:"reference"`
        Amount       int    `json:"amount"`
        Status       string `json:"status"`
        CreatedAt    string `json:"created_at"`
        Currency     string `json:"currency"`
        Customer     int    `json:"customer"`
        Deducted     int    `json:"deducted_amount"`
        MerchantNote string `json:"merchant_note"`
        Channel      string `json:"channel"`
        Transaction  int    `json:"transaction"`
}

// PaystackCustomerResponse represents a customer response
type PaystackCustomerResponse struct {
        ID                  int         `json:"id"`
        FirstName           string      `json:"first_name"`
        LastName            string      `json:"last_name"`
        Email               string      `json:"email"`
        Phone               string      `json:"phone"`
        CustomerCode        string      `json:"customer_code"`
        RiskAction          string      `json:"risk_action"`
        InternationalFormat string      `json:"international_format_phone"`
        Metadata            interface{} `json:"metadata"`
        Domain              string      `json:"domain"`
        CreatedAt           string      `json:"created_at"`
        UpdatedAt           string      `json:"updated_at"`
}

// PaystackTransferRecipientResponse represents a transfer recipient response
type PaystackTransferRecipientResponse struct {
        ID            int         `json:"id"`
        Type          string      `json:"type"`
        Name          string      `json:"name"`
        RecipientCode string      `json:"recipient_code"`
        Email         interface{} `json:"email"`
        PhoneNumber   interface{} `json:"phoneNumber"`
        Description   string      `json:"description"`
        Metadata      interface{} `json:"metadata"`
        Details       struct {
                AccountNumber string `json:"account_number"`
                AccountName   string `json:"account_name"`
                BankCode      string `json:"bank_code"`
                BankName      string `json:"bank_name"`
        } `json:"details"`
        CreatedAt string `json:"created_at"`
        UpdatedAt string `json:"updated_at"`
}

// PaystackTransferResponse represents a transfer response
type PaystackTransferResponse struct {
        ID             int         `json:"id"`
        Amount         int         `json:"amount"`
        Currency       string      `json:"currency"`
        Source         string      `json:"source"`
        Reason         string      `json:"reason"`
        Recipient      int         `json:"recipient"`
        Status         string      `json:"status"`
        TransferCode   string      `json:"transfer_code"`
        Reference      string      `json:"reference"`
        Integration    int         `json:"integration"`
        Domain         string      `json:"domain"`
        Failures       interface{} `json:"failures"`
        TransferredAt  interface{} `json:"transferred_at"`
        CreatedAt      string      `json:"created_at"`
        UpdatedAt      string      `json:"updated_at"`
}

// makeRequest makes a request to the Paystack API
func (p *PaystackProvider) makeRequest(method, endpoint string, body interface{}) ([]byte, error) {
        var reqBody io.Reader
        
        if body != nil {
                jsonBody, err := json.Marshal(body)
                if err != nil {
                        return nil, fmt.Errorf("error marshaling request body: %w", err)
                }
                reqBody = bytes.NewBuffer(jsonBody)
        }
        
        req, err := http.NewRequest(method, PaystackBaseURL+endpoint, reqBody)
        if err != nil {
                return nil, fmt.Errorf("error creating request: %w", err)
        }
        
        req.Header.Set("Authorization", "Bearer "+p.secretKey)
        req.Header.Set("Content-Type", "application/json")
        
        resp, err := p.client.Do(req)
        if err != nil {
                return nil, fmt.Errorf("error making request: %w", err)
        }
        defer resp.Body.Close()
        
        respBody, err := io.ReadAll(resp.Body)
        if err != nil {
                return nil, fmt.Errorf("error reading response body: %w", err)
        }
        
        if resp.StatusCode >= 400 {
                var errResp PaystackErrorResponse
                if err := json.Unmarshal(respBody, &errResp); err != nil {
                        return nil, fmt.Errorf("error parsing error response: %w", err)
                }
                return nil, errors.New(errResp.Message)
        }
        
        return respBody, nil
}

// InitializeTransaction initializes a transaction with Paystack
func (p *PaystackProvider) InitializeTransaction(amount int, email, reference, callbackURL string, metadata map[string]interface{}) (*PaystackTransactionInitResponse, error) {
        requestBody := map[string]interface{}{
                "amount":       amount,
                "email":        email,
                "reference":    reference,
                "callback_url": callbackURL,
                "metadata":     metadata,
        }
        
        respBody, err := p.makeRequest(http.MethodPost, "/transaction/initialize", requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var initResp PaystackTransactionInitResponse
        if err := json.Unmarshal(paystackResp.Data, &initResp); err != nil {
                return nil, fmt.Errorf("error parsing transaction response: %w", err)
        }
        
        return &initResp, nil
}

// VerifyTransaction verifies a transaction with Paystack
func (p *PaystackProvider) VerifyTransaction(reference string) (*PaystackTransactionVerifyResponse, error) {
        respBody, err := p.makeRequest(http.MethodGet, "/transaction/verify/"+reference, nil)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var verifyResp PaystackTransactionVerifyResponse
        if err := json.Unmarshal(paystackResp.Data, &verifyResp); err != nil {
                return nil, fmt.Errorf("error parsing transaction verification response: %w", err)
        }
        
        return &verifyResp, nil
}

// CreateCustomer creates a customer with Paystack
func (p *PaystackProvider) CreateCustomer(email, firstName, lastName, phone string, metadata map[string]interface{}) (*PaystackCustomerResponse, error) {
        requestBody := map[string]interface{}{
                "email":      email,
                "first_name": firstName,
                "last_name":  lastName,
                "phone":      phone,
                "metadata":   metadata,
        }
        
        respBody, err := p.makeRequest(http.MethodPost, "/customer", requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var customerResp PaystackCustomerResponse
        if err := json.Unmarshal(paystackResp.Data, &customerResp); err != nil {
                return nil, fmt.Errorf("error parsing customer response: %w", err)
        }
        
        return &customerResp, nil
}

// CreatePlan creates a subscription plan with Paystack
func (p *PaystackProvider) CreatePlan(name, description string, amount int, interval string, currency models.Currency) (*PaystackPlanResponse, error) {
        requestBody := map[string]interface{}{
                "name":        name,
                "description": description,
                "amount":      amount,
                "interval":    interval,
                "currency":    currency,
        }
        
        respBody, err := p.makeRequest(http.MethodPost, "/plan", requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var planResp PaystackPlanResponse
        if err := json.Unmarshal(paystackResp.Data, &planResp); err != nil {
                return nil, fmt.Errorf("error parsing plan response: %w", err)
        }
        
        return &planResp, nil
}

// GetPlan retrieves a plan from Paystack
func (p *PaystackProvider) GetPlan(planCode string) (*PaystackPlanResponse, error) {
        respBody, err := p.makeRequest(http.MethodGet, "/plan/"+planCode, nil)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var planResp PaystackPlanResponse
        if err := json.Unmarshal(paystackResp.Data, &planResp); err != nil {
                return nil, fmt.Errorf("error parsing plan response: %w", err)
        }
        
        return &planResp, nil
}

// UpdatePlan updates a plan in Paystack
func (p *PaystackProvider) UpdatePlan(planCode, name, description string, amount int) (*PaystackPlanResponse, error) {
        requestBody := map[string]interface{}{
                "name":        name,
                "description": description,
                "amount":      amount,
        }
        
        respBody, err := p.makeRequest(http.MethodPut, "/plan/"+planCode, requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var planResp PaystackPlanResponse
        if err := json.Unmarshal(paystackResp.Data, &planResp); err != nil {
                return nil, fmt.Errorf("error parsing plan response: %w", err)
        }
        
        return &planResp, nil
}

// CreateSubscription creates a subscription with Paystack
func (p *PaystackProvider) CreateSubscription(customerEmail, planCode string, metadata map[string]interface{}) (*PaystackSubscriptionResponse, error) {
        requestBody := map[string]interface{}{
                "customer":  customerEmail,
                "plan":      planCode,
                "metadata":  metadata,
        }
        
        respBody, err := p.makeRequest(http.MethodPost, "/subscription", requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var subscriptionResp PaystackSubscriptionResponse
        if err := json.Unmarshal(paystackResp.Data, &subscriptionResp); err != nil {
                return nil, fmt.Errorf("error parsing subscription response: %w", err)
        }
        
        return &subscriptionResp, nil
}

// EnableSubscription enables a subscription with Paystack
func (p *PaystackProvider) EnableSubscription(subscriptionCode, emailToken string) (*PaystackSubscriptionResponse, error) {
        requestBody := map[string]interface{}{
                "code":  subscriptionCode,
                "token": emailToken,
        }
        
        respBody, err := p.makeRequest(http.MethodPost, "/subscription/enable", requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var subscriptionResp PaystackSubscriptionResponse
        if err := json.Unmarshal(paystackResp.Data, &subscriptionResp); err != nil {
                return nil, fmt.Errorf("error parsing subscription response: %w", err)
        }
        
        return &subscriptionResp, nil
}

// DisableSubscription disables a subscription with Paystack
func (p *PaystackProvider) DisableSubscription(subscriptionCode, emailToken string) (*PaystackSubscriptionResponse, error) {
        requestBody := map[string]interface{}{
                "code":  subscriptionCode,
                "token": emailToken,
        }
        
        respBody, err := p.makeRequest(http.MethodPost, "/subscription/disable", requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var subscriptionResp PaystackSubscriptionResponse
        if err := json.Unmarshal(paystackResp.Data, &subscriptionResp); err != nil {
                return nil, fmt.Errorf("error parsing subscription response: %w", err)
        }
        
        return &subscriptionResp, nil
}

// GetSubscription retrieves a subscription from Paystack
func (p *PaystackProvider) GetSubscription(subscriptionID string) (*PaystackSubscriptionResponse, error) {
        respBody, err := p.makeRequest(http.MethodGet, "/subscription/"+subscriptionID, nil)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var subscriptionResp PaystackSubscriptionResponse
        if err := json.Unmarshal(paystackResp.Data, &subscriptionResp); err != nil {
                return nil, fmt.Errorf("error parsing subscription response: %w", err)
        }
        
        return &subscriptionResp, nil
}

// InitiateRefund initiates a refund with Paystack
func (p *PaystackProvider) InitiateRefund(transactionReference string, amount int, reason string) (*PaystackRefundResponse, error) {
        requestBody := map[string]interface{}{
                "transaction": transactionReference,
                "amount":      amount,
                "merchant_note": reason,
        }
        
        respBody, err := p.makeRequest(http.MethodPost, "/refund", requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var refundResp PaystackRefundResponse
        if err := json.Unmarshal(paystackResp.Data, &refundResp); err != nil {
                return nil, fmt.Errorf("error parsing refund response: %w", err)
        }
        
        return &refundResp, nil
}

// GetRefund retrieves a refund from Paystack
func (p *PaystackProvider) GetRefund(reference string) (*PaystackRefundResponse, error) {
        respBody, err := p.makeRequest(http.MethodGet, "/refund/"+reference, nil)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var refundResp PaystackRefundResponse
        if err := json.Unmarshal(paystackResp.Data, &refundResp); err != nil {
                return nil, fmt.Errorf("error parsing refund response: %w", err)
        }
        
        return &refundResp, nil
}

// CreateTransferRecipient creates a transfer recipient with Paystack
func (p *PaystackProvider) CreateTransferRecipient(name, accountNumber, bankCode, description string) (*PaystackTransferRecipientResponse, error) {
        requestBody := map[string]interface{}{
                "type":          "nuban",
                "name":          name,
                "account_number": accountNumber,
                "bank_code":     bankCode,
                "description":   description,
        }
        
        respBody, err := p.makeRequest(http.MethodPost, "/transferrecipient", requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var recipientResp PaystackTransferRecipientResponse
        if err := json.Unmarshal(paystackResp.Data, &recipientResp); err != nil {
                return nil, fmt.Errorf("error parsing recipient response: %w", err)
        }
        
        return &recipientResp, nil
}

// InitiateTransfer initiates a transfer with Paystack
func (p *PaystackProvider) InitiateTransfer(amount int, recipientCode, reason, reference string, currency models.Currency) (*PaystackTransferResponse, error) {
        requestBody := map[string]interface{}{
                "source":    "balance",
                "amount":    amount,
                "recipient": recipientCode,
                "reason":    reason,
                "reference": reference,
                "currency":  currency,
        }
        
        respBody, err := p.makeRequest(http.MethodPost, "/transfer", requestBody)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var transferResp PaystackTransferResponse
        if err := json.Unmarshal(paystackResp.Data, &transferResp); err != nil {
                return nil, fmt.Errorf("error parsing transfer response: %w", err)
        }
        
        return &transferResp, nil
}

// VerifyTransfer verifies a transfer with Paystack
func (p *PaystackProvider) VerifyTransfer(reference string) (*PaystackTransferResponse, error) {
        respBody, err := p.makeRequest(http.MethodGet, "/transfer/verify/"+reference, nil)
        if err != nil {
                return nil, err
        }
        
        var paystackResp PaystackResponse
        if err := json.Unmarshal(respBody, &paystackResp); err != nil {
                return nil, fmt.Errorf("error parsing response: %w", err)
        }
        
        if !paystackResp.Status {
                return nil, errors.New(paystackResp.Message)
        }
        
        var transferResp PaystackTransferResponse
        if err := json.Unmarshal(paystackResp.Data, &transferResp); err != nil {
                return nil, fmt.Errorf("error parsing transfer response: %w", err)
        }
        
        return &transferResp, nil
}