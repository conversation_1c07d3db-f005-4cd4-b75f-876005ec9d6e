package providers

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/greatnigeria/internal/payment/models"
)

const (
	// SquadBaseURL is the base URL for Squad API
	SquadBaseURL = "https://api.squadco.com"
)

// SquadProvider implements the payment provider interface for Squad
type SquadProvider struct {
	secretKey string
	publicKey string
	client    *http.Client
}

// NewSquadProvider creates a new Squad provider
func NewSquadProvider(secretKey, publicKey string) *SquadProvider {
	return &SquadProvider{
		secretKey: secretKey,
		publicKey: publicKey,
		client: &http.Client{
			Timeout: RequestTimeout,
		},
	}
}

// SquadResponse represents a generic Squad API response
type SquadResponse struct {
	Status  string          `json:"status"`
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

// SquadErrorResponse represents a Squad API error response
type SquadErrorResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}

// SquadTransactionResponse represents a transaction response
type SquadTransactionResponse struct {
	TransactionReference string `json:"transaction_reference"`
	CheckoutURL          string `json:"checkout_url"`
	RedirectLink         string `json:"redirect_link"`
}

// SquadTransactionVerifyResponse represents a transaction verification response
type SquadTransactionVerifyResponse struct {
	TransactionReference string `json:"transaction_reference"`
	PaymentReference     string `json:"payment_reference"`
	Amount              float64 `json:"amount"`
	Currency            string  `json:"currency"`
	Status              string  `json:"status"`
	PaymentMethod       string  `json:"payment_method"`
	PaidAt              string  `json:"paid_at"`
	Channel             string  `json:"channel"`
	CustomerEmail       string  `json:"customer_email"`
	CustomerName        string  `json:"customer_name"`
	TransactionFee      float64 `json:"transaction_fee"`
	Gate                string  `json:"gate"`
	MerchantServiceCharge float64 `json:"merchant_service_charge"`
	MerchantAmount      float64 `json:"merchant_amount"`
	MetaData            json.RawMessage `json:"meta_data"`
}

// SquadOTPResponse represents an OTP response
type SquadOTPResponse struct {
	Reference          string `json:"reference"`
	IsOTPValidated     bool   `json:"is_otp_validated"`
	IsEnrolled         bool   `json:"is_enrolled"`
	IsBlacklisted      bool   `json:"is_blacklisted"`
	IsReused           bool   `json:"is_reused"`
	IsHardDecline      bool   `json:"is_hard_decline"`
	IsRetry            bool   `json:"is_retry"`
	RetryIn            int    `json:"retry_in"`
	Message            string `json:"message"`
}

// SquadPlanResponse represents a plan response
type SquadPlanResponse struct {
	ID               string `json:"plan_id"`
	Name             string `json:"name"`
	Interval         string `json:"interval"`
	Amount           int    `json:"amount"`
	Currency         string `json:"currency"`
	PlanCode         string `json:"plan_code"`
	Status           string `json:"status"`
	CreatedAt        string `json:"created_at"`
}

// SquadCustomerResponse represents a customer response
type SquadCustomerResponse struct {
	ID              string `json:"customer_id"`
	Name            string `json:"name"`
	Email           string `json:"email"`
	Phone           string `json:"phone"`
	TransactionCount int    `json:"transaction_count"`
	CreatedAt       string `json:"created_at"`
}

// SquadSubscriptionResponse represents a subscription response
type SquadSubscriptionResponse struct {
	ID               string `json:"subscription_id"`
	CustomerID       string `json:"customer_id"`
	PlanID           string `json:"plan_id"`
	Gateway          string `json:"gateway"`
	Status           string `json:"status"`
	SubcriptionCode  string `json:"subscription_code"`
	Email            string `json:"email"`
	Amount           int    `json:"amount"`
	AuthorizationCode string `json:"authorization_code"`
	StartDate        string `json:"start_date"`
	NextPaymentDate  string `json:"next_payment_date"`
	CreatedAt        string `json:"created_at"`
}

// SquadRefundResponse represents a refund response
type SquadRefundResponse struct {
	ID                string `json:"refund_id"`
	TransactionReference string `json:"transaction_reference"`
	Amount            float64 `json:"amount"`
	Comment           string `json:"comment"`
	Status            string `json:"status"`
	RefundReference   string `json:"refund_reference"`
	CreatedAt         string `json:"created_at"`
}

// SquadVirtualAccountResponse represents a virtual account response
type SquadVirtualAccountResponse struct {
	ID                string `json:"virtual_account_id"`
	BusinessName      string `json:"business_name"`
	AccountName       string `json:"account_name"`
	AccountNumber     string `json:"account_number"`
	BankCode          string `json:"bank_code"`
	BankName          string `json:"bank_name"`
	Status            string `json:"status"`
	Currency          string `json:"currency"`
	CreatedAt         string `json:"created_at"`
}

// makeRequest makes a request to the Squad API
func (s *SquadProvider) makeRequest(method, endpoint string, body interface{}) ([]byte, error) {
	var reqBody io.Reader
	
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("error marshaling request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}
	
	req, err := http.NewRequest(method, SquadBaseURL+endpoint, reqBody)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}
	
	req.Header.Set("Authorization", "Bearer "+s.secretKey)
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()
	
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}
	
	if resp.StatusCode >= 400 {
		var errResp SquadErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err != nil {
			return nil, fmt.Errorf("error parsing error response: %w", err)
		}
		return nil, errors.New(errResp.Message)
	}
	
	return respBody, nil
}

// InitializeTransaction initializes a transaction with Squad
func (s *SquadProvider) InitializeTransaction(
	amount float64,
	email string,
	currency models.Currency,
	callbackURL, returnURL string,
	transactionRef string,
	customerName string,
	metaData map[string]interface{},
) (*SquadTransactionResponse, error) {
	requestBody := map[string]interface{}{
		"amount":        amount,
		"email":         email,
		"currency":      currency,
		"callback_url":  callbackURL,
		"return_url":    returnURL,
		"tx_ref":        transactionRef,
		"customer_name": customerName,
		"meta_data":     metaData,
	}
	
	respBody, err := s.makeRequest(http.MethodPost, "/transaction/initialize", requestBody)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var transResp SquadTransactionResponse
	if err := json.Unmarshal(squadResp.Data, &transResp); err != nil {
		return nil, fmt.Errorf("error parsing transaction response: %w", err)
	}
	
	return &transResp, nil
}

// VerifyTransaction verifies a transaction with Squad
func (s *SquadProvider) VerifyTransaction(transactionRef string) (*SquadTransactionVerifyResponse, error) {
	respBody, err := s.makeRequest(http.MethodGet, "/transaction/verify/"+transactionRef, nil)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var verifyResp SquadTransactionVerifyResponse
	if err := json.Unmarshal(squadResp.Data, &verifyResp); err != nil {
		return nil, fmt.Errorf("error parsing verification response: %w", err)
	}
	
	return &verifyResp, nil
}

// ChargeCard charges a card with Squad
func (s *SquadProvider) ChargeCard(
	amount float64,
	email, customerName string,
	cardNumber, expiryMonth, expiryYear, cvv string,
	currency models.Currency,
	transactionRef string,
	metaData map[string]interface{},
) (string, error) {
	requestBody := map[string]interface{}{
		"amount":        amount,
		"email":         email,
		"currency":      currency,
		"tx_ref":        transactionRef,
		"customer_name": customerName,
		"card": map[string]string{
			"card_no":      cardNumber,
			"expiry_month": expiryMonth,
			"expiry_year":  expiryYear,
			"cvv":          cvv,
		},
		"meta_data": metaData,
	}
	
	respBody, err := s.makeRequest(http.MethodPost, "/transaction/charge_card", requestBody)
	if err != nil {
		return "", err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return "", fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return "", errors.New(squadResp.Message)
	}
	
	// For card charges, the response contains a reference for OTP validation
	var respData struct {
		Reference string `json:"reference"`
	}
	
	if err := json.Unmarshal(squadResp.Data, &respData); err != nil {
		return "", fmt.Errorf("error parsing charge response: %w", err)
	}
	
	return respData.Reference, nil
}

// ValidateOTP validates an OTP with Squad
func (s *SquadProvider) ValidateOTP(reference, otp string) (*SquadOTPResponse, error) {
	requestBody := map[string]interface{}{
		"reference": reference,
		"otp":       otp,
	}
	
	respBody, err := s.makeRequest(http.MethodPost, "/transaction/validate", requestBody)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var otpResp SquadOTPResponse
	if err := json.Unmarshal(squadResp.Data, &otpResp); err != nil {
		return nil, fmt.Errorf("error parsing OTP response: %w", err)
	}
	
	return &otpResp, nil
}

// CreatePlan creates a subscription plan with Squad
func (s *SquadProvider) CreatePlan(name, interval string, amount int, currency models.Currency) (*SquadPlanResponse, error) {
	requestBody := map[string]interface{}{
		"name":     name,
		"interval": interval,
		"amount":   amount,
		"currency": currency,
	}
	
	respBody, err := s.makeRequest(http.MethodPost, "/subscription/plan/create", requestBody)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var planResp SquadPlanResponse
	if err := json.Unmarshal(squadResp.Data, &planResp); err != nil {
		return nil, fmt.Errorf("error parsing plan response: %w", err)
	}
	
	return &planResp, nil
}

// GetPlan retrieves a plan from Squad
func (s *SquadProvider) GetPlan(planID string) (*SquadPlanResponse, error) {
	respBody, err := s.makeRequest(http.MethodGet, "/subscription/plan/"+planID, nil)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var planResp SquadPlanResponse
	if err := json.Unmarshal(squadResp.Data, &planResp); err != nil {
		return nil, fmt.Errorf("error parsing plan response: %w", err)
	}
	
	return &planResp, nil
}

// UpdatePlan updates a plan in Squad
func (s *SquadProvider) UpdatePlan(planID, name, interval string, amount int, currency models.Currency, isActive bool) (*SquadPlanResponse, error) {
	status := "active"
	if !isActive {
		status = "inactive"
	}
	
	requestBody := map[string]interface{}{
		"name":     name,
		"interval": interval,
		"amount":   amount,
		"currency": currency,
		"status":   status,
	}
	
	respBody, err := s.makeRequest(http.MethodPut, "/subscription/plan/"+planID, requestBody)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var planResp SquadPlanResponse
	if err := json.Unmarshal(squadResp.Data, &planResp); err != nil {
		return nil, fmt.Errorf("error parsing plan response: %w", err)
	}
	
	return &planResp, nil
}

// CreateSubscription creates a subscription with Squad
func (s *SquadProvider) CreateSubscription(
	customerEmail, customerName, planID string,
	amount int,
	cardNumber, expiryMonth, expiryYear, cvv string,
	startDate string,
) (*SquadSubscriptionResponse, error) {
	requestBody := map[string]interface{}{
		"customer_email": customerEmail,
		"customer_name":  customerName,
		"plan_id":        planID,
		"amount":         amount,
		"card": map[string]string{
			"card_no":      cardNumber,
			"expiry_month": expiryMonth,
			"expiry_year":  expiryYear,
			"cvv":          cvv,
		},
		"start_date": startDate,
	}
	
	respBody, err := s.makeRequest(http.MethodPost, "/subscription/create", requestBody)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var subResp SquadSubscriptionResponse
	if err := json.Unmarshal(squadResp.Data, &subResp); err != nil {
		return nil, fmt.Errorf("error parsing subscription response: %w", err)
	}
	
	return &subResp, nil
}

// GetSubscription retrieves a subscription from Squad
func (s *SquadProvider) GetSubscription(subscriptionID string) (*SquadSubscriptionResponse, error) {
	respBody, err := s.makeRequest(http.MethodGet, "/subscription/"+subscriptionID, nil)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var subResp SquadSubscriptionResponse
	if err := json.Unmarshal(squadResp.Data, &subResp); err != nil {
		return nil, fmt.Errorf("error parsing subscription response: %w", err)
	}
	
	return &subResp, nil
}

// CancelSubscription cancels a subscription with Squad
func (s *SquadProvider) CancelSubscription(subscriptionID string) error {
	respBody, err := s.makeRequest(http.MethodPost, "/subscription/"+subscriptionID+"/cancel", nil)
	if err != nil {
		return err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return errors.New(squadResp.Message)
	}
	
	return nil
}

// ActivateSubscription activates a subscription with Squad
func (s *SquadProvider) ActivateSubscription(subscriptionID string) error {
	respBody, err := s.makeRequest(http.MethodPost, "/subscription/"+subscriptionID+"/activate", nil)
	if err != nil {
		return err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return errors.New(squadResp.Message)
	}
	
	return nil
}

// InitiateRefund initiates a refund with Squad
func (s *SquadProvider) InitiateRefund(transactionRef string, amount float64, reason string) (*SquadRefundResponse, error) {
	requestBody := map[string]interface{}{
		"transaction_ref": transactionRef,
		"amount":          amount,
		"comment":         reason,
	}
	
	respBody, err := s.makeRequest(http.MethodPost, "/transaction/refund", requestBody)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var refundResp SquadRefundResponse
	if err := json.Unmarshal(squadResp.Data, &refundResp); err != nil {
		return nil, fmt.Errorf("error parsing refund response: %w", err)
	}
	
	return &refundResp, nil
}

// GetRefund retrieves a refund from Squad
func (s *SquadProvider) GetRefund(refundID string) (*SquadRefundResponse, error) {
	respBody, err := s.makeRequest(http.MethodGet, "/transaction/refund/"+refundID, nil)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var refundResp SquadRefundResponse
	if err := json.Unmarshal(squadResp.Data, &refundResp); err != nil {
		return nil, fmt.Errorf("error parsing refund response: %w", err)
	}
	
	return &refundResp, nil
}

// CreateVirtualAccount creates a virtual account with Squad
func (s *SquadProvider) CreateVirtualAccount(
	businessName, firstName, lastName, email, mobile string,
	currency models.Currency,
	bvn string,
) (*SquadVirtualAccountResponse, error) {
	requestBody := map[string]interface{}{
		"business_name": businessName,
		"firstname":     firstName,
		"lastname":      lastName,
		"email":         email,
		"mobile":        mobile,
		"country":       "NG",
		"currency":      currency,
		"bvn":           bvn,
	}
	
	respBody, err := s.makeRequest(http.MethodPost, "/virtual-account", requestBody)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var vaResp SquadVirtualAccountResponse
	if err := json.Unmarshal(squadResp.Data, &vaResp); err != nil {
		return nil, fmt.Errorf("error parsing virtual account response: %w", err)
	}
	
	return &vaResp, nil
}

// GetVirtualAccount retrieves a virtual account from Squad
func (s *SquadProvider) GetVirtualAccount(virtualAccountID string) (*SquadVirtualAccountResponse, error) {
	respBody, err := s.makeRequest(http.MethodGet, "/virtual-account/"+virtualAccountID, nil)
	if err != nil {
		return nil, err
	}
	
	var squadResp SquadResponse
	if err := json.Unmarshal(respBody, &squadResp); err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}
	
	if squadResp.Status != "success" {
		return nil, errors.New(squadResp.Message)
	}
	
	var vaResp SquadVirtualAccountResponse
	if err := json.Unmarshal(squadResp.Data, &vaResp); err != nil {
		return nil, fmt.Errorf("error parsing virtual account response: %w", err)
	}
	
	return &vaResp, nil
}