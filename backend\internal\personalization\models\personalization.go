package models

import (
	"time"
)

// LearningStyle represents a user's learning style preferences
type LearningStyle struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	UserID            uint      `json:"userId" gorm:"uniqueIndex;not null"`
	Visual            int       `json:"visual" gorm:"not null;default:0"`         // 0-100 score
	Auditory          int       `json:"auditory" gorm:"not null;default:0"`       // 0-100 score
	ReadWrite         int       `json:"readWrite" gorm:"not null;default:0"`      // 0-100 score
	Kinesthetic       int       `json:"kinesthetic" gorm:"not null;default:0"`    // 0-100 score
	Social            int       `json:"social" gorm:"not null;default:0"`         // 0-100 score
	Solitary          int       `json:"solitary" gorm:"not null;default:0"`       // 0-100 score
	Logical           int       `json:"logical" gorm:"not null;default:0"`        // 0-100 score
	PrimaryStyle      string    `json:"primaryStyle" gorm:"size:50"`              // Dominant learning style
	SecondaryStyle    string    `json:"secondaryStyle" gorm:"size:50"`            // Secondary learning style
	AssessmentTaken   bool      `json:"assessmentTaken" gorm:"not null;default:false"`
	AssessmentDate    time.Time `json:"assessmentDate"`
	LastUpdated       time.Time `json:"lastUpdated" gorm:"autoUpdateTime"`
}

// LearningPreference represents a user's content preferences
type LearningPreference struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	UserID            uint      `json:"userId" gorm:"uniqueIndex;not null"`
	PreferredTopics   []string  `json:"preferredTopics" gorm:"-"`                 // Stored as JSON in DB
	PreferredTopicsDB string    `json:"-" gorm:"column:preferred_topics;type:text"`
	AvoidTopics       []string  `json:"avoidTopics" gorm:"-"`                     // Stored as JSON in DB
	AvoidTopicsDB     string    `json:"-" gorm:"column:avoid_topics;type:text"`
	PreferredFormats  []string  `json:"preferredFormats" gorm:"-"`                // Stored as JSON in DB
	PreferredFormatsDB string   `json:"-" gorm:"column:preferred_formats;type:text"`
	DifficultyLevel   string    `json:"difficultyLevel" gorm:"size:20;not null;default:'medium'"`
	LearningGoals     []string  `json:"learningGoals" gorm:"-"`                   // Stored as JSON in DB
	LearningGoalsDB   string    `json:"-" gorm:"column:learning_goals;type:text"`
	LastUpdated       time.Time `json:"lastUpdated" gorm:"autoUpdateTime"`
}

// PersonalizedPath represents a personalized learning path for a user
type PersonalizedPath struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	UserID            uint      `json:"userId" gorm:"index;not null"`
	Name              string    `json:"name" gorm:"size:100;not null"`
	Description       string    `json:"description" gorm:"size:500"`
	CreatedAt         time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt         time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
	IsActive          bool      `json:"isActive" gorm:"not null;default:true"`
	CompletionRate    float64   `json:"completionRate" gorm:"not null;default:0"` // 0-100 percentage
}

// PathItem represents an item in a personalized learning path
type PathItem struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	PathID            uint      `json:"pathId" gorm:"index;not null"`
	ItemType          string    `json:"itemType" gorm:"size:50;not null"`         // book, course, tutorial, etc.
	ItemID            uint      `json:"itemId" gorm:"not null"`                   // ID of the referenced item
	Title             string    `json:"title" gorm:"size:200;not null"`
	Description       string    `json:"description" gorm:"size:500"`
	Order             int       `json:"order" gorm:"not null"`                    // Position in the path
	IsCompleted       bool      `json:"isCompleted" gorm:"not null;default:false"`
	CompletedAt       *time.Time `json:"completedAt"`
	EstimatedDuration int       `json:"estimatedDuration" gorm:"not null;default:0"` // In minutes
}

// AssessmentQuestion represents a question in the learning style assessment
type AssessmentQuestion struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	Question          string    `json:"question" gorm:"size:500;not null"`
	Options           []string  `json:"options" gorm:"-"`                         // Stored as JSON in DB
	OptionsDB         string    `json:"-" gorm:"column:options;type:text"`
	StyleDimension    string    `json:"styleDimension" gorm:"size:50;not null"`   // visual, auditory, etc.
	Weight            int       `json:"weight" gorm:"not null;default:1"`         // Question importance
	IsActive          bool      `json:"isActive" gorm:"not null;default:true"`
}

// AssessmentResponse represents a user's response to an assessment question
type AssessmentResponse struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	UserID            uint      `json:"userId" gorm:"index;not null"`
	QuestionID        uint      `json:"questionId" gorm:"index;not null"`
	SelectedOption    int       `json:"selectedOption" gorm:"not null"`           // Index of selected option
	CreatedAt         time.Time `json:"createdAt" gorm:"autoCreateTime"`
}

// ContentRecommendation represents a content recommendation for a user
type ContentRecommendation struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	UserID            uint      `json:"userId" gorm:"index;not null"`
	ContentType       string    `json:"contentType" gorm:"size:50;not null"`      // book, course, tutorial, etc.
	ContentID         uint      `json:"contentId" gorm:"not null"`                // ID of the recommended content
	Title             string    `json:"title" gorm:"size:200;not null"`
	Description       string    `json:"description" gorm:"size:500"`
	RecommendationScore float64 `json:"recommendationScore" gorm:"not null"`      // 0-100 score
	ReasonCodes       []string  `json:"reasonCodes" gorm:"-"`                     // Stored as JSON in DB
	ReasonCodesDB     string    `json:"-" gorm:"column:reason_codes;type:text"`
	IsViewed          bool      `json:"isViewed" gorm:"not null;default:false"`
	IsSaved           bool      `json:"isSaved" gorm:"not null;default:false"`
	IsRejected        bool      `json:"isRejected" gorm:"not null;default:false"`
	CreatedAt         time.Time `json:"createdAt" gorm:"autoCreateTime"`
}

// DifficultyLevel represents a content difficulty level
type DifficultyLevel struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	Name              string    `json:"name" gorm:"size:50;uniqueIndex;not null"` // beginner, intermediate, advanced, expert
	Description       string    `json:"description" gorm:"size:500"`
	RequiredScore     int       `json:"requiredScore" gorm:"not null"`            // Minimum score required to access
}

// UserPerformance represents a user's performance metrics
type UserPerformance struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	UserID            uint      `json:"userId" gorm:"uniqueIndex;not null"`
	OverallScore      int       `json:"overallScore" gorm:"not null;default:0"`   // 0-100 score
	TopicScores       map[string]int `json:"topicScores" gorm:"-"`                // Stored as JSON in DB
	TopicScoresDB     string    `json:"-" gorm:"column:topic_scores;type:text"`
	QuizzesTaken      int       `json:"quizzesTaken" gorm:"not null;default:0"`
	QuizAvgScore      float64   `json:"quizAvgScore" gorm:"not null;default:0"`   // 0-100 percentage
	ContentCompleted  int       `json:"contentCompleted" gorm:"not null;default:0"`
	LastUpdated       time.Time `json:"lastUpdated" gorm:"autoUpdateTime"`
}

// LearningPathTemplate represents a template for creating personalized learning paths
type LearningPathTemplate struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	Name              string    `json:"name" gorm:"size:100;not null"`
	Description       string    `json:"description" gorm:"size:500"`
	TargetStyles      []string  `json:"targetStyles" gorm:"-"`                    // Stored as JSON in DB
	TargetStylesDB    string    `json:"-" gorm:"column:target_styles;type:text"`
	DifficultyLevel   string    `json:"difficultyLevel" gorm:"size:20;not null;default:'medium'"`
	Topics            []string  `json:"topics" gorm:"-"`                          // Stored as JSON in DB
	TopicsDB          string    `json:"-" gorm:"column:topics;type:text"`
	IsActive          bool      `json:"isActive" gorm:"not null;default:true"`
	CreatedAt         time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt         time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TemplateItem represents an item in a learning path template
type TemplateItem struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	TemplateID        uint      `json:"templateId" gorm:"index;not null"`
	ItemType          string    `json:"itemType" gorm:"size:50;not null"`         // book, course, tutorial, etc.
	ItemID            uint      `json:"itemId"`                                   // Optional specific item ID
	ItemCriteria      string    `json:"itemCriteria" gorm:"size:500"`             // JSON criteria for dynamic selection
	Order             int       `json:"order" gorm:"not null"`                    // Position in the template
	IsRequired        bool      `json:"isRequired" gorm:"not null;default:true"`
	StyleWeights      map[string]int `json:"styleWeights" gorm:"-"`               // Stored as JSON in DB
	StyleWeightsDB    string    `json:"-" gorm:"column:style_weights;type:text"`
}

// AssessmentResult represents the result of a learning style assessment
type AssessmentResult struct {
	UserID            uint                  `json:"userId"`
	LearningStyle     LearningStyle         `json:"learningStyle"`
	Recommendations   []ContentRecommendation `json:"recommendations"`
	SuggestedPaths    []PersonalizedPath    `json:"suggestedPaths"`
}

// PersonalizationRequest represents a request for personalized content
type PersonalizationRequest struct {
	UserID            uint      `json:"userId"`
	ContentType       string    `json:"contentType,omitempty"`  // Optional filter by content type
	Topic             string    `json:"topic,omitempty"`        // Optional filter by topic
	Count             int       `json:"count,omitempty"`        // Number of recommendations to return
}

// PersonalizationResponse represents a response with personalized content
type PersonalizationResponse struct {
	Recommendations   []ContentRecommendation `json:"recommendations"`
	HasMoreResults    bool                    `json:"hasMoreResults"`
	UserHasStyle      bool                    `json:"userHasStyle"`  // Whether the user has taken the assessment
}
