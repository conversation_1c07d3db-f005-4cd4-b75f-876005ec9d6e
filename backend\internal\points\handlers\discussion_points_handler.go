package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/greatnigeria/internal/points/models"
	"github.com/greatnigeria/internal/points/service"
)

// DiscussionPointsHandlerConfig defines the configuration for the discussion points handler
type DiscussionPointsHandlerConfig struct {
	// Points awarded for creating a new topic
	NewTopicPoints int
	
	// Points awarded for posting a reply
	ReplyPoints int
	
	// Maximum points awarded per day for discussion activities
	DailyMaxDiscussionPoints int
	
	// Points awarded for upvotes received
	UpvoteReceivedPoints int
	
	// Points for having your topic marked as featured 
	FeaturedTopicPoints int
	
	// Points for receiving an award on your post
	AwardReceivedPoints int
	
	// Quality multiplier for high-quality posts (applied to base points)
	QualityMultiplier float64
	
	// Boolean to enable/disable discussion points
	DiscussionPointsEnabled bool
}

// DefaultDiscussionPointsConfig returns the default configuration for discussion points
func DefaultDiscussionPointsConfig() DiscussionPointsHandlerConfig {
	return DiscussionPointsHandlerConfig{
		NewTopicPoints:          20,
		ReplyPoints:             10,
		DailyMaxDiscussionPoints: 100,
		UpvoteReceivedPoints:    5,
		FeaturedTopicPoints:     50,
		AwardReceivedPoints:     25,
		QualityMultiplier:       1.5,
		DiscussionPointsEnabled: true,
	}
}

// DiscussionPointsHandler manages points related to forum discussions
type DiscussionPointsHandler struct {
	pointsService service.PointsService
	config        DiscussionPointsHandlerConfig
}

// NewDiscussionPointsHandler creates a new discussion points handler
func NewDiscussionPointsHandler(pointsService service.PointsService, config DiscussionPointsHandlerConfig) *DiscussionPointsHandler {
	return &DiscussionPointsHandler{
		pointsService: pointsService,
		config:        config,
	}
}

// AwardNewTopicPoints awards points to a user for creating a new forum topic
func (h *DiscussionPointsHandler) AwardNewTopicPoints(c *gin.Context) {
	if !h.config.DiscussionPointsEnabled {
		c.JSON(http.StatusOK, gin.H{"message": "Discussion points are currently disabled", "points_awarded": 0})
		return
	}

	var req struct {
		UserID   uint   `json:"user_id" binding:"required"`
		TopicID  uint   `json:"topic_id" binding:"required"`
		Quality  string `json:"quality"` // "low", "medium", "high"
		Category string `json:"category"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Note: In a production system, we would check if the user has exceeded
	// their daily points limit for discussion activities.
	// For now, we'll skip this check as we don't have that functionality yet.
	// TODO: Implement daily points limit check

	// Calculate points based on quality
	pointsToAward := h.config.NewTopicPoints
	qualityMultiplier := 1.0

	switch req.Quality {
	case "high":
		qualityMultiplier = h.config.QualityMultiplier
	case "medium":
		qualityMultiplier = 1.2
	case "low":
		qualityMultiplier = 0.8
	}

	adjustedPoints := int(float64(pointsToAward) * qualityMultiplier)

	// Award points
	metadata := map[string]interface{}{
		"topic_id":  req.TopicID,
		"action":    "created_topic",
		"quality":   req.Quality,
		"category":  req.Category,
		"timestamp": time.Now().Unix(),
	}

	sourceType := models.PointSourceDiscussion
	transaction, err := h.pointsService.AwardPoints(req.UserID, adjustedPoints, sourceType, "topic", req.TopicID, "New forum topic", metadata)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award points"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Points awarded for new topic",
		"points_awarded": adjustedPoints,
		"transaction_id": transaction.ID,
	})
}

// AwardReplyPoints awards points to a user for posting a reply in a forum
func (h *DiscussionPointsHandler) AwardReplyPoints(c *gin.Context) {
	if !h.config.DiscussionPointsEnabled {
		c.JSON(http.StatusOK, gin.H{"message": "Discussion points are currently disabled", "points_awarded": 0})
		return
	}

	var req struct {
		UserID    uint   `json:"user_id" binding:"required"`
		TopicID   uint   `json:"topic_id" binding:"required"`
		CommentID uint   `json:"comment_id" binding:"required"`
		Quality   string `json:"quality"` // "low", "medium", "high"
		IsReply   bool   `json:"is_reply"` // True if this is a reply to another comment
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Note: In a production system, we would check if the user has exceeded
	// their daily points limit for discussion activities.
	// For now, we'll skip this check as we don't have that functionality yet.
	// TODO: Implement daily points limit check

	// Calculate points based on quality
	pointsToAward := h.config.ReplyPoints
	qualityMultiplier := 1.0

	switch req.Quality {
	case "high":
		qualityMultiplier = h.config.QualityMultiplier
	case "medium":
		qualityMultiplier = 1.2
	case "low":
		qualityMultiplier = 0.8
	}

	adjustedPoints := int(float64(pointsToAward) * qualityMultiplier)

	// Award points
	metadata := map[string]interface{}{
		"topic_id":   req.TopicID,
		"comment_id": req.CommentID,
		"action":     "posted_reply",
		"quality":    req.Quality,
		"is_reply":   req.IsReply,
		"timestamp":  time.Now().Unix(),
	}

	sourceType := models.PointSourceDiscussion
	transaction, err := h.pointsService.AwardPoints(req.UserID, adjustedPoints, sourceType, "comment", req.CommentID, "Forum reply", metadata)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award points"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Points awarded for forum reply",
		"points_awarded": adjustedPoints,
		"transaction_id": transaction.ID,
	})
}

// AwardUpvotePoints awards points to a user for receiving upvotes on their post
func (h *DiscussionPointsHandler) AwardUpvotePoints(c *gin.Context) {
	if !h.config.DiscussionPointsEnabled {
		c.JSON(http.StatusOK, gin.H{"message": "Discussion points are currently disabled", "points_awarded": 0})
		return
	}

	var req struct {
		UserID    uint `json:"user_id" binding:"required"`
		TopicID   uint `json:"topic_id"`
		CommentID uint `json:"comment_id"`
		Count     int  `json:"count" binding:"required,min=1"` // Number of upvotes received
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Calculate points based on upvote count
	pointsToAward := h.config.UpvoteReceivedPoints * req.Count

	// Award points
	metadata := map[string]interface{}{
		"topic_id":    req.TopicID,
		"comment_id":  req.CommentID,
		"action":      "received_upvotes",
		"upvote_count": req.Count,
		"timestamp":   time.Now().Unix(),
	}

	sourceType := models.PointSourceDiscussion
	// Use the comment ID if available, otherwise use the topic ID
	referenceType := "topic"
	referenceID := req.TopicID
	if req.CommentID > 0 {
		referenceType = "comment"
		referenceID = req.CommentID
	}
	
	transaction, err := h.pointsService.AwardPoints(req.UserID, pointsToAward, sourceType, referenceType, referenceID, "Upvotes received", metadata)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award points"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Points awarded for upvotes",
		"points_awarded": pointsToAward,
		"transaction_id": transaction.ID,
	})
}

// AwardFeaturedTopicPoints awards points to a user when their topic is featured
func (h *DiscussionPointsHandler) AwardFeaturedTopicPoints(c *gin.Context) {
	if !h.config.DiscussionPointsEnabled {
		c.JSON(http.StatusOK, gin.H{"message": "Discussion points are currently disabled", "points_awarded": 0})
		return
	}

	var req struct {
		UserID   uint   `json:"user_id" binding:"required"`
		TopicID  uint   `json:"topic_id" binding:"required"`
		Category string `json:"category"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Award points
	pointsToAward := h.config.FeaturedTopicPoints

	metadata := map[string]interface{}{
		"topic_id":  req.TopicID,
		"action":    "topic_featured",
		"category":  req.Category,
		"timestamp": time.Now().Unix(),
	}

	sourceType := models.PointSourceDiscussion
	transaction, err := h.pointsService.AwardPoints(req.UserID, pointsToAward, sourceType, "topic", req.TopicID, "Featured topic", metadata)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award points"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Points awarded for featured topic",
		"points_awarded": pointsToAward,
		"transaction_id": transaction.ID,
	})
}

// GetDiscussionPointsConfig returns the current discussion points configuration
func (h *DiscussionPointsHandler) GetDiscussionPointsConfig(c *gin.Context) {
	c.JSON(http.StatusOK, h.config)
}

// UpdateDiscussionPointsConfig updates the discussion points configuration
func (h *DiscussionPointsHandler) UpdateDiscussionPointsConfig(c *gin.Context) {
	// Check admin permissions
	if !isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin permissions required"})
		return
	}

	var config DiscussionPointsHandlerConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update the configuration
	h.config = config

	c.JSON(http.StatusOK, gin.H{
		"message": "Discussion points configuration updated",
		"config":  h.config,
	})
}

// Helper function to check if a user is an admin
func isAdmin(c *gin.Context) bool {
	// In a real implementation, this would check the JWT token claims
	// For now, just return true for testing
	return true
}