package models

import (
	"time"

	"gorm.io/gorm"
)

// PointSourceType represents different sources of points
type PointSourceType string

const (
	// PointSourceReading for points earned from reading
	PointSourceReading PointSourceType = "reading"
	
	// PointSourceDiscussion for points earned from discussion participation
	PointSourceDiscussion PointSourceType = "discussion"
	
	// PointSourceContent for points earned from content creation
	PointSourceContent PointSourceType = "content"
	
	// PointSourceSocial for points earned from social sharing
	PointSourceSocial PointSourceType = "social"
	
	// PointSourceQuality for bonus points for quality contributions
	PointSourceQuality PointSourceType = "quality"
	
	// PointSourceChallenge for points earned from challenges
	PointSourceChallenge PointSourceType = "challenge"
	
	// PointSourceEvent for points earned from special events
	PointSourceEvent PointSourceType = "event"
	
	// PointSourceGameStreak for points earned from maintaining activity streaks
	PointSourceGameStreak PointSourceType = "streak"
	
	// PointSourceAdmin for points manually awarded by administrators
	PointSourceAdmin PointSourceType = "admin"
	
	// PointSourceRedemption for points spent on redemptions
	PointSourceRedemption PointSourceType = "redemption"
	
	// PointSourceTransfer for points transferred between users
	PointSourceTransfer PointSourceType = "transfer"
	
	// PointSourceExpiration for points that expired
	PointSourceExpiration PointSourceType = "expiration"
)

// PointsTransaction represents a transaction in the points system
type PointsTransaction struct {
	gorm.Model
	UserID          uint            `json:"userId" gorm:"index"`
	Amount          int             `json:"amount"`
	Balance         int             `json:"balance"`
	SourceType      PointSourceType `json:"sourceType"`
	ReferenceType   string          `json:"referenceType"`
	ReferenceID     uint            `json:"referenceId"`
	Description     string          `json:"description"`
	ExpiresAt       *time.Time      `json:"expiresAt"`
	ExpiredAmount   *int            `json:"expiredAmount"`
	MetaData        string          `json:"metaData" gorm:"type:text"` // JSON string for additional data
	CreatedAt       time.Time       `json:"createdAt"`
}

// UserPointsBalance represents a user's current points balance
type UserPointsBalance struct {
	gorm.Model
	UserID            uint      `json:"userId" gorm:"uniqueIndex"`
	TotalPoints       int       `json:"totalPoints"`
	AvailablePoints   int       `json:"availablePoints"`
	PendingPoints     int       `json:"pendingPoints"`
	ExpiredPoints     int       `json:"expiredPoints"`
	RedeemedPoints    int       `json:"redeemedPoints"`
	TransferredPoints int       `json:"transferredPoints"`
	DailyReadingStreak int      `json:"dailyReadingStreak"`
	LastStreakDate    *time.Time `json:"lastStreakDate"`
	LastUpdated       time.Time `json:"lastUpdated"`
}

// MembershipTier represents different membership tiers based on points
type MembershipTier string

const (
	// TierBasic for new users (0+ points)
	TierBasic MembershipTier = "basic"
	
	// TierEngaged for engaged users (500+ points)
	TierEngaged MembershipTier = "engaged"
	
	// TierActive for active users (1500+ points)
	TierActive MembershipTier = "active"
	
	// TierPremium for premium users (paid subscription)
	TierPremium MembershipTier = "premium"
)

// UserMembership represents a user's membership tier information
type UserMembership struct {
	gorm.Model
	UserID           uint          `json:"userId" gorm:"uniqueIndex"`
	CurrentTier      MembershipTier `json:"currentTier"`
	PreviousTier     MembershipTier `json:"previousTier"`
	TierEffectiveDate time.Time    `json:"tierEffectiveDate"`
	LastEvaluatedAt  time.Time    `json:"lastEvaluatedAt"`
	TierChangeNotified bool        `json:"tierChangeNotified" gorm:"default:false"`
	IsPremium        bool          `json:"isPremium" gorm:"default:false"`
	PremiumExpiresAt *time.Time    `json:"premiumExpiresAt"`
}

// PointsExpirationRule represents rules for when points expire
type PointsExpirationRule struct {
	gorm.Model
	SourceType     PointSourceType `json:"sourceType"`
	DaysToExpire   int             `json:"daysToExpire"` // 0 means never expire
	IsActive       bool            `json:"isActive" gorm:"default:true"`
	CreatedBy      uint            `json:"createdBy"`
	LastModifiedBy uint            `json:"lastModifiedBy"`
}

// Achievement represents a user achievement or badge
type Achievement struct {
	gorm.Model
	Name               string    `json:"name"`
	Description        string    `json:"description"`
	Category           string    `json:"category"`
	ImageURL           string    `json:"imageURL"`
	PointsAwarded      int       `json:"pointsAwarded"`
	RequiredActions    string    `json:"requiredActions" gorm:"type:text"` // JSON string
	IsActive           bool      `json:"isActive" gorm:"default:true"`
	DisplayOrder       int       `json:"displayOrder"`
	CreatedBy          uint      `json:"createdBy"`
	LastModifiedBy     uint      `json:"lastModifiedBy"`
}

// UserAchievement represents an achievement earned by a user
type UserAchievement struct {
	gorm.Model
	UserID           uint      `json:"userId" gorm:"index"`
	AchievementID    uint      `json:"achievementId" gorm:"index"`
	Achievement      Achievement `json:"achievement" gorm:"foreignKey:AchievementID"`
	AwardedAt        time.Time `json:"awardedAt"`
	PointsAwarded    int       `json:"pointsAwarded"`
	NotificationSent bool      `json:"notificationSent" gorm:"default:false"`
	IsFeatured       bool      `json:"isFeatured" gorm:"default:false"`
}

// PointsEventType represents types of point events
type PointsEventType string

const (
	// EventTypeDoublePoints for double points events
	EventTypeDoublePoints PointsEventType = "double_points"
	
	// EventTypeBonusChallenge for bonus challenge events
	EventTypeBonusChallenge PointsEventType = "bonus_challenge"
	
	// EventTypeStreakMultiplier for streak multiplier events
	EventTypeStreakMultiplier PointsEventType = "streak_multiplier"
	
	// EventTypeSpecialActivity for special activity events
	EventTypeSpecialActivity PointsEventType = "special_activity"
)

// PointsEvent represents a special event with bonus points
type PointsEvent struct {
	gorm.Model
	Name              string         `json:"name"`
	Description       string         `json:"description"`
	EventType         PointsEventType `json:"eventType"`
	StartDate         time.Time      `json:"startDate"`
	EndDate           time.Time      `json:"endDate"`
	Multiplier        float64        `json:"multiplier" gorm:"default:1"`
	ApplicableSources string         `json:"applicableSources" gorm:"type:text"` // JSON array of source types
	IsActive          bool           `json:"isActive" gorm:"default:true"`
	CreatedBy         uint           `json:"createdBy"`
	LastModifiedBy    uint           `json:"lastModifiedBy"`
}

// PointsTransferRecord represents a transfer of points between users
type PointsTransferRecord struct {
	gorm.Model
	FromUserID     uint      `json:"fromUserId" gorm:"index"`
	ToUserID       uint      `json:"toUserId" gorm:"index"`
	Amount         int       `json:"amount"`
	Notes          string    `json:"notes"`
	TransactionID  uint      `json:"transactionId"` // Reference to PointsTransaction
	Status         string    `json:"status"` // e.g., "completed", "declined", "pending"
	CompletedAt    *time.Time `json:"completedAt"`
	CreatedAt      time.Time `json:"createdAt"`
}

// PointsRedemptionItem represents an item that can be redeemed with points
type PointsRedemptionItem struct {
	gorm.Model
	Name            string    `json:"name"`
	Description     string    `json:"description"`
	PointsCost      int       `json:"pointsCost"`
	Category        string    `json:"category"`
	ImageURL        string    `json:"imageURL"`
	IsDigital       bool      `json:"isDigital" gorm:"default:true"`
	IsActive        bool      `json:"isActive" gorm:"default:true"`
	QuantityAvailable int     `json:"quantityAvailable"`
	RedemptionCode  string    `json:"redemptionCode"`
	CreatedBy       uint      `json:"createdBy"`
	LastModifiedBy  uint      `json:"lastModifiedBy"`
}

// PointsRedemptionRecord represents a redemption of points for an item
type PointsRedemptionRecord struct {
	gorm.Model
	UserID          uint      `json:"userId" gorm:"index"`
	ItemID          uint      `json:"itemId" gorm:"index"`
	PointsSpent     int       `json:"pointsSpent"`
	TransactionID   uint      `json:"transactionId"` // Reference to PointsTransaction
	RedemptionCode  string    `json:"redemptionCode"`
	Status          string    `json:"status"` // e.g., "completed", "processing", "delivered"
	DeliveryAddress string    `json:"deliveryAddress"`
	DeliveryDate    *time.Time `json:"deliveryDate"`
	Notes           string    `json:"notes"`
	CreatedAt       time.Time `json:"createdAt"`
}

// PointsChallenge represents a challenge that awards points on completion
type PointsChallenge struct {
	gorm.Model
	Name            string    `json:"name"`
	Description     string    `json:"description"`
	PointsAwarded   int       `json:"pointsAwarded"`
	RequiredActions string    `json:"requiredActions" gorm:"type:text"`
	StartDate       time.Time `json:"startDate"`
	EndDate         time.Time `json:"endDate"`
	IsActive        bool      `json:"isActive" gorm:"default:true"`
	MaxCompletions  int       `json:"maxCompletions" gorm:"default:1"`
	Category        string    `json:"category"`
	Difficulty      string    `json:"difficulty"`
	CreatedBy       uint      `json:"createdBy"`
	LastModifiedBy  uint      `json:"lastModifiedBy"`
}

// UserChallenge represents a user's progress on a challenge
type UserChallenge struct {
	gorm.Model
	UserID         uint      `json:"userId" gorm:"index"`
	ChallengeID    uint      `json:"challengeId" gorm:"index"`
	Progress       float64   `json:"progress" gorm:"default:0"` // 0-1 for percentage complete
	CompletedAt    *time.Time `json:"completedAt"`
	TimesCompleted int       `json:"timesCompleted" gorm:"default:0"`
	PointsAwarded  int       `json:"pointsAwarded" gorm:"default:0"`
	LastUpdated    time.Time `json:"lastUpdated"`
}