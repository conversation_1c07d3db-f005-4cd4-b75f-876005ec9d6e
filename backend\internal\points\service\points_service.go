package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/greatnigeria/internal/points/models"
	"github.com/greatnigeria/internal/points/repository"
)

// PointsService defines the interface for points operations
type PointsService interface {
	// Basic points operations
	AwardPoints(userID uint, amount int, sourceType models.PointSourceType, referenceType string, referenceID uint, description string, metaData map[string]interface{}) (*models.PointsTransaction, error)
	DeductPoints(userID uint, amount int, sourceType models.PointSourceType, referenceType string, referenceID uint, description string, metaData map[string]interface{}) (*models.PointsTransaction, error)
	GetUserPointsBalance(userID uint) (*models.UserPointsBalance, error)
	GetPointsHistory(userID uint, page, pageSize int) ([]models.PointsTransaction, int64, error)
	GetPointsTransactionsByType(userID uint, sourceType models.PointSourceType, page, pageSize int) ([]models.PointsTransaction, error)
	
	// Membership tier management
	GetUserMembership(userID uint) (*models.UserMembership, error)
	EvaluateUserTier(userID uint) (models.MembershipTier, bool, error)
	GetTierRequirements() map[models.MembershipTier]int
	GetUsersByTier(tier models.MembershipTier) ([]models.UserMembership, error)
	BulkEvaluateUserTiers() (int, error)
	SetPremiumMembership(userID uint, isPremium bool, expiryDate *time.Time) error
	
	// Points expiration
	SetExpirationRule(sourceType models.PointSourceType, daysToExpire int, userID uint) (*models.PointsExpirationRule, error)
	GetExpirationRules() ([]models.PointsExpirationRule, error)
	ProcessExpiringPoints() (int, error)
	DisableExpirationRule(ruleID uint, userID uint) error
	
	// Achievement system
	CreateAchievement(name, description, category, imageURL string, pointsAwarded int, requiredActions map[string]interface{}, displayOrder int, userID uint) (*models.Achievement, error)
	GetAchievements(activeOnly bool) ([]models.Achievement, error)
	AwardAchievement(userID, achievementID uint) (*models.UserAchievement, error)
	GetUserAchievements(userID uint) ([]models.UserAchievement, error)
	CheckEligibleAchievements(userID uint) ([]models.Achievement, error)
	MarkAchievementAsFeatured(userAchievementID uint, isFeatured bool) error
	MarkAchievementNotified(userAchievementID uint) error
	UpdateAchievement(id uint, name, description, category, imageURL string, pointsAwarded int, requiredActions map[string]interface{}, isActive bool, displayOrder int, userID uint) (*models.Achievement, error)
	GetUnnotifiedAchievements() ([]models.UserAchievement, error)
	
	// Points events
	CreatePointsEvent(name, description string, eventType models.PointsEventType, startDate, endDate time.Time, multiplier float64, applicableSources []models.PointSourceType, userID uint) (*models.PointsEvent, error)
	GetActivePointsEvents() ([]models.PointsEvent, error)
	GetPointsEventByID(id uint) (*models.PointsEvent, error)
	UpdatePointsEvent(id uint, name, description string, eventType models.PointsEventType, startDate, endDate time.Time, multiplier float64, applicableSources []models.PointSourceType, isActive bool, userID uint) (*models.PointsEvent, error)
	GetPointsMultiplier(sourceType models.PointSourceType) float64
	
	// Points transfers
	TransferPoints(fromUserID, toUserID uint, amount int, notes string) (*models.PointsTransferRecord, error)
	GetTransfersByUser(userID uint, isReceiver bool, page, pageSize int) ([]models.PointsTransferRecord, error)
	
	// Redemption system
	CreateRedemptionItem(name, description, category, imageURL string, pointsCost int, isDigital bool, quantityAvailable int, redemptionCode string, userID uint) (*models.PointsRedemptionItem, error)
	GetRedemptionItems(activeOnly bool) ([]models.PointsRedemptionItem, error)
	RedeemPoints(userID, itemID uint, deliveryAddress string, notes string) (*models.PointsRedemptionRecord, error)
	GetUserRedemptionHistory(userID uint, page, pageSize int) ([]models.PointsRedemptionRecord, error)
	UpdateRedemptionItemStatus(itemID uint, isActive bool, userID uint) error
	UpdateRedemptionStatus(redemptionID uint, status string, userID uint) error
	
	// Challenge system
	CreateChallenge(name, description string, pointsAwarded int, requiredActions map[string]interface{}, startDate, endDate time.Time, maxCompletions int, category, difficulty string, userID uint) (*models.PointsChallenge, error)
	GetActiveChallenges() ([]models.PointsChallenge, error)
	GetChallengeByID(id uint) (*models.PointsChallenge, error)
	UpdateChallengeProgress(userID, challengeID uint, progress float64) error
	CompleteChallenge(userID, challengeID uint) error
	GetUserChallenges(userID uint) ([]models.UserChallenge, error)
	
	// Reading points
	AwardReadingPoints(userID, sectionID uint, metadata map[string]interface{}) (*models.PointsTransaction, error)
	
	// Discussion points
	AwardDiscussionPoints(userID uint, discussionType string, discussionID uint, metadata map[string]interface{}) (*models.PointsTransaction, error)
	
	// Content creation points
	AwardContentCreationPoints(userID uint, contentType string, contentID uint, quality float64, metadata map[string]interface{}) (*models.PointsTransaction, error)
	
	// Social sharing points
	AwardSocialSharingPoints(userID uint, platform string, contentType string, contentID uint, metadata map[string]interface{}) (*models.PointsTransaction, error)
	
	// Game mechanics
	UpdateStreak(userID uint, activityType string) (int, error)
	ResetStreak(userID uint) error
	
	// Leaderboards
	GetGlobalLeaderboard(page, pageSize int) ([]models.UserPointsBalance, error)
	GetCategoryLeaderboard(category string, page, pageSize int) ([]struct {
		UserID uint
		Points int
	}, error)
	GetTimeRangeLeaderboard(period string, page, pageSize int) ([]struct {
		UserID uint
		Points int
	}, error)
}

// PointsServiceImpl implements the PointsService interface
type PointsServiceImpl struct {
	pointsRepo repository.PointsRepository
}

// NewPointsService creates a new points service
func NewPointsService(pointsRepo repository.PointsRepository) PointsService {
	return &PointsServiceImpl{
		pointsRepo: pointsRepo,
	}
}

// AwardPoints awards points to a user
func (s *PointsServiceImpl) AwardPoints(
	userID uint,
	amount int,
	sourceType models.PointSourceType,
	referenceType string,
	referenceID uint,
	description string,
	metaData map[string]interface{},
) (*models.PointsTransaction, error) {
	if amount <= 0 {
		return nil, errors.New("award amount must be positive")
	}
	
	// Convert metadata to JSON
	metaDataJSON, err := json.Marshal(metaData)
	if err != nil {
		return nil, fmt.Errorf("error marshaling metadata: %w", err)
	}
	
	// Apply any active multipliers
	multiplier := s.GetPointsMultiplier(sourceType)
	adjustedAmount := int(float64(amount) * multiplier)
	
	// Get user's current balance
	balance, err := s.GetUserPointsBalance(userID)
	if err != nil {
		return nil, fmt.Errorf("error getting user balance: %w", err)
	}
	
	// If user doesn't have a balance yet, create one
	if balance == nil {
		balance = &models.UserPointsBalance{
			UserID:          userID,
			TotalPoints:     0,
			AvailablePoints: 0,
			PendingPoints:   0,
			ExpiredPoints:   0,
			RedeemedPoints:  0,
			LastUpdated:     time.Now(),
		}
		
		if err := s.pointsRepo.CreateUserPointsBalance(balance); err != nil {
			return nil, fmt.Errorf("error creating user balance: %w", err)
		}
	}
	
	// Check for expiration rule
	var expiresAt *time.Time
	rule, err := s.pointsRepo.GetExpirationRuleBySourceType(sourceType)
	if err != nil {
		return nil, fmt.Errorf("error getting expiration rule: %w", err)
	}
	
	if rule != nil && rule.DaysToExpire > 0 {
		expires := time.Now().AddDate(0, 0, rule.DaysToExpire)
		expiresAt = &expires
	}
	
	// Create the transaction
	newBalance := balance.AvailablePoints + adjustedAmount
	transaction := &models.PointsTransaction{
		UserID:        userID,
		Amount:        adjustedAmount,
		Balance:       newBalance,
		SourceType:    sourceType,
		ReferenceType: referenceType,
		ReferenceID:   referenceID,
		Description:   description,
		ExpiresAt:     expiresAt,
		MetaData:      string(metaDataJSON),
		CreatedAt:     time.Now(),
	}
	
	if err := s.pointsRepo.CreateTransaction(transaction); err != nil {
		return nil, fmt.Errorf("error creating transaction: %w", err)
	}
	
	// Update user's balance
	balance.TotalPoints += adjustedAmount
	balance.AvailablePoints += adjustedAmount
	balance.LastUpdated = time.Now()
	
	if err := s.pointsRepo.UpdateUserPointsBalance(balance); err != nil {
		return nil, fmt.Errorf("error updating user balance: %w", err)
	}
	
	// Evaluate if user's tier has changed
	if _, _, err := s.EvaluateUserTier(userID); err != nil {
		// Log error but continue
		fmt.Printf("Error evaluating user tier: %v\n", err)
	}
	
	return transaction, nil
}

// DeductPoints deducts points from a user
func (s *PointsServiceImpl) DeductPoints(
	userID uint,
	amount int,
	sourceType models.PointSourceType,
	referenceType string,
	referenceID uint,
	description string,
	metaData map[string]interface{},
) (*models.PointsTransaction, error) {
	if amount <= 0 {
		return nil, errors.New("deduction amount must be positive")
	}
	
	// Convert metadata to JSON
	metaDataJSON, err := json.Marshal(metaData)
	if err != nil {
		return nil, fmt.Errorf("error marshaling metadata: %w", err)
	}
	
	// Get user's current balance
	balance, err := s.GetUserPointsBalance(userID)
	if err != nil {
		return nil, fmt.Errorf("error getting user balance: %w", err)
	}
	
	if balance == nil || balance.AvailablePoints < amount {
		return nil, errors.New("insufficient points available")
	}
	
	// Create the transaction
	newBalance := balance.AvailablePoints - amount
	transaction := &models.PointsTransaction{
		UserID:        userID,
		Amount:        -amount, // Negative for deduction
		Balance:       newBalance,
		SourceType:    sourceType,
		ReferenceType: referenceType,
		ReferenceID:   referenceID,
		Description:   description,
		MetaData:      string(metaDataJSON),
		CreatedAt:     time.Now(),
	}
	
	if err := s.pointsRepo.CreateTransaction(transaction); err != nil {
		return nil, fmt.Errorf("error creating transaction: %w", err)
	}
	
	// Update user's balance
	balance.AvailablePoints -= amount
	
	// Update other fields based on source type
	if sourceType == models.PointSourceRedemption {
		balance.RedeemedPoints += amount
	} else if sourceType == models.PointSourceTransfer {
		balance.TransferredPoints += amount
	}
	
	balance.LastUpdated = time.Now()
	
	if err := s.pointsRepo.UpdateUserPointsBalance(balance); err != nil {
		return nil, fmt.Errorf("error updating user balance: %w", err)
	}
	
	return transaction, nil
}

// GetUserPointsBalance retrieves the points balance for a user
func (s *PointsServiceImpl) GetUserPointsBalance(userID uint) (*models.UserPointsBalance, error) {
	return s.pointsRepo.GetUserPointsBalance(userID)
}

// GetPointsHistory retrieves the points transaction history for a user
func (s *PointsServiceImpl) GetPointsHistory(userID uint, page, pageSize int) ([]models.PointsTransaction, int64, error) {
	// Ensure valid pagination
	if page < 1 {
		page = 1
	}
	
	if pageSize < 1 {
		pageSize = 10
	}
	
	offset := (page - 1) * pageSize
	
	// Get transactions
	transactions, err := s.pointsRepo.GetTransactionsByUser(userID, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("error getting transactions: %w", err)
	}
	
	// Get total count
	total, err := s.pointsRepo.GetTotalTransactions(userID)
	if err != nil {
		return nil, 0, fmt.Errorf("error getting total transactions: %w", err)
	}
	
	return transactions, total, nil
}

// GetPointsTransactionsByType retrieves the points transactions of a specific type for a user
func (s *PointsServiceImpl) GetPointsTransactionsByType(userID uint, sourceType models.PointSourceType, page, pageSize int) ([]models.PointsTransaction, error) {
	// Ensure valid pagination
	if page < 1 {
		page = 1
	}
	
	if pageSize < 1 {
		pageSize = 10
	}
	
	offset := (page - 1) * pageSize
	
	return s.pointsRepo.GetTransactionsByUserAndType(userID, sourceType, pageSize, offset)
}

// GetUserMembership retrieves the membership for a user
func (s *PointsServiceImpl) GetUserMembership(userID uint) (*models.UserMembership, error) {
	membership, err := s.pointsRepo.GetUserMembership(userID)
	if err != nil {
		return nil, fmt.Errorf("error getting user membership: %w", err)
	}
	
	// If user doesn't have a membership yet, create one with basic tier
	if membership == nil {
		membership = &models.UserMembership{
			UserID:            userID,
			CurrentTier:       models.TierBasic,
			PreviousTier:      models.TierBasic,
			TierEffectiveDate: time.Now(),
			LastEvaluatedAt:   time.Now(),
			IsPremium:         false,
		}
		
		if err := s.pointsRepo.CreateUserMembership(membership); err != nil {
			return nil, fmt.Errorf("error creating user membership: %w", err)
		}
	}
	
	return membership, nil
}

// EvaluateUserTier evaluates and updates a user's membership tier
func (s *PointsServiceImpl) EvaluateUserTier(userID uint) (models.MembershipTier, bool, error) {
	// Get current balance
	balance, err := s.GetUserPointsBalance(userID)
	if err != nil {
		return "", false, fmt.Errorf("error getting user balance: %w", err)
	}
	
	if balance == nil {
		return "", false, errors.New("user has no points balance")
	}
	
	// Get current membership
	membership, err := s.GetUserMembership(userID)
	if err != nil {
		return "", false, fmt.Errorf("error getting user membership: %w", err)
	}
	
	// Calculate new tier based on points
	requirements := s.GetTierRequirements()
	var newTier models.MembershipTier
	
	// Start with basic tier
	newTier = models.TierBasic
	
	// Check if user meets higher tier requirements
	if balance.TotalPoints >= requirements[models.TierActive] {
		newTier = models.TierActive
	} else if balance.TotalPoints >= requirements[models.TierEngaged] {
		newTier = models.TierEngaged
	}
	
	// Preserve premium status regardless of points
	if membership.IsPremium {
		newTier = models.TierPremium
	}
	
	// Check if tier changed
	tierChanged := membership.CurrentTier != newTier
	
	// Update membership if tier changed
	if tierChanged {
		membership.PreviousTier = membership.CurrentTier
		membership.CurrentTier = newTier
		membership.TierEffectiveDate = time.Now()
		membership.TierChangeNotified = false
	}
	
	// Always update evaluation timestamp
	membership.LastEvaluatedAt = time.Now()
	
	if err := s.pointsRepo.UpdateUserMembership(membership); err != nil {
		return newTier, tierChanged, fmt.Errorf("error updating user membership: %w", err)
	}
	
	return newTier, tierChanged, nil
}

// GetTierRequirements returns the points requirements for each tier
func (s *PointsServiceImpl) GetTierRequirements() map[models.MembershipTier]int {
	return map[models.MembershipTier]int{
		models.TierBasic:   0,
		models.TierEngaged: 500,
		models.TierActive:  1500,
		models.TierPremium: 0, // Premium is based on subscription, not points
	}
}

// GetUsersByTier retrieves users in a specific membership tier
func (s *PointsServiceImpl) GetUsersByTier(tier models.MembershipTier) ([]models.UserMembership, error) {
	return s.pointsRepo.GetUsersByTier(tier)
}

// BulkEvaluateUserTiers evaluates tiers for users who might need updates
func (s *PointsServiceImpl) BulkEvaluateUserTiers() (int, error) {
	// Get users who might need tier evaluation (have sufficient points for tier changes)
	threshold := s.GetTierRequirements()[models.TierEngaged] // Lowest tier threshold
	users, err := s.pointsRepo.GetUsersForTierEvaluation(threshold)
	if err != nil {
		return 0, fmt.Errorf("error getting users for tier evaluation: %w", err)
	}
	
	changedCount := 0
	for _, user := range users {
		_, changed, err := s.EvaluateUserTier(user.UserID)
		if err != nil {
			// Log error but continue with other users
			fmt.Printf("Error evaluating tier for user %d: %v\n", user.UserID, err)
			continue
		}
		
		if changed {
			changedCount++
		}
	}
	
	return changedCount, nil
}

// SetPremiumMembership sets or updates a user's premium membership status
func (s *PointsServiceImpl) SetPremiumMembership(userID uint, isPremium bool, expiryDate *time.Time) error {
	// Get current membership
	membership, err := s.GetUserMembership(userID)
	if err != nil {
		return fmt.Errorf("error getting user membership: %w", err)
	}
	
	// Update premium status
	membership.IsPremium = isPremium
	membership.PremiumExpiresAt = expiryDate
	
	// Update tier if necessary
	if isPremium {
		membership.PreviousTier = membership.CurrentTier
		membership.CurrentTier = models.TierPremium
		membership.TierEffectiveDate = time.Now()
		membership.TierChangeNotified = false
	} else if membership.CurrentTier == models.TierPremium {
		// If removing premium, need to recalculate tier based on points
		balance, err := s.GetUserPointsBalance(userID)
		if err != nil {
			return fmt.Errorf("error getting user balance: %w", err)
		}
		
		requirements := s.GetTierRequirements()
		
		if balance.TotalPoints >= requirements[models.TierActive] {
			membership.CurrentTier = models.TierActive
		} else if balance.TotalPoints >= requirements[models.TierEngaged] {
			membership.CurrentTier = models.TierEngaged
		} else {
			membership.CurrentTier = models.TierBasic
		}
		
		membership.TierEffectiveDate = time.Now()
		membership.TierChangeNotified = false
	}
	
	if err := s.pointsRepo.UpdateUserMembership(membership); err != nil {
		return fmt.Errorf("error updating user membership: %w", err)
	}
	
	return nil
}

// SetExpirationRule sets or updates an expiration rule for a points source type
func (s *PointsServiceImpl) SetExpirationRule(sourceType models.PointSourceType, daysToExpire int, userID uint) (*models.PointsExpirationRule, error) {
	if daysToExpire < 0 {
		return nil, errors.New("days to expire must be non-negative")
	}
	
	// Check if rule already exists
	existingRule, err := s.pointsRepo.GetExpirationRuleBySourceType(sourceType)
	if err != nil {
		return nil, fmt.Errorf("error checking for existing rule: %w", err)
	}
	
	if existingRule != nil {
		// Update existing rule
		existingRule.DaysToExpire = daysToExpire
		existingRule.LastModifiedBy = userID
		existingRule.IsActive = true
		
		if err := s.pointsRepo.UpdateExpirationRule(existingRule); err != nil {
			return nil, fmt.Errorf("error updating expiration rule: %w", err)
		}
		
		return existingRule, nil
	}
	
	// Create new rule
	newRule := &models.PointsExpirationRule{
		SourceType:     sourceType,
		DaysToExpire:   daysToExpire,
		IsActive:       true,
		CreatedBy:      userID,
		LastModifiedBy: userID,
	}
	
	if err := s.pointsRepo.CreateExpirationRule(newRule); err != nil {
		return nil, fmt.Errorf("error creating expiration rule: %w", err)
	}
	
	return newRule, nil
}

// GetExpirationRules retrieves all expiration rules
func (s *PointsServiceImpl) GetExpirationRules() ([]models.PointsExpirationRule, error) {
	return s.pointsRepo.GetExpirationRules()
}

// ProcessExpiringPoints processes points that are expiring
func (s *PointsServiceImpl) ProcessExpiringPoints() (int, error) {
	// Get transactions that are expiring
	now := time.Now()
	expiringTransactions, err := s.pointsRepo.GetExpiringTransactions(now)
	if err != nil {
		return 0, fmt.Errorf("error getting expiring transactions: %w", err)
	}
	
	processedCount := 0
	for _, transaction := range expiringTransactions {
		// Skip transactions with negative amounts (deductions)
		if transaction.Amount <= 0 {
			continue
		}
		
		// Skip transactions that have already expired
		if transaction.ExpiredAmount != nil && *transaction.ExpiredAmount > 0 {
			continue
		}
		
		// Get user's balance
		userID := transaction.UserID
		balance, err := s.GetUserPointsBalance(userID)
		if err != nil {
			fmt.Printf("Error getting balance for user %d: %v\n", userID, err)
			continue
		}
		
		if balance == nil {
			fmt.Printf("Balance not found for user %d\n", userID)
			continue
		}
		
		// Create expiration transaction
		expirationAmount := transaction.Amount
		expiredAmount := expirationAmount
		transaction.ExpiredAmount = &expiredAmount
		
		// Update the original transaction to mark as expired
		if err := s.pointsRepo.CreateTransaction(&transaction); err != nil {
			fmt.Printf("Error updating expired transaction %d: %v\n", transaction.ID, err)
			continue
		}
		
		// Create a new transaction for the expiration
		metaData := map[string]interface{}{
			"originalTransactionId": transaction.ID,
			"expirationDate":        now,
		}
		
		metaDataJSON, _ := json.Marshal(metaData)
		
		expirationTransaction := &models.PointsTransaction{
			UserID:        userID,
			Amount:        -expirationAmount, // Negative amount for expiration
			Balance:       balance.AvailablePoints - expirationAmount,
			SourceType:    models.PointSourceExpiration,
			ReferenceType: "expiration",
			ReferenceID:   transaction.ID,
			Description:   fmt.Sprintf("Points expired from %s source", transaction.SourceType),
			MetaData:      string(metaDataJSON),
			CreatedAt:     now,
		}
		
		if err := s.pointsRepo.CreateTransaction(expirationTransaction); err != nil {
			fmt.Printf("Error creating expiration transaction for user %d: %v\n", userID, err)
			continue
		}
		
		// Update user's balance
		balance.AvailablePoints -= expirationAmount
		balance.ExpiredPoints += expirationAmount
		balance.LastUpdated = now
		
		if err := s.pointsRepo.UpdateUserPointsBalance(balance); err != nil {
			fmt.Printf("Error updating balance for user %d after expiration: %v\n", userID, err)
			continue
		}
		
		processedCount++
	}
	
	return processedCount, nil
}

// DisableExpirationRule disables an expiration rule
func (s *PointsServiceImpl) DisableExpirationRule(ruleID uint, userID uint) error {
	// Check if rule exists
	rules, err := s.pointsRepo.GetExpirationRules()
	if err != nil {
		return fmt.Errorf("error getting expiration rules: %w", err)
	}
	
	var rule *models.PointsExpirationRule
	for _, r := range rules {
		if r.ID == ruleID {
			rule = &r
			break
		}
	}
	
	if rule == nil {
		return errors.New("expiration rule not found")
	}
	
	// Update rule
	rule.IsActive = false
	rule.LastModifiedBy = userID
	
	if err := s.pointsRepo.UpdateExpirationRule(rule); err != nil {
		return fmt.Errorf("error updating expiration rule: %w", err)
	}
	
	return nil
}

// CreateAchievement creates a new achievement
func (s *PointsServiceImpl) CreateAchievement(
	name, description, category, imageURL string,
	pointsAwarded int,
	requiredActions map[string]interface{},
	displayOrder int,
	userID uint,
) (*models.Achievement, error) {
	// Convert required actions to JSON
	requiredActionsJSON, err := json.Marshal(requiredActions)
	if err != nil {
		return nil, fmt.Errorf("error marshaling required actions: %w", err)
	}
	
	// Create achievement
	achievement := &models.Achievement{
		Name:            name,
		Description:     description,
		Category:        category,
		ImageURL:        imageURL,
		PointsAwarded:   pointsAwarded,
		RequiredActions: string(requiredActionsJSON),
		IsActive:        true,
		DisplayOrder:    displayOrder,
		CreatedBy:       userID,
		LastModifiedBy:  userID,
	}
	
	if err := s.pointsRepo.CreateAchievement(achievement); err != nil {
		return nil, fmt.Errorf("error creating achievement: %w", err)
	}
	
	return achievement, nil
}

// GetAchievements retrieves all achievements
func (s *PointsServiceImpl) GetAchievements(activeOnly bool) ([]models.Achievement, error) {
	return s.pointsRepo.GetAchievements(activeOnly)
}

// AwardAchievement awards an achievement to a user
func (s *PointsServiceImpl) AwardAchievement(userID, achievementID uint) (*models.UserAchievement, error) {
	// Get achievement details
	achievement, err := s.pointsRepo.GetAchievementByID(achievementID)
	if err != nil {
		return nil, fmt.Errorf("error getting achievement: %w", err)
	}
	
	if achievement == nil {
		return nil, errors.New("achievement not found")
	}
	
	// Check if user already has this achievement
	userAchievements, err := s.pointsRepo.GetUserAchievements(userID)
	if err != nil {
		return nil, fmt.Errorf("error getting user achievements: %w", err)
	}
	
	for _, ua := range userAchievements {
		if ua.AchievementID == achievementID {
			// Already awarded
			return &ua, nil
		}
	}
	
	// Award achievement
	now := time.Now()
	userAchievement := &models.UserAchievement{
		UserID:        userID,
		AchievementID: achievementID,
		Achievement:   *achievement,
		AwardedAt:     now,
		PointsAwarded: achievement.PointsAwarded,
	}
	
	if err := s.pointsRepo.CreateUserAchievement(userAchievement); err != nil {
		return nil, fmt.Errorf("error creating user achievement: %w", err)
	}
	
	// Award points
	if achievement.PointsAwarded > 0 {
		metaData := map[string]interface{}{
			"achievementId": achievementID,
			"name":          achievement.Name,
		}
		
		_, err := s.AwardPoints(
			userID,
			achievement.PointsAwarded,
			models.PointSourceChallenge,
			"achievement",
			achievementID,
			fmt.Sprintf("Achievement unlocked: %s", achievement.Name),
			metaData,
		)
		
		if err != nil {
			// Log error but continue
			fmt.Printf("Error awarding points for achievement: %v\n", err)
		}
	}
	
	return userAchievement, nil
}

// GetUserAchievements retrieves all achievements for a user
func (s *PointsServiceImpl) GetUserAchievements(userID uint) ([]models.UserAchievement, error) {
	return s.pointsRepo.GetUserAchievements(userID)
}

// CheckEligibleAchievements checks which achievements a user is eligible for but hasn't earned yet
func (s *PointsServiceImpl) CheckEligibleAchievements(userID uint) ([]models.Achievement, error) {
	// This is a simplified implementation.
	// In a real-world scenario, this would involve complex logic to check against achievement criteria.
	
	// Get all active achievements
	allAchievements, err := s.pointsRepo.GetAchievements(true)
	if err != nil {
		return nil, fmt.Errorf("error getting achievements: %w", err)
	}
	
	// Get user's achievements
	userAchievements, err := s.pointsRepo.GetUserAchievements(userID)
	if err != nil {
		return nil, fmt.Errorf("error getting user achievements: %w", err)
	}
	
	// Create a map of achievement IDs that user already has
	earnedAchievements := make(map[uint]bool)
	for _, ua := range userAchievements {
		earnedAchievements[ua.AchievementID] = true
	}
	
	// Filter out achievements user already has
	var eligibleAchievements []models.Achievement
	for _, achievement := range allAchievements {
		if !earnedAchievements[achievement.ID] {
			eligibleAchievements = append(eligibleAchievements, achievement)
		}
	}
	
	return eligibleAchievements, nil
}

// MarkAchievementAsFeatured marks a user's achievement as featured
func (s *PointsServiceImpl) MarkAchievementAsFeatured(userAchievementID uint, isFeatured bool) error {
	// Get the user achievement
	userAchievement, err := s.pointsRepo.GetUserAchievementByID(userAchievementID)
	if err != nil {
		return fmt.Errorf("error getting user achievement: %w", err)
	}
	
	if userAchievement == nil {
		return errors.New("user achievement not found")
	}
	
	// Update the featured status
	userAchievement.IsFeatured = isFeatured
	
	if err := s.pointsRepo.UpdateUserAchievement(userAchievement); err != nil {
		return fmt.Errorf("error updating user achievement: %w", err)
	}
	
	return nil
}

// MarkAchievementNotified marks a user's achievement as notified
func (s *PointsServiceImpl) MarkAchievementNotified(userAchievementID uint) error {
	// Get the user achievement
	userAchievement, err := s.pointsRepo.GetUserAchievementByID(userAchievementID)
	if err != nil {
		return fmt.Errorf("error getting user achievement: %w", err)
	}
	
	if userAchievement == nil {
		return errors.New("user achievement not found")
	}
	
	// Mark as notified
	userAchievement.NotificationSent = true
	
	if err := s.pointsRepo.UpdateUserAchievement(userAchievement); err != nil {
		return fmt.Errorf("error updating user achievement: %w", err)
	}
	
	return nil
}

// UpdateAchievement updates an achievement
func (s *PointsServiceImpl) UpdateAchievement(
	id uint,
	name, description, category, imageURL string,
	pointsAwarded int,
	requiredActions map[string]interface{},
	isActive bool,
	displayOrder int,
	userID uint,
) (*models.Achievement, error) {
	// Get existing achievement
	achievement, err := s.pointsRepo.GetAchievementByID(id)
	if err != nil {
		return nil, fmt.Errorf("error getting achievement: %w", err)
	}
	
	if achievement == nil {
		return nil, errors.New("achievement not found")
	}
	
	// Convert required actions to JSON
	requiredActionsJSON, err := json.Marshal(requiredActions)
	if err != nil {
		return nil, fmt.Errorf("error marshaling required actions: %w", err)
	}
	
	// Update achievement
	achievement.Name = name
	achievement.Description = description
	achievement.Category = category
	achievement.ImageURL = imageURL
	achievement.PointsAwarded = pointsAwarded
	achievement.RequiredActions = string(requiredActionsJSON)
	achievement.IsActive = isActive
	achievement.DisplayOrder = displayOrder
	achievement.LastModifiedBy = userID
	
	if err := s.pointsRepo.UpdateAchievement(achievement); err != nil {
		return nil, fmt.Errorf("error updating achievement: %w", err)
	}
	
	return achievement, nil
}

// GetUnnotifiedAchievements retrieves user achievements that have not been notified
func (s *PointsServiceImpl) GetUnnotifiedAchievements() ([]models.UserAchievement, error) {
	return s.pointsRepo.GetUnnotifiedAchievements()
}

// CreatePointsEvent creates a new points event
func (s *PointsServiceImpl) CreatePointsEvent(
	name, description string,
	eventType models.PointsEventType,
	startDate, endDate time.Time,
	multiplier float64,
	applicableSources []models.PointSourceType,
	userID uint,
) (*models.PointsEvent, error) {
	// Validate inputs
	if name == "" {
		return nil, errors.New("event name is required")
	}
	
	if multiplier <= 0 {
		return nil, errors.New("multiplier must be positive")
	}
	
	if endDate.Before(startDate) {
		return nil, errors.New("end date must be after start date")
	}
	
	// Convert applicable sources to JSON
	applicableSourcesJSON, err := json.Marshal(applicableSources)
	if err != nil {
		return nil, fmt.Errorf("error marshaling applicable sources: %w", err)
	}
	
	// Create event
	event := &models.PointsEvent{
		Name:              name,
		Description:       description,
		EventType:         eventType,
		StartDate:         startDate,
		EndDate:           endDate,
		Multiplier:        multiplier,
		ApplicableSources: string(applicableSourcesJSON),
		IsActive:          true,
		CreatedBy:         userID,
		LastModifiedBy:    userID,
	}
	
	if err := s.pointsRepo.CreatePointsEvent(event); err != nil {
		return nil, fmt.Errorf("error creating points event: %w", err)
	}
	
	return event, nil
}

// GetActivePointsEvents retrieves active points events
func (s *PointsServiceImpl) GetActivePointsEvents() ([]models.PointsEvent, error) {
	return s.pointsRepo.GetActivePointsEvents()
}

// GetPointsEventByID retrieves a points event by ID
func (s *PointsServiceImpl) GetPointsEventByID(id uint) (*models.PointsEvent, error) {
	return s.pointsRepo.GetPointsEventByID(id)
}

// UpdatePointsEvent updates a points event
func (s *PointsServiceImpl) UpdatePointsEvent(
	id uint,
	name, description string,
	eventType models.PointsEventType,
	startDate, endDate time.Time,
	multiplier float64,
	applicableSources []models.PointSourceType,
	isActive bool,
	userID uint,
) (*models.PointsEvent, error) {
	// Get existing event
	event, err := s.pointsRepo.GetPointsEventByID(id)
	if err != nil {
		return nil, fmt.Errorf("error getting points event: %w", err)
	}
	
	if event == nil {
		return nil, errors.New("points event not found")
	}
	
	// Validate inputs
	if name == "" {
		return nil, errors.New("event name is required")
	}
	
	if multiplier <= 0 {
		return nil, errors.New("multiplier must be positive")
	}
	
	if endDate.Before(startDate) {
		return nil, errors.New("end date must be after start date")
	}
	
	// Convert applicable sources to JSON
	applicableSourcesJSON, err := json.Marshal(applicableSources)
	if err != nil {
		return nil, fmt.Errorf("error marshaling applicable sources: %w", err)
	}
	
	// Update event
	event.Name = name
	event.Description = description
	event.EventType = eventType
	event.StartDate = startDate
	event.EndDate = endDate
	event.Multiplier = multiplier
	event.ApplicableSources = string(applicableSourcesJSON)
	event.IsActive = isActive
	event.LastModifiedBy = userID
	
	if err := s.pointsRepo.UpdatePointsEvent(event); err != nil {
		return nil, fmt.Errorf("error updating points event: %w", err)
	}
	
	return event, nil
}

// GetPointsMultiplier calculates the points multiplier for a given source type
func (s *PointsServiceImpl) GetPointsMultiplier(sourceType models.PointSourceType) float64 {
	// Default multiplier is 1.0 (no change)
	multiplier := 1.0
	
	// Get active events
	events, err := s.pointsRepo.GetActivePointsEvents()
	if err != nil {
		// Log error but continue with default multiplier
		fmt.Printf("Error getting active events: %v\n", err)
		return multiplier
	}
	
	// Check each event to see if it applies to this source type
	for _, event := range events {
		// Parse applicable sources
		var applicableSources []models.PointSourceType
		if err := json.Unmarshal([]byte(event.ApplicableSources), &applicableSources); err != nil {
			// Log error but continue
			fmt.Printf("Error parsing applicable sources for event %d: %v\n", event.ID, err)
			continue
		}
		
		// Check if this source type is in the applicable sources
		isApplicable := false
		for _, source := range applicableSources {
			if source == sourceType {
				isApplicable = true
				break
			}
		}
		
		if isApplicable {
			// Apply multiplier based on event type
			switch event.EventType {
			case models.EventTypeDoublePoints:
				multiplier *= event.Multiplier
			case models.EventTypeStreakMultiplier:
				// For streak multipliers, only apply if source is streak-related
				if sourceType == models.PointSourceGameStreak {
					multiplier *= event.Multiplier
				}
			case models.EventTypeBonusChallenge:
				// For bonus challenges, only apply if source is challenge-related
				if sourceType == models.PointSourceChallenge {
					multiplier *= event.Multiplier
				}
			case models.EventTypeSpecialActivity:
				// Special activities apply their multiplier regardless of source type
				// but they should be in the applicable sources list
				multiplier *= event.Multiplier
			}
		}
	}
	
	return multiplier
}

// TransferPoints transfers points from one user to another
func (s *PointsServiceImpl) TransferPoints(fromUserID, toUserID uint, amount int, notes string) (*models.PointsTransferRecord, error) {
	if amount <= 0 {
		return nil, errors.New("transfer amount must be positive")
	}
	
	if fromUserID == toUserID {
		return nil, errors.New("cannot transfer points to yourself")
	}
	
	// Get sender's balance
	fromBalance, err := s.GetUserPointsBalance(fromUserID)
	if err != nil {
		return nil, fmt.Errorf("error getting sender's balance: %w", err)
	}
	
	if fromBalance == nil || fromBalance.AvailablePoints < amount {
		return nil, errors.New("insufficient points available for transfer")
	}
	
	// Get recipient's balance
	toBalance, err := s.GetUserPointsBalance(toUserID)
	if err != nil {
		return nil, fmt.Errorf("error getting recipient's balance: %w", err)
	}
	
	// If recipient doesn't have a balance yet, create one
	if toBalance == nil {
		toBalance = &models.UserPointsBalance{
			UserID:          toUserID,
			TotalPoints:     0,
			AvailablePoints: 0,
			PendingPoints:   0,
			ExpiredPoints:   0,
			RedeemedPoints:  0,
			LastUpdated:     time.Now(),
		}
		
		if err := s.pointsRepo.CreateUserPointsBalance(toBalance); err != nil {
			return nil, fmt.Errorf("error creating recipient's balance: %w", err)
		}
	}
	
	// Create transfer record
	now := time.Now()
	transfer := &models.PointsTransferRecord{
		FromUserID:  fromUserID,
		ToUserID:    toUserID,
		Amount:      amount,
		Notes:       notes,
		Status:      "completed",
		CompletedAt: &now,
		CreatedAt:   now,
	}
	
	// Begin transaction
	// Note: A proper implementation would use a database transaction
	
	// Deduct points from sender
	senderMetaData := map[string]interface{}{
		"transferType": "sent",
		"toUserId":     toUserID,
	}
	
	senderTransaction, err := s.DeductPoints(
		fromUserID,
		amount,
		models.PointSourceTransfer,
		"transfer",
		0, // ID will be set after creating transfer record
		fmt.Sprintf("Points transferred to user %d", toUserID),
		senderMetaData,
	)
	
	if err != nil {
		return nil, fmt.Errorf("error deducting points from sender: %w", err)
	}
	
	// Award points to recipient
	recipientMetaData := map[string]interface{}{
		"transferType": "received",
		"fromUserId":   fromUserID,
	}
	
	recipientTransaction, err := s.AwardPoints(
		toUserID,
		amount,
		models.PointSourceTransfer,
		"transfer",
		0, // ID will be set after creating transfer record
		fmt.Sprintf("Points received from user %d", fromUserID),
		recipientMetaData,
	)
	
	if err != nil {
		// Attempt to rollback sender's transaction
		rollbackMetaData := map[string]interface{}{
			"originalTransactionId": senderTransaction.ID,
			"rollbackReason":        "failed recipient transaction",
		}
		
		_, rollbackErr := s.AwardPoints(
			fromUserID,
			amount,
			models.PointSourceTransfer,
			"transfer_rollback",
			0,
			"Rollback of transfer due to error",
			rollbackMetaData,
		)
		
		if rollbackErr != nil {
			// Log rollback error
			fmt.Printf("Error rolling back sender transaction: %v\n", rollbackErr)
		}
		
		return nil, fmt.Errorf("error awarding points to recipient: %w", err)
	}
	
	// Save transfer record
	if err := s.pointsRepo.CreatePointsTransfer(transfer); err != nil {
		return nil, fmt.Errorf("error creating transfer record: %w", err)
	}
	
	// Update transaction IDs with transfer ID
	senderTransaction.ReferenceID = transfer.ID
	recipientTransaction.ReferenceID = transfer.ID
	
	// Record transaction IDs in transfer record
	transfer.TransactionID = senderTransaction.ID
	
	if err := s.pointsRepo.UpdatePointsTransfer(transfer); err != nil {
		return nil, fmt.Errorf("error updating transfer record: %w", err)
	}
	
	return transfer, nil
}

// GetTransfersByUser retrieves points transfers for a user
func (s *PointsServiceImpl) GetTransfersByUser(userID uint, isReceiver bool, page, pageSize int) ([]models.PointsTransferRecord, error) {
	// Ensure valid pagination
	if page < 1 {
		page = 1
	}
	
	if pageSize < 1 {
		pageSize = 10
	}
	
	offset := (page - 1) * pageSize
	
	return s.pointsRepo.GetPointsTransfersByUser(userID, isReceiver, pageSize, offset)
}

// CreateRedemptionItem creates a new redemption item
func (s *PointsServiceImpl) CreateRedemptionItem(
	name, description, category, imageURL string,
	pointsCost int,
	isDigital bool,
	quantityAvailable int,
	redemptionCode string,
	userID uint,
) (*models.PointsRedemptionItem, error) {
	if name == "" {
		return nil, errors.New("item name is required")
	}
	
	if pointsCost <= 0 {
		return nil, errors.New("points cost must be positive")
	}
	
	// Create item
	item := &models.PointsRedemptionItem{
		Name:              name,
		Description:       description,
		PointsCost:        pointsCost,
		Category:          category,
		ImageURL:          imageURL,
		IsDigital:         isDigital,
		IsActive:          true,
		QuantityAvailable: quantityAvailable,
		RedemptionCode:    redemptionCode,
		CreatedBy:         userID,
		LastModifiedBy:    userID,
	}
	
	if err := s.pointsRepo.CreateRedemptionItem(item); err != nil {
		return nil, fmt.Errorf("error creating redemption item: %w", err)
	}
	
	return item, nil
}

// GetRedemptionItems retrieves all redemption items
func (s *PointsServiceImpl) GetRedemptionItems(activeOnly bool) ([]models.PointsRedemptionItem, error) {
	return s.pointsRepo.GetRedemptionItems(activeOnly)
}

// RedeemPoints redeems points for an item
func (s *PointsServiceImpl) RedeemPoints(userID, itemID uint, deliveryAddress, notes string) (*models.PointsRedemptionRecord, error) {
	// Get item
	item, err := s.pointsRepo.GetRedemptionItemByID(itemID)
	if err != nil {
		return nil, fmt.Errorf("error getting redemption item: %w", err)
	}
	
	if item == nil {
		return nil, errors.New("redemption item not found")
	}
	
	if !item.IsActive {
		return nil, errors.New("redemption item is not active")
	}
	
	if item.QuantityAvailable <= 0 {
		return nil, errors.New("redemption item is out of stock")
	}
	
	// Check if user has enough points
	balance, err := s.GetUserPointsBalance(userID)
	if err != nil {
		return nil, fmt.Errorf("error getting user balance: %w", err)
	}
	
	if balance == nil || balance.AvailablePoints < item.PointsCost {
		return nil, errors.New("insufficient points available")
	}
	
	// Deduct points
	metaData := map[string]interface{}{
		"itemId":   itemID,
		"itemName": item.Name,
	}
	
	transaction, err := s.DeductPoints(
		userID,
		item.PointsCost,
		models.PointSourceRedemption,
		"redemption",
		itemID,
		fmt.Sprintf("Redeemed points for %s", item.Name),
		metaData,
	)
	
	if err != nil {
		return nil, fmt.Errorf("error deducting points: %w", err)
	}
	
	// Create redemption record
	record := &models.PointsRedemptionRecord{
		UserID:         userID,
		ItemID:         itemID,
		PointsSpent:    item.PointsCost,
		TransactionID:  transaction.ID,
		RedemptionCode: item.RedemptionCode,
		Status:         "processing",
		DeliveryAddress: deliveryAddress,
		Notes:          notes,
		CreatedAt:      time.Now(),
	}
	
	if err := s.pointsRepo.CreateRedemptionRecord(record); err != nil {
		return nil, fmt.Errorf("error creating redemption record: %w", err)
	}
	
	// Update item quantity
	item.QuantityAvailable--
	if item.QuantityAvailable <= 0 {
		item.IsActive = false
	}
	
	if err := s.pointsRepo.UpdateRedemptionItem(item); err != nil {
		// Log error but continue
		fmt.Printf("Error updating redemption item quantity: %v\n", err)
	}
	
	return record, nil
}

// GetUserRedemptionHistory retrieves redemption records for a user
func (s *PointsServiceImpl) GetUserRedemptionHistory(userID uint, page, pageSize int) ([]models.PointsRedemptionRecord, error) {
	// Ensure valid pagination
	if page < 1 {
		page = 1
	}
	
	if pageSize < 1 {
		pageSize = 10
	}
	
	offset := (page - 1) * pageSize
	
	return s.pointsRepo.GetRedemptionRecordsByUser(userID, pageSize, offset)
}

// UpdateRedemptionItemStatus updates the status of a redemption item
func (s *PointsServiceImpl) UpdateRedemptionItemStatus(itemID uint, isActive bool, userID uint) error {
	// Get item
	item, err := s.pointsRepo.GetRedemptionItemByID(itemID)
	if err != nil {
		return fmt.Errorf("error getting redemption item: %w", err)
	}
	
	if item == nil {
		return errors.New("redemption item not found")
	}
	
	// Update status
	item.IsActive = isActive
	item.LastModifiedBy = userID
	
	if err := s.pointsRepo.UpdateRedemptionItem(item); err != nil {
		return fmt.Errorf("error updating redemption item: %w", err)
	}
	
	return nil
}

// UpdateRedemptionStatus updates the status of a redemption record
func (s *PointsServiceImpl) UpdateRedemptionStatus(redemptionID uint, status string, userID uint) error {
	// Get record
	record, err := s.pointsRepo.GetRedemptionRecordByID(redemptionID)
	if err != nil {
		return fmt.Errorf("error getting redemption record: %w", err)
	}
	
	if record == nil {
		return errors.New("redemption record not found")
	}
	
	// Validate status
	validStatuses := map[string]bool{
		"processing": true,
		"shipped":    true,
		"delivered":  true,
		"completed":  true,
		"cancelled":  true,
	}
	
	if !validStatuses[status] {
		return errors.New("invalid redemption status")
	}
	
	// Update status
	record.Status = status
	
	if status == "delivered" || status == "completed" {
		now := time.Now()
		record.DeliveryDate = &now
	}
	
	if err := s.pointsRepo.UpdateRedemptionRecord(record); err != nil {
		return fmt.Errorf("error updating redemption record: %w", err)
	}
	
	return nil
}

// CreateChallenge creates a new challenge
func (s *PointsServiceImpl) CreateChallenge(
	name, description string,
	pointsAwarded int,
	requiredActions map[string]interface{},
	startDate, endDate time.Time,
	maxCompletions int,
	category, difficulty string,
	userID uint,
) (*models.PointsChallenge, error) {
	if name == "" {
		return nil, errors.New("challenge name is required")
	}
	
	if pointsAwarded <= 0 {
		return nil, errors.New("points awarded must be positive")
	}
	
	if endDate.Before(startDate) {
		return nil, errors.New("end date must be after start date")
	}
	
	// Convert required actions to JSON
	requiredActionsJSON, err := json.Marshal(requiredActions)
	if err != nil {
		return nil, fmt.Errorf("error marshaling required actions: %w", err)
	}
	
	// Create challenge
	challenge := &models.PointsChallenge{
		Name:            name,
		Description:     description,
		PointsAwarded:   pointsAwarded,
		RequiredActions: string(requiredActionsJSON),
		StartDate:       startDate,
		EndDate:         endDate,
		IsActive:        true,
		MaxCompletions:  maxCompletions,
		Category:        category,
		Difficulty:      difficulty,
		CreatedBy:       userID,
		LastModifiedBy:  userID,
	}
	
	if err := s.pointsRepo.CreateChallenge(challenge); err != nil {
		return nil, fmt.Errorf("error creating challenge: %w", err)
	}
	
	return challenge, nil
}

// GetActiveChallenges retrieves active challenges
func (s *PointsServiceImpl) GetActiveChallenges() ([]models.PointsChallenge, error) {
	return s.pointsRepo.GetActiveChallenges()
}

// GetChallengeByID retrieves a challenge by ID
func (s *PointsServiceImpl) GetChallengeByID(id uint) (*models.PointsChallenge, error) {
	return s.pointsRepo.GetChallengeByID(id)
}

// UpdateChallengeProgress updates a user's progress on a challenge
func (s *PointsServiceImpl) UpdateChallengeProgress(userID, challengeID uint, progress float64) error {
	// Validate progress
	if progress < 0 || progress > 1 {
		return errors.New("progress must be between 0 and 1")
	}
	
	// Get challenge
	challenge, err := s.pointsRepo.GetChallengeByID(challengeID)
	if err != nil {
		return fmt.Errorf("error getting challenge: %w", err)
	}
	
	if challenge == nil {
		return errors.New("challenge not found")
	}
	
	// Check if challenge is active
	if !challenge.IsActive {
		return errors.New("challenge is not active")
	}
	
	now := time.Now()
	if now.Before(challenge.StartDate) || now.After(challenge.EndDate) {
		return errors.New("challenge is not currently active")
	}
	
	// Get user's challenge progress
	userChallenge, err := s.pointsRepo.GetUserChallengeByID(userID, challengeID)
	if err != nil {
		return fmt.Errorf("error getting user challenge: %w", err)
	}
	
	// If user doesn't have a challenge record yet, create one
	if userChallenge == nil {
		userChallenge = &models.UserChallenge{
			UserID:      userID,
			ChallengeID: challengeID,
			Progress:    progress,
			LastUpdated: now,
		}
		
		if err := s.pointsRepo.CreateUserChallenge(userChallenge); err != nil {
			return fmt.Errorf("error creating user challenge: %w", err)
		}
	} else {
		// Update progress
		// Only update if new progress is greater
		if progress > userChallenge.Progress {
			userChallenge.Progress = progress
			userChallenge.LastUpdated = now
			
			if err := s.pointsRepo.UpdateUserChallenge(userChallenge); err != nil {
				return fmt.Errorf("error updating user challenge: %w", err)
			}
		}
	}
	
	// Check if challenge is completed
	if progress >= 1 && (userChallenge.CompletedAt == nil || userChallenge.TimesCompleted < challenge.MaxCompletions) {
		return s.CompleteChallenge(userID, challengeID)
	}
	
	return nil
}

// CompleteChallenge marks a challenge as completed for a user
func (s *PointsServiceImpl) CompleteChallenge(userID, challengeID uint) error {
	// Get challenge
	challenge, err := s.pointsRepo.GetChallengeByID(challengeID)
	if err != nil {
		return fmt.Errorf("error getting challenge: %w", err)
	}
	
	if challenge == nil {
		return errors.New("challenge not found")
	}
	
	// Get user's challenge progress
	userChallenge, err := s.pointsRepo.GetUserChallengeByID(userID, challengeID)
	if err != nil {
		return fmt.Errorf("error getting user challenge: %w", err)
	}
	
	now := time.Now()
	
	// If user doesn't have a challenge record yet, create one
	if userChallenge == nil {
		userChallenge = &models.UserChallenge{
			UserID:         userID,
			ChallengeID:    challengeID,
			Progress:       1.0,
			CompletedAt:    &now,
			TimesCompleted: 1,
			PointsAwarded:  challenge.PointsAwarded,
			LastUpdated:    now,
		}
		
		if err := s.pointsRepo.CreateUserChallenge(userChallenge); err != nil {
			return fmt.Errorf("error creating user challenge: %w", err)
		}
	} else {
		// Check if user has reached max completions
		if challenge.MaxCompletions > 0 && userChallenge.TimesCompleted >= challenge.MaxCompletions {
			return errors.New("maximum number of completions reached for this challenge")
		}
		
		// Update challenge record
		userChallenge.Progress = 1.0
		userChallenge.CompletedAt = &now
		userChallenge.TimesCompleted++
		userChallenge.PointsAwarded += challenge.PointsAwarded
		userChallenge.LastUpdated = now
		
		if err := s.pointsRepo.UpdateUserChallenge(userChallenge); err != nil {
			return fmt.Errorf("error updating user challenge: %w", err)
		}
	}
	
	// Award points
	metaData := map[string]interface{}{
		"challengeId": challengeID,
		"name":        challenge.Name,
		"completion":  userChallenge.TimesCompleted,
	}
	
	_, err = s.AwardPoints(
		userID,
		challenge.PointsAwarded,
		models.PointSourceChallenge,
		"challenge",
		challengeID,
		fmt.Sprintf("Completed challenge: %s", challenge.Name),
		metaData,
	)
	
	if err != nil {
		return fmt.Errorf("error awarding points for challenge: %w", err)
	}
	
	return nil
}

// GetUserChallenges retrieves all challenges for a user
func (s *PointsServiceImpl) GetUserChallenges(userID uint) ([]models.UserChallenge, error) {
	return s.pointsRepo.GetUserChallenges(userID)
}

// AwardReadingPoints awards points for reading content
func (s *PointsServiceImpl) AwardReadingPoints(userID, sectionID uint, metadata map[string]interface{}) (*models.PointsTransaction, error) {
	const POINTS_PER_SECTION = 20
	
	return s.AwardPoints(
		userID,
		POINTS_PER_SECTION,
		models.PointSourceReading,
		"section",
		sectionID,
		"Completed reading section",
		metadata,
	)
}

// AwardDiscussionPoints awards points for discussion participation
func (s *PointsServiceImpl) AwardDiscussionPoints(userID uint, discussionType string, discussionID uint, metadata map[string]interface{}) (*models.PointsTransaction, error) {
	const POINTS_PER_DISCUSSION = 10
	
	return s.AwardPoints(
		userID,
		POINTS_PER_DISCUSSION,
		models.PointSourceDiscussion,
		discussionType,
		discussionID,
		fmt.Sprintf("Participated in %s", discussionType),
		metadata,
	)
}

// AwardContentCreationPoints awards points for content creation
func (s *PointsServiceImpl) AwardContentCreationPoints(userID uint, contentType string, contentID uint, quality float64, metadata map[string]interface{}) (*models.PointsTransaction, error) {
	// Base points for content creation
	basePoints := 15
	
	// Apply quality multiplier (1.0-2.0)
	qualityMultiplier := 1.0
	if quality > 0 {
		// Ensure quality is between 0 and 1
		if quality > 1 {
			quality = 1
		}
		
		// Scale quality to a 1.0-2.0 range
		qualityMultiplier = 1.0 + quality
	}
	
	// Calculate points
	points := int(float64(basePoints) * qualityMultiplier)
	
	return s.AwardPoints(
		userID,
		points,
		models.PointSourceContent,
		contentType,
		contentID,
		fmt.Sprintf("Created %s content", contentType),
		metadata,
	)
}

// AwardSocialSharingPoints awards points for social sharing
func (s *PointsServiceImpl) AwardSocialSharingPoints(userID uint, platform string, contentType string, contentID uint, metadata map[string]interface{}) (*models.PointsTransaction, error) {
	const POINTS_PER_SHARE = 15
	
	// Enrich metadata
	if metadata == nil {
		metadata = make(map[string]interface{})
	}
	metadata["platform"] = platform
	
	return s.AwardPoints(
		userID,
		POINTS_PER_SHARE,
		models.PointSourceSocial,
		contentType,
		contentID,
		fmt.Sprintf("Shared content on %s", platform),
		metadata,
	)
}

// UpdateStreak updates a user's activity streak
func (s *PointsServiceImpl) UpdateStreak(userID uint, activityType string) (int, error) {
	// Get user's balance
	balance, err := s.GetUserPointsBalance(userID)
	if err != nil {
		return 0, fmt.Errorf("error getting user balance: %w", err)
	}
	
	if balance == nil {
		return 0, errors.New("user has no points balance")
	}
	
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	
	// Check if streak was already updated today
	if balance.LastStreakDate != nil {
		lastStreakDate := time.Date(balance.LastStreakDate.Year(), balance.LastStreakDate.Month(), balance.LastStreakDate.Day(), 0, 0, 0, 0, balance.LastStreakDate.Location())
		if lastStreakDate.Equal(today) {
			// Already updated today, return current streak
			return balance.DailyReadingStreak, nil
		}
	}
	
	// Check if streak should be continued or reset
	var newStreak int
	var streakBroken bool
	
	if balance.LastStreakDate != nil {
		yesterday := today.AddDate(0, 0, -1)
		lastStreakDate := time.Date(balance.LastStreakDate.Year(), balance.LastStreakDate.Month(), balance.LastStreakDate.Day(), 0, 0, 0, 0, balance.LastStreakDate.Location())
		
		if lastStreakDate.Equal(yesterday) {
			// Continuing streak
			newStreak = balance.DailyReadingStreak + 1
			streakBroken = false
		} else {
			// Streak broken
			newStreak = 1
			streakBroken = true
		}
	} else {
		// First streak
		newStreak = 1
		streakBroken = false
	}
	
	// Update streak
	balance.DailyReadingStreak = newStreak
	balance.LastStreakDate = &now
	balance.LastUpdated = now
	
	if err := s.pointsRepo.UpdateUserPointsBalance(balance); err != nil {
		return 0, fmt.Errorf("error updating user balance: %w", err)
	}
	
	// Award streak points if applicable
	if newStreak > 1 && !streakBroken {
		// Increasing points for longer streaks
		var streakPoints int
		
		if newStreak >= 30 {
			streakPoints = 50 // 30+ day streak
		} else if newStreak >= 14 {
			streakPoints = 30 // 14+ day streak
		} else if newStreak >= 7 {
			streakPoints = 20 // 7+ day streak
		} else if newStreak >= 3 {
			streakPoints = 10 // 3+ day streak
		} else {
			streakPoints = 5 // 2 day streak
		}
		
		// Apply any active multipliers
		multiplier := s.GetPointsMultiplier(models.PointSourceGameStreak)
		adjustedPoints := int(float64(streakPoints) * multiplier)
		
		metadata := map[string]interface{}{
			"activityType": activityType,
			"streak":       newStreak,
			"basePoints":   streakPoints,
			"multiplier":   multiplier,
		}
		
		_, err := s.AwardPoints(
			userID,
			adjustedPoints,
			models.PointSourceGameStreak,
			"streak",
			0,
			fmt.Sprintf("%d day streak bonus", newStreak),
			metadata,
		)
		
		if err != nil {
			// Log error but continue
			fmt.Printf("Error awarding streak points: %v\n", err)
		}
	}
	
	return newStreak, nil
}

// ResetStreak resets a user's activity streak
func (s *PointsServiceImpl) ResetStreak(userID uint) error {
	// Get user's balance
	balance, err := s.GetUserPointsBalance(userID)
	if err != nil {
		return fmt.Errorf("error getting user balance: %w", err)
	}
	
	if balance == nil {
		return errors.New("user has no points balance")
	}
	
	// Reset streak
	balance.DailyReadingStreak = 0
	balance.LastStreakDate = nil
	balance.LastUpdated = time.Now()
	
	if err := s.pointsRepo.UpdateUserPointsBalance(balance); err != nil {
		return fmt.Errorf("error updating user balance: %w", err)
	}
	
	return nil
}

// GetGlobalLeaderboard retrieves the global leaderboard
func (s *PointsServiceImpl) GetGlobalLeaderboard(page, pageSize int) ([]models.UserPointsBalance, error) {
	// Ensure valid pagination
	if page < 1 {
		page = 1
	}
	
	if pageSize < 1 {
		pageSize = 10
	}
	
	offset := (page - 1) * pageSize
	
	return s.pointsRepo.GetGlobalLeaderboard(pageSize, offset)
}

// GetCategoryLeaderboard retrieves a category-specific leaderboard
func (s *PointsServiceImpl) GetCategoryLeaderboard(category string, page, pageSize int) ([]struct {
	UserID uint
	Points int
}, error) {
	// Ensure valid pagination
	if page < 1 {
		page = 1
	}
	
	if pageSize < 1 {
		pageSize = 10
	}
	
	offset := (page - 1) * pageSize
	
	return s.pointsRepo.GetCategoryLeaderboard(category, pageSize, offset)
}

// GetTimeRangeLeaderboard retrieves a leaderboard for a specific time period
func (s *PointsServiceImpl) GetTimeRangeLeaderboard(period string, page, pageSize int) ([]struct {
	UserID uint
	Points int
}, error) {
	// Ensure valid pagination
	if page < 1 {
		page = 1
	}
	
	if pageSize < 1 {
		pageSize = 10
	}
	
	offset := (page - 1) * pageSize
	
	// Calculate time range
	now := time.Now()
	var startDate time.Time
	
	switch period {
	case "daily":
		startDate = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	case "weekly":
		// Start of current week (Sunday)
		daysToSunday := int(now.Weekday())
		startDate = time.Date(now.Year(), now.Month(), now.Day()-daysToSunday, 0, 0, 0, 0, now.Location())
	case "monthly":
		// Start of current month
		startDate = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	default:
		// Default to all time
		startDate = time.Time{} // Zero time
	}
	
	return s.pointsRepo.GetTimeRangeLeaderboard(startDate, now, pageSize, offset)
}