package models

import (
	"time"
)

// Activity represents a user's learning activity
type Activity struct {
	ID       int       `json:"id" db:"id"`
	UserID   int       `json:"user_id" db:"user_id"`
	Type     string    `json:"type" db:"type"`
	Name     string    `json:"name" db:"name"`
	Progress int       `json:"progress" db:"progress"`
	Date     time.Time `json:"date" db:"date"`
}

// UserProgress represents a user's overall progress
type UserProgress struct {
	ID                int        `json:"id" db:"id"`
	UserID           int        `json:"user_id" db:"user_id"`
	OverallCompletion int        `json:"overallCompletion" db:"overall_completion"`
	PointsEarned      int        `json:"pointsEarned" db:"points_earned"`
	Streak            int        `json:"streak" db:"streak"`
	Level             int        `json:"level" db:"level"`
	RecentActivities  []Activity `json:"recentActivities"`
}

// Milestone represents a learning milestone
type Milestone struct {
	ID          int        `json:"id" db:"id"`
	Name        string     `json:"name" db:"name"`
	Description string     `json:"description" db:"description"`
	Completed   bool       `json:"completed" db:"completed"`
	Date        *time.Time `json:"date,omitempty" db:"date"`
	Progress    *int       `json:"progress,omitempty" db:"progress"`
	Icon        string     `json:"icon" db:"icon"`
	UserID      int        `json:"user_id" db:"user_id"`
}

// Achievement represents a user achievement
type Achievement struct {
	ID          int        `json:"id" db:"id"`
	Name        string     `json:"name" db:"name"`
	Description string     `json:"description" db:"description"`
	Earned      bool       `json:"earned" db:"earned"`
	Date        *time.Time `json:"date,omitempty" db:"date"`
	Progress    *int       `json:"progress,omitempty" db:"progress"`
	Icon        string     `json:"icon" db:"icon"`
	UserID      int        `json:"user_id" db:"user_id"`
}

// HistoricalData represents historical progress data
type HistoricalData struct {
	ID         int       `json:"id" db:"id"`
	UserID     int       `json:"user_id" db:"user_id"`
	Month      string    `json:"month" db:"month"`
	Progress   int       `json:"progress" db:"progress"`
	Activities int       `json:"activities" db:"activities"`
	Date       time.Time `json:"date" db:"date"`
}

// SkillData represents a user's skill level
type SkillData struct {
	ID     int    `json:"id" db:"id"`
	UserID int    `json:"user_id" db:"user_id"`
	Name   string `json:"name" db:"name"`
	Value  int    `json:"value" db:"value"`
}

// MilestoneDefinition represents a system-defined milestone
type MilestoneDefinition struct {
	ID          int    `json:"id" db:"id"`
	Name        string `json:"name" db:"name"`
	Description string `json:"description" db:"description"`
	Criteria    string `json:"criteria" db:"criteria"`
	Icon        string `json:"icon" db:"icon"`
}

// AchievementDefinition represents a system-defined achievement
type AchievementDefinition struct {
	ID          int    `json:"id" db:"id"`
	Name        string `json:"name" db:"name"`
	Description string `json:"description" db:"description"`
	Criteria    string `json:"criteria" db:"criteria"`
	Icon        string `json:"icon" db:"icon"`
}
