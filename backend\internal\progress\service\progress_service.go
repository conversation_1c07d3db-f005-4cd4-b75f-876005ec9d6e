package service

import (
	"time"

	"github.com/yerenwg/greatnigeria/internal/progress/models"
	"github.com/yerenwg/greatnigeria/internal/progress/repository"
)

// ProgressService defines the interface for progress business logic
type ProgressService interface {
	GetUserProgress(userID int) (*models.UserProgress, error)
	GetMilestones(userID int) ([]models.Milestone, error)
	GetAchievements(userID int) ([]models.Achievement, error)
	GetHistoricalData(userID int) ([]models.HistoricalData, error)
	GetSkillsData(userID int) ([]models.SkillData, error)
	UpdateMilestoneProgress(milestoneID, progress int) error
	UpdateAchievementProgress(achievementID, progress int) error
	LogActivity(userID int, activityType, name string, progress int) error
	CheckAndUpdateMilestones(userID int) error
	CheckAndUpdateAchievements(userID int) error
	InitializeUserProgress(userID int) error
}

// DefaultProgressService implements ProgressService
type DefaultProgressService struct {
	repo repository.ProgressRepository
}

// NewProgressService creates a new DefaultProgressService
func NewProgressService(repo repository.ProgressRepository) *DefaultProgressService {
	return &DefaultProgressService{repo: repo}
}

// GetUserProgress retrieves a user's progress
func (s *DefaultProgressService) GetUserProgress(userID int) (*models.UserProgress, error) {
	return s.repo.GetUserProgress(userID)
}

// GetMilestones retrieves a user's milestones
func (s *DefaultProgressService) GetMilestones(userID int) ([]models.Milestone, error) {
	return s.repo.GetMilestones(userID)
}

// GetAchievements retrieves a user's achievements
func (s *DefaultProgressService) GetAchievements(userID int) ([]models.Achievement, error) {
	return s.repo.GetAchievements(userID)
}

// GetHistoricalData retrieves a user's historical progress data
func (s *DefaultProgressService) GetHistoricalData(userID int) ([]models.HistoricalData, error) {
	return s.repo.GetHistoricalData(userID)
}

// GetSkillsData retrieves a user's skills data
func (s *DefaultProgressService) GetSkillsData(userID int) ([]models.SkillData, error) {
	return s.repo.GetSkillsData(userID)
}

// UpdateMilestoneProgress updates a milestone's progress
func (s *DefaultProgressService) UpdateMilestoneProgress(milestoneID, progress int) error {
	return s.repo.UpdateMilestoneProgress(milestoneID, progress)
}

// UpdateAchievementProgress updates an achievement's progress
func (s *DefaultProgressService) UpdateAchievementProgress(achievementID, progress int) error {
	return s.repo.UpdateAchievementProgress(achievementID, progress)
}

// LogActivity logs a user activity
func (s *DefaultProgressService) LogActivity(userID int, activityType, name string, progress int) error {
	activity := &models.Activity{
		UserID:   userID,
		Type:     activityType,
		Name:     name,
		Progress: progress,
		Date:     time.Now(),
	}
	
	err := s.repo.LogActivity(activity)
	if err != nil {
		return err
	}
	
	// After logging an activity, check if any milestones or achievements should be updated
	if err := s.CheckAndUpdateMilestones(userID); err != nil {
		return err
	}
	
	if err := s.CheckAndUpdateAchievements(userID); err != nil {
		return err
	}
	
	return nil
}

// CheckAndUpdateMilestones checks and updates milestones based on user activities
func (s *DefaultProgressService) CheckAndUpdateMilestones(userID int) error {
	// Get milestone definitions
	definitions, err := s.repo.GetMilestoneDefinitions()
	if err != nil {
		return err
	}
	
	// Get user milestones
	milestones, err := s.repo.GetMilestones(userID)
	if err != nil {
		return err
	}
	
	// Get recent activities
	activities, err := s.repo.GetRecentActivities(userID, 100)
	if err != nil {
		return err
	}
	
	// For each milestone, check if it should be updated
	for _, milestone := range milestones {
		if milestone.Completed {
			continue
		}
		
		// Find the corresponding definition
		var definition *models.MilestoneDefinition
		for _, def := range definitions {
			if def.Name == milestone.Name {
				definition = &def
				break
			}
		}
		
		if definition == nil {
			continue
		}
		
		// Calculate progress based on the criteria and activities
		progress := calculateMilestoneProgress(definition, activities)
		
		// Update the milestone if progress has changed
		if milestone.Progress == nil || *milestone.Progress != progress {
			if err := s.repo.UpdateMilestoneProgress(milestone.ID, progress); err != nil {
				return err
			}
		}
	}
	
	return nil
}

// CheckAndUpdateAchievements checks and updates achievements based on user activities
func (s *DefaultProgressService) CheckAndUpdateAchievements(userID int) error {
	// Get achievement definitions
	definitions, err := s.repo.GetAchievementDefinitions()
	if err != nil {
		return err
	}
	
	// Get user achievements
	achievements, err := s.repo.GetAchievements(userID)
	if err != nil {
		return err
	}
	
	// Get recent activities
	activities, err := s.repo.GetRecentActivities(userID, 100)
	if err != nil {
		return err
	}
	
	// For each achievement, check if it should be updated
	for _, achievement := range achievements {
		if achievement.Earned {
			continue
		}
		
		// Find the corresponding definition
		var definition *models.AchievementDefinition
		for _, def := range definitions {
			if def.Name == achievement.Name {
				definition = &def
				break
			}
		}
		
		if definition == nil {
			continue
		}
		
		// Calculate progress based on the criteria and activities
		progress := calculateAchievementProgress(definition, activities)
		
		// Update the achievement if progress has changed
		if achievement.Progress == nil || *achievement.Progress != progress {
			if err := s.repo.UpdateAchievementProgress(achievement.ID, progress); err != nil {
				return err
			}
		}
	}
	
	return nil
}

// InitializeUserProgress initializes progress tracking for a new user
func (s *DefaultProgressService) InitializeUserProgress(userID int) error {
	// Implementation would create initial records for a new user
	// This would include creating milestone and achievement records based on definitions
	return nil
}

// Helper function to calculate milestone progress
func calculateMilestoneProgress(definition *models.MilestoneDefinition, activities []models.Activity) int {
	// This is a simplified implementation
	// In a real application, this would parse the criteria and calculate progress accordingly
	
	// For demonstration purposes, we'll just return a random progress value
	// In a real implementation, this would analyze the activities and criteria
	return 50
}

// Helper function to calculate achievement progress
func calculateAchievementProgress(definition *models.AchievementDefinition, activities []models.Activity) int {
	// This is a simplified implementation
	// In a real application, this would parse the criteria and calculate progress accordingly
	
	// For demonstration purposes, we'll just return a random progress value
	// In a real implementation, this would analyze the activities and criteria
	return 70
}
