package models

import (
	"time"
)

// ProjectStatus represents the status of a project
type ProjectStatus string

const (
	ProjectStatusPlanning   ProjectStatus = "planning"
	ProjectStatusInProgress ProjectStatus = "in_progress"
	ProjectStatusCompleted  ProjectStatus = "completed"
	ProjectStatusOnHold     ProjectStatus = "on_hold"
	ProjectStatusCancelled  ProjectStatus = "cancelled"
)

// Project represents a collaborative project in the system
type Project struct {
	ID          uint64        `json:"id" gorm:"primaryKey"`
	Title       string        `json:"title" gorm:"size:255;not null"`
	Description string        `json:"description" gorm:"type:text"`
	Status      ProjectStatus `json:"status" gorm:"size:50;not null;default:'planning'"`
	StartDate   *time.Time    `json:"start_date"`
	EndDate     *time.Time    `json:"end_date"`
	OwnerID     uint64        `json:"owner_id" gorm:"not null;index"`
	IsPublic    bool          `json:"is_public" gorm:"default:true"`
	Location    string        `json:"location" gorm:"size:255"`
	ImageURL    string        `json:"image_url" gorm:"size:255"`
	CreatedAt   time.Time     `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time     `json:"updated_at" gorm:"autoUpdateTime"`
	Tasks       []Task        `json:"tasks,omitempty" gorm:"foreignKey:ProjectID"`
	Members     []ProjectMember `json:"members,omitempty" gorm:"foreignKey:ProjectID"`
	Tags        []ProjectTag  `json:"tags,omitempty" gorm:"many2many:project_tag_mappings;"`
	BookSectionIDs string     `json:"book_section_ids" gorm:"type:text"` // Stored as JSON array string of section IDs
}

// ProjectMember represents a member of a project
type ProjectMember struct {
	ID        uint64    `json:"id" gorm:"primaryKey"`
	ProjectID uint64    `json:"project_id" gorm:"not null;index:idx_project_member"`
	UserID    uint64    `json:"user_id" gorm:"not null;index:idx_project_member"`
	Role      string    `json:"role" gorm:"size:50;not null;default:'member'"`
	JoinedAt  time.Time `json:"joined_at" gorm:"autoCreateTime"`
}

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusTodo       TaskStatus = "todo"
	TaskStatusInProgress TaskStatus = "in_progress"
	TaskStatusReview     TaskStatus = "review"
	TaskStatusCompleted  TaskStatus = "completed"
)

// TaskPriority represents the priority of a task
type TaskPriority string

const (
	TaskPriorityLow    TaskPriority = "low"
	TaskPriorityMedium TaskPriority = "medium"
	TaskPriorityHigh   TaskPriority = "high"
	TaskPriorityCritical TaskPriority = "critical"
)

// Task represents a task within a project
type Task struct {
	ID          uint64       `json:"id" gorm:"primaryKey"`
	ProjectID   uint64       `json:"project_id" gorm:"not null;index"`
	Title       string       `json:"title" gorm:"size:255;not null"`
	Description string       `json:"description" gorm:"type:text"`
	Status      TaskStatus   `json:"status" gorm:"size:50;not null;default:'todo'"`
	Priority    TaskPriority `json:"priority" gorm:"size:50;not null;default:'medium'"`
	AssigneeID  *uint64      `json:"assignee_id" gorm:"index"`
	CreatorID   uint64       `json:"creator_id" gorm:"not null;index"`
	DueDate     *time.Time   `json:"due_date"`
	CreatedAt   time.Time    `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time    `json:"updated_at" gorm:"autoUpdateTime"`
	CompletedAt *time.Time   `json:"completed_at"`
}

// ProjectTag represents a tag for categorizing projects
type ProjectTag struct {
	ID        uint64    `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"size:50;not null;uniqueIndex"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	Projects  []Project `json:"projects,omitempty" gorm:"many2many:project_tag_mappings;"`
}

// ProjectUpdate represents an update on a project's progress
type ProjectUpdate struct {
	ID        uint64    `json:"id" gorm:"primaryKey"`
	ProjectID uint64    `json:"project_id" gorm:"not null;index"`
	UserID    uint64    `json:"user_id" gorm:"not null;index"`
	Content   string    `json:"content" gorm:"type:text;not null"`
	ImageURL  string    `json:"image_url" gorm:"size:255"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}