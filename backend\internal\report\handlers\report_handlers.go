package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/greatnigeria/internal/report/models"
	"github.com/greatnigeria/internal/report/service"
)

// ReportHandlers contains all the handlers for report-related endpoints
type ReportHandlers struct {
	ReportService *service.ReportService
}

// NewReportHandlers creates a new ReportHandlers
func NewReportHandlers(reportService *service.ReportService) *ReportHandlers {
	return &ReportHandlers{
		ReportService: reportService,
	}
}

// GetReportTemplates handles GET /api/reports/templates
func (h *ReportHandlers) GetReportTemplates(c *gin.Context) {
	// Parse filter parameters
	params := make(map[string]interface{})

	if reportType := c.Query("report_type"); reportType != "" {
		params["report_type"] = reportType
	}

	if creatorIDStr := c.Query("creator_id"); creatorIDStr != "" {
		creatorID, err := strconv.ParseUint(creatorIDStr, 10, 64)
		if err == nil {
			params["creator_id"] = creatorID
		}
	}

	if isPublicStr := c.Query("is_public"); isPublicStr != "" {
		isPublic := isPublicStr == "true"
		params["is_public"] = isPublic
	}

	if search := c.Query("search"); search != "" {
		params["search"] = search
	}

	templates, err := h.ReportService.GetReportTemplates(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, templates)
}

// GetReportTemplateByID handles GET /api/reports/templates/:id
func (h *ReportHandlers) GetReportTemplateByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	template, err := h.ReportService.GetReportTemplateByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	c.JSON(http.StatusOK, template)
}

// CreateReportTemplate handles POST /api/reports/templates
func (h *ReportHandlers) CreateReportTemplate(c *gin.Context) {
	var template models.ReportTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set creator ID from authenticated user
	// userID, exists := c.Get("user_id")
	// if !exists {
	// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
	// 	return
	// }
	// creatorID := userID.(uint64)
	// template.CreatorID = &creatorID

	if err := h.ReportService.CreateReportTemplate(&template); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, template)
}

// UpdateReportTemplate handles PUT /api/reports/templates/:id
func (h *ReportHandlers) UpdateReportTemplate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	// Check if the template exists
	existingTemplate, err := h.ReportService.GetReportTemplateByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	var template models.ReportTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	template.ID = id
	template.CreatorID = existingTemplate.CreatorID // Ensure the creator remains the same

	if err := h.ReportService.UpdateReportTemplate(&template); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, template)
}

// DeleteReportTemplate handles DELETE /api/reports/templates/:id
func (h *ReportHandlers) DeleteReportTemplate(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	if err := h.ReportService.DeleteReportTemplate(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template deleted successfully"})
}

// CreateProjectReport handles POST /api/projects/:projectID/reports
func (h *ReportHandlers) CreateProjectReport(c *gin.Context) {
	projectID, err := strconv.ParseUint(c.Param("projectID"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}

	var report models.ProjectReport
	if err := c.ShouldBindJSON(&report); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	report.ProjectID = projectID

	// Set author ID from authenticated user
	// userID, exists := c.Get("user_id")
	// if !exists {
	// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
	// 	return
	// }
	// report.AuthorID = userID.(uint64)

	if err := h.ReportService.CreateProjectReport(&report); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, report)
}

// GetProjectReports handles GET /api/projects/:projectID/reports
func (h *ReportHandlers) GetProjectReports(c *gin.Context) {
	projectID, err := strconv.ParseUint(c.Param("projectID"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID format"})
		return
	}

	// Parse filter parameters
	params := make(map[string]interface{})
	params["project_id"] = projectID

	if reportType := c.Query("report_type"); reportType != "" {
		params["report_type"] = reportType
	}

	if authorIDStr := c.Query("author_id"); authorIDStr != "" {
		authorID, err := strconv.ParseUint(authorIDStr, 10, 64)
		if err == nil {
			params["author_id"] = authorID
		}
	}

	if isPublicStr := c.Query("is_public"); isPublicStr != "" {
		isPublic := isPublicStr == "true"
		params["is_public"] = isPublic
	}

	if search := c.Query("search"); search != "" {
		params["search"] = search
	}

	reports, err := h.ReportService.GetProjectReports(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, reports)
}

// GetAllReports handles GET /api/reports
func (h *ReportHandlers) GetAllReports(c *gin.Context) {
	// Parse filter parameters
	params := make(map[string]interface{})

	if reportType := c.Query("report_type"); reportType != "" {
		params["report_type"] = reportType
	}

	if authorIDStr := c.Query("author_id"); authorIDStr != "" {
		authorID, err := strconv.ParseUint(authorIDStr, 10, 64)
		if err == nil {
			params["author_id"] = authorID
		}
	}

	if isPublicStr := c.Query("is_public"); isPublicStr != "" {
		isPublic := isPublicStr == "true"
		params["is_public"] = isPublic
	}

	if search := c.Query("search"); search != "" {
		params["search"] = search
	}

	reports, err := h.ReportService.GetProjectReports(params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, reports)
}

// GetProjectReportByID handles GET /api/reports/:id
func (h *ReportHandlers) GetProjectReportByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	report, err := h.ReportService.GetProjectReportByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
		return
	}

	// Increment view count
	go h.ReportService.IncrementReportViewCount(id)

	c.JSON(http.StatusOK, report)
}

// UpdateProjectReport handles PUT /api/reports/:id
func (h *ReportHandlers) UpdateProjectReport(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	// Check if the report exists
	existingReport, err := h.ReportService.GetProjectReportByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
		return
	}

	var report models.ProjectReport
	if err := c.ShouldBindJSON(&report); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	report.ID = id
	report.ProjectID = existingReport.ProjectID // Ensure project remains the same
	report.AuthorID = existingReport.AuthorID   // Ensure author remains the same

	if err := h.ReportService.UpdateProjectReport(&report); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, report)
}

// DeleteProjectReport handles DELETE /api/reports/:id
func (h *ReportHandlers) DeleteProjectReport(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	if err := h.ReportService.DeleteProjectReport(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Report deleted successfully"})
}

// PublishProjectReport handles PUT /api/reports/:id/publish
func (h *ReportHandlers) PublishProjectReport(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	if err := h.ReportService.PublishProjectReport(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Report published successfully"})
}

// CreateReportFeedback handles POST /api/reports/:id/feedback
func (h *ReportHandlers) CreateReportFeedback(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID format"})
		return
	}

	var feedback models.ReportFeedback
	if err := c.ShouldBindJSON(&feedback); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	feedback.ReportID = reportID

	// Set user ID from authenticated user
	// userID, exists := c.Get("user_id")
	// if !exists {
	// 	c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
	// 	return
	// }
	// feedback.UserID = userID.(uint64)

	if err := h.ReportService.CreateReportFeedback(&feedback); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, feedback)
}

// GetReportFeedback handles GET /api/reports/:id/feedback
func (h *ReportHandlers) GetReportFeedback(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID format"})
		return
	}

	feedback, err := h.ReportService.GetReportFeedback(reportID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, feedback)
}

// GetReportAverageRating handles GET /api/reports/:id/rating
func (h *ReportHandlers) GetReportAverageRating(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID format"})
		return
	}

	rating, err := h.ReportService.GetAverageRating(reportID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"average_rating": rating})
}

// GetReportsByBookSection handles GET /api/books/:bookID/chapters/:chapterID/sections/:sectionID/reports
func (h *ReportHandlers) GetReportsByBookSection(c *gin.Context) {
	bookID, err := strconv.ParseUint(c.Param("bookID"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid book ID format"})
		return
	}

	chapterID, err := strconv.ParseUint(c.Param("chapterID"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid chapter ID format"})
		return
	}

	sectionID, err := strconv.ParseUint(c.Param("sectionID"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid section ID format"})
		return
	}

	reports, err := h.ReportService.GetReportsByBookSection(bookID, chapterID, sectionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, reports)
}

// AddReportToBookSection handles POST /api/reports/:id/booksections
func (h *ReportHandlers) AddReportToBookSection(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID format"})
		return
	}

	var request struct {
		BookID    uint64 `json:"book_id" binding:"required"`
		ChapterID uint64 `json:"chapter_id" binding:"required"`
		SectionID uint64 `json:"section_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.ReportService.AddReportToBookSection(reportID, request.BookID, request.ChapterID, request.SectionID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Report added to book section successfully"})
}