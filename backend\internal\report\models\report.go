package models

import (
	"time"
)

// ReportType represents the type of a report
type ReportType string

const (
	ReportTypeProgress   ReportType = "progress"
	ReportTypeImpact     ReportType = "impact"
	ReportTypeChallenges ReportType = "challenges"
	ReportTypeLessons    ReportType = "lessons"
	ReportTypeFinal      ReportType = "final"
)

// ReportTemplate represents a template for creating project reports
type ReportTemplate struct {
	ID              uint64    `json:"id" gorm:"primaryKey"`
	Title           string    `json:"title" gorm:"size:100;not null"`
	Description     string    `json:"description" gorm:"type:text"`
	ReportType      ReportType `json:"report_type" gorm:"size:50;not null"`
	TemplateContent string    `json:"template_content" gorm:"type:text;not null"`
	CreatorID       *uint64   `json:"creator_id" gorm:"index"`
	IsPublic        bool      `json:"is_public" gorm:"default:true"`
	UsageCount      uint64    `json:"usage_count" gorm:"default:0"`
	CreatedAt       time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// ProjectReport represents a report generated for a project
type ProjectReport struct {
	ID               uint64    `json:"id" gorm:"primaryKey"`
	ProjectID        uint64    `json:"project_id" gorm:"not null;index"`
	TemplateID       *uint64   `json:"template_id" gorm:"index"` // Nullable as custom reports may not use a template
	Title            string    `json:"title" gorm:"size:255;not null"`
	Content          string    `json:"content" gorm:"type:text;not null"`
	ReportType       ReportType `json:"report_type" gorm:"size:50;not null"`
	AuthorID         uint64    `json:"author_id" gorm:"not null;index"`
	IsPublic         bool      `json:"is_public" gorm:"default:true"`
	PublishedAt      *time.Time `json:"published_at"`
	ViewCount        uint64    `json:"view_count" gorm:"default:0"`
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"autoUpdateTime"`
	BookSectionIDs   string    `json:"book_section_ids" gorm:"type:text"` // Stored as JSON array string of section IDs
}

// ReportFeedback represents feedback on a project report
type ReportFeedback struct {
	ID        uint64    `json:"id" gorm:"primaryKey"`
	ReportID  uint64    `json:"report_id" gorm:"not null;index"`
	UserID    uint64    `json:"user_id" gorm:"not null;index"`
	Rating    int       `json:"rating" gorm:"not null"`
	Comment   string    `json:"comment" gorm:"type:text"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}