package models

import (
	"time"
)

// ResourceCategory represents a category for organizing resources
type ResourceCategory struct {
	ID          uint64     `json:"id" gorm:"primaryKey"`
	Name        string     `json:"name" gorm:"size:100;not null"`
	Description string     `json:"description" gorm:"type:text"`
	ParentID    *uint64    `json:"parent_id" gorm:"index"`
	SortOrder   int        `json:"sort_order" gorm:"default:0"`
	CreatedAt   time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
	Resources   []Resource `json:"resources,omitempty" gorm:"foreignKey:CategoryID"`
}

// ResourceType represents the type of a resource
type ResourceType string

const (
	ResourceTypeTemplate    ResourceType = "template"
	ResourceTypeToolkit     ResourceType = "toolkit"
	ResourceTypeGuide       ResourceType = "guide"
	ResourceTypeCaseStudy   ResourceType = "case_study"
	ResourceTypeFramework   ResourceType = "framework"
	ResourceTypeMethodology ResourceType = "methodology"
)

// Resource represents an item in the resource library
type Resource struct {
	ID            uint64        `json:"id" gorm:"primaryKey"`
	Title         string        `json:"title" gorm:"size:255;not null"`
	Description   string        `json:"description" gorm:"type:text"`
	Content       string        `json:"content" gorm:"type:text"`
	ResourceType  ResourceType  `json:"resource_type" gorm:"size:50;not null"`
	FileURL       string        `json:"file_url" gorm:"size:255"`
	ThumbnailURL  string        `json:"thumbnail_url" gorm:"size:255"`
	CategoryID    *uint64       `json:"category_id" gorm:"index"`
	AuthorID      *uint64       `json:"author_id" gorm:"index"`
	IsFeatured    bool          `json:"is_featured" gorm:"default:false"`
	IsPremium     bool          `json:"is_premium" gorm:"default:false"`
	DownloadCount uint64        `json:"download_count" gorm:"default:0"`
	ViewCount     uint64        `json:"view_count" gorm:"default:0"`
	CreatedAt     time.Time     `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time     `json:"updated_at" gorm:"autoUpdateTime"`
	Tags          []ResourceTag `json:"tags,omitempty" gorm:"many2many:resource_tag_mappings;"`
	BookSectionIDs string       `json:"book_section_ids" gorm:"type:text"` // Stored as JSON array string of section IDs
}

// ResourceTag represents a tag for categorizing resources
type ResourceTag struct {
	ID        uint64     `json:"id" gorm:"primaryKey"`
	Name      string     `json:"name" gorm:"size:50;not null;uniqueIndex"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime"`
	Resources []Resource `json:"resources,omitempty" gorm:"many2many:resource_tag_mappings;"`
}

// ResourceBookSectionMapping maps resources to specific book sections
type ResourceBookSectionMapping struct {
	ID         uint64    `json:"id" gorm:"primaryKey"`
	ResourceID uint64    `json:"resource_id" gorm:"not null;index:idx_resource_section"`
	BookID     uint64    `json:"book_id" gorm:"not null;index:idx_resource_section"`
	ChapterID  uint64    `json:"chapter_id" gorm:"not null;index:idx_resource_section"`
	SectionID  uint64    `json:"section_id" gorm:"not null;index:idx_resource_section"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`
}