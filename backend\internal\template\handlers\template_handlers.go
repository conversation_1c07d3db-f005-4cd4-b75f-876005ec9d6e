package handlers

import (
	"fmt"
	"html/template"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// TemplateHandlers manages the rendering of HTML templates
type TemplateHandlers struct {
	templates *template.Template
}

// PageData contains the data needed to render a template
type PageData struct {
	Title       string
	Description string
	Content     template.HTML
	CustomCSS   string
	CustomJS    string
	CurrentYear int
}

// NewTemplateHandlers creates a new instance of TemplateHandlers
func NewTemplateHandlers() (*TemplateHandlers, error) {
	// Load templates
	tmpl, err := template.ParseGlob("web/templates/*.tmpl")
	if err != nil {
		return nil, fmt.Errorf("failed to parse templates: %w", err)
	}

	return &TemplateHandlers{
		templates: tmpl,
	}, nil
}

// RenderPage renders a page using the templates
func (h *TemplateHandlers) RenderPage(c *gin.Context) {
	// Get the page name from the URL path
	pagePath := c.Param("page")
	if pagePath == "" {
		pagePath = "index"
	}

	// Set default data
	data := PageData{
		Title:       "Great Nigeria",
		Description: "Transform Nigeria Through Knowledge, Community, and Action",
		CurrentYear: time.Now().Year(),
	}

	// Handle .html extension in the URL
	if pagePath != "index" && !strings.HasSuffix(pagePath, ".html") {
		pagePath = pagePath + ".html"
	}

	// Customize data based on page
	switch pagePath {
	case "index":
		data.Title = "Great Nigeria - Transform Nigeria Through Knowledge, Community, and Action"
		data.Description = "Join thousands of Nigerians working together to build a more prosperous, just, and united nation through practical, grassroots solutions."
	case "books.html":
		data.Title = "Books - Great Nigeria"
		data.Description = "Explore the Great Nigeria book series that provides comprehensive analysis, practical frameworks, and implementation tools for Nigeria's transformation."
	case "community.html":
		data.Title = "Community - Great Nigeria"
		data.Description = "Connect with thousands of Nigerians working together for positive transformation."
	case "about.html":
		data.Title = "About - Great Nigeria"
		data.Description = "Learn about our mission to transform Nigeria through citizen education, community building, and coordinated action."
	case "resources.html":
		data.Title = "Resources - Great Nigeria"
		data.Description = "Practical tools and frameworks to support your transformation initiatives."
	case "celebrate.html":
		data.Title = "Celebrate Nigeria - Great Nigeria"
		data.Description = "Explore and celebrate Nigeria's rich cultural heritage, traditions, and achievements."
	default:
		data.Title = fmt.Sprintf("%s - Great Nigeria", pagePath)
		data.Description = "Part of the Great Nigeria platform for national transformation."
	}

	// For now, we need to serve the static HTML files but using our templates for header/footer
	// We will load the static HTML content from the original HTML files
	var originalHtmlContent []byte
	var err error

	// Special handling for index page
	if pagePath == "index" {
		originalHtmlContent, err = os.ReadFile("web/static/index.html")
	} else {
		originalHtmlContent, err = os.ReadFile(fmt.Sprintf("web/static/%s", pagePath))
	}

	if err != nil {
		// If file doesn't exist, provide an error message
		c.AbortWithStatus(http.StatusNotFound)
		return
	}

	// Extract the content between <main> and </main>
	content := extractMainContent(string(originalHtmlContent))
	data.Content = template.HTML(content)

	// Render the template
	if err := h.templates.ExecuteTemplate(c.Writer, "page.tmpl", data); err != nil {
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
}

// Helper function to extract content between <main> and </main> tags
func extractMainContent(html string) string {
	startTag := "<main>"
	endTag := "</main>"

	startIndex := strings.Index(html, startTag)
	if startIndex == -1 {
		return "<div id=\"main-content\">Page content not found</div>"
	}

	startIndex += len(startTag)
	endIndex := strings.Index(html[startIndex:], endTag)

	if endIndex == -1 {
		return "<div id=\"main-content\">Page content not properly formatted</div>"
	}

	content := html[startIndex : startIndex+endIndex]

	// Make sure we have a main-content div
	if !strings.Contains(content, "id=\"main-content\"") {
		content = "<div id=\"main-content\">" + content + "</div>"
	}

	return content
}

// RegisterRoutes registers the template handler routes
func (h *TemplateHandlers) RegisterRoutes(router *gin.Engine) {
	// Serve the index page
	router.GET("/", func(c *gin.Context) {
		// For the index page
		data := PageData{
			Title:       "Great Nigeria - Transform Nigeria Through Knowledge, Community, and Action",
			Description: "Join thousands of Nigerians working together to build a more prosperous, just, and united nation through practical, grassroots solutions.",
			CurrentYear: time.Now().Year(),
		}

		// Load the content from index.html
		indexContent, err := os.ReadFile("web/static/index.html")
		if err != nil {
			c.AbortWithError(http.StatusInternalServerError, fmt.Errorf("failed to read index.html: %w", err))
			return
		}

		// Extract the content between <main> and </main>
		content := extractMainContent(string(indexContent))
		data.Content = template.HTML(content)

		// Render the template
		if err := h.templates.ExecuteTemplate(c.Writer, "page.tmpl", data); err != nil {
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
	})

	// Serve other pages, but exclude /celebrate which is handled by CelebrateTemplateHandlers
	router.GET("/:page", func(c *gin.Context) {
		// Skip /celebrate route as it's handled by CelebrateTemplateHandlers
		if c.Param("page") == "celebrate" {
			c.Next()
			return
		}
		h.RenderPage(c)
	})
}
