package models

import (
	"time"
)

// TipCategory represents the category of a tip
type TipCategory string

const (
	CategoryNavigation   TipCategory = "navigation"
	CategoryFeature      TipCategory = "feature"
	CategoryContent      TipCategory = "content"
	CategoryProductivity TipCategory = "productivity"
	CategoryLearning     TipCategory = "learning"
)

// TipTrigger represents the trigger condition for a tip
type TipTrigger string

const (
	TriggerPageView      TipTrigger = "page_view"
	TriggerTimeOnPage    TipTrigger = "time_on_page"
	TriggerAction        TipTrigger = "action"
	TriggerInactivity    TipTrigger = "inactivity"
	TriggerProgressLevel TipTrigger = "progress_level"
)

// TipPriority represents the priority level of a tip
type TipPriority int

const (
	PriorityLow    TipPriority = 1
	PriorityMedium TipPriority = 2
	PriorityHigh   TipPriority = 3
)

// Tip represents a contextual tip that can be shown to users
type Tip struct {
	ID          uint        `json:"id" gorm:"primaryKey"`
	Title       string      `json:"title" gorm:"size:100;not null"`
	Content     string      `json:"content" gorm:"size:500;not null"`
	Category    TipCategory `json:"category" gorm:"size:50;not null"`
	Trigger     TipTrigger  `json:"trigger" gorm:"size:50;not null"`
	TriggerData string      `json:"triggerData" gorm:"size:255"`
	Priority    TipPriority `json:"priority" gorm:"not null;default:2"`
	ImageURL    string      `json:"imageUrl" gorm:"size:255"`
	ActionURL   string      `json:"actionUrl" gorm:"size:255"`
	ActionText  string      `json:"actionText" gorm:"size:50"`
	Active      bool        `json:"active" gorm:"not null;default:true"`
	StartDate   *time.Time  `json:"startDate"`
	EndDate     *time.Time  `json:"endDate"`
	CreatedAt   time.Time   `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time   `json:"updatedAt" gorm:"autoUpdateTime"`
}

// UserTip represents a record of tips shown to a user
type UserTip struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"userId" gorm:"not null"`
	TipID     uint      `json:"tipId" gorm:"not null"`
	Viewed    bool      `json:"viewed" gorm:"not null;default:false"`
	Dismissed bool      `json:"dismissed" gorm:"not null;default:false"`
	Clicked   bool      `json:"clicked" gorm:"not null;default:false"`
	ViewedAt  time.Time `json:"viewedAt" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TipRule represents a rule for when to show a tip
type TipRule struct {
	ID           uint        `json:"id" gorm:"primaryKey"`
	TipID        uint        `json:"tipId" gorm:"not null"`
	ContextType  string      `json:"contextType" gorm:"size:50;not null"` // page, feature, content, etc.
	ContextValue string      `json:"contextValue" gorm:"size:255;not null"`
	Condition    string      `json:"condition" gorm:"size:255"`
	Priority     TipPriority `json:"priority" gorm:"not null;default:2"`
	CreatedAt    time.Time   `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time   `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TipRequest represents a request for contextual tips
type TipRequest struct {
	UserID      uint   `json:"userId"`
	ContextType string `json:"contextType"`
	ContextID   string `json:"contextId"`
	PageURL     string `json:"pageUrl"`
	Action      string `json:"action,omitempty"`
}

// TipResponse represents a response containing contextual tips
type TipResponse struct {
	Tips []Tip `json:"tips"`
}

// TipFeedback represents user feedback on a tip
type TipFeedback struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"userId" gorm:"not null"`
	TipID     uint      `json:"tipId" gorm:"not null"`
	Helpful   bool      `json:"helpful"`
	Feedback  string    `json:"feedback" gorm:"size:500"`
	CreatedAt time.Time `json:"createdAt" gorm:"autoCreateTime"`
}

// TipStatistics represents statistics for a tip
type TipStatistics struct {
	TipID         uint    `json:"tipId"`
	ViewCount     int     `json:"viewCount"`
	DismissCount  int     `json:"dismissCount"`
	ClickCount    int     `json:"clickCount"`
	HelpfulCount  int     `json:"helpfulCount"`
	EffectivenessRate float64 `json:"effectivenessRate"` // Calculated as (ClickCount / ViewCount) * 100
}
