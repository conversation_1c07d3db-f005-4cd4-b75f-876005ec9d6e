package service

import (
	"github.com/greatnigeria/internal/tips/models"
	"github.com/greatnigeria/internal/tips/repository"
	"github.com/sirupsen/logrus"
	"sort"
)

// TipsService defines the interface for tips business logic
type TipsService interface {
	// Tip methods
	GetTipByID(id uint) (*models.Tip, error)
	GetAllTips() ([]models.Tip, error)
	GetActiveTips() ([]models.Tip, error)
	GetTipsByCategory(category models.TipCategory) ([]models.Tip, error)
	CreateTip(tip *models.Tip) error
	UpdateTip(tip *models.Tip) error
	DeleteTip(id uint) error
	
	// TipRule methods
	GetTipRulesByTipID(tipID uint) ([]models.TipRule, error)
	CreateTipRule(rule *models.TipRule) error
	UpdateTipRule(rule *models.TipRule) error
	DeleteTipRule(id uint) error
	
	// UserTip methods
	RecordTipView(userID, tipID uint) error
	RecordTipDismiss(userID, tipID uint) error
	RecordTipClick(userID, tipID uint) error
	
	// TipFeedback methods
	SubmitTipFeedback(feedback *models.TipFeedback) error
	
	// Statistics methods
	GetTipStatistics(tipID uint) (*models.TipStatistics, error)
	GetAllTipStatistics() ([]models.TipStatistics, error)
	
	// Context-aware tip retrieval
	GetContextualTips(request *models.TipRequest) ([]models.Tip, error)
}

// DefaultTipsService implements TipsService
type DefaultTipsService struct {
	repo   repository.TipsRepository
	logger *logrus.Logger
}

// NewTipsService creates a new tips service
func NewTipsService(repo repository.TipsRepository, logger *logrus.Logger) TipsService {
	return &DefaultTipsService{
		repo:   repo,
		logger: logger,
	}
}

// GetTipByID retrieves a tip by its ID
func (s *DefaultTipsService) GetTipByID(id uint) (*models.Tip, error) {
	return s.repo.GetTipByID(id)
}

// GetAllTips retrieves all tips
func (s *DefaultTipsService) GetAllTips() ([]models.Tip, error) {
	return s.repo.GetAllTips()
}

// GetActiveTips retrieves all active tips
func (s *DefaultTipsService) GetActiveTips() ([]models.Tip, error) {
	return s.repo.GetActiveTips()
}

// GetTipsByCategory retrieves tips by category
func (s *DefaultTipsService) GetTipsByCategory(category models.TipCategory) ([]models.Tip, error) {
	return s.repo.GetTipsByCategory(category)
}

// CreateTip creates a new tip
func (s *DefaultTipsService) CreateTip(tip *models.Tip) error {
	return s.repo.CreateTip(tip)
}

// UpdateTip updates an existing tip
func (s *DefaultTipsService) UpdateTip(tip *models.Tip) error {
	return s.repo.UpdateTip(tip)
}

// DeleteTip deletes a tip by its ID
func (s *DefaultTipsService) DeleteTip(id uint) error {
	return s.repo.DeleteTip(id)
}

// GetTipRulesByTipID retrieves tip rules by tip ID
func (s *DefaultTipsService) GetTipRulesByTipID(tipID uint) ([]models.TipRule, error) {
	return s.repo.GetTipRulesByTipID(tipID)
}

// CreateTipRule creates a new tip rule
func (s *DefaultTipsService) CreateTipRule(rule *models.TipRule) error {
	return s.repo.CreateTipRule(rule)
}

// UpdateTipRule updates an existing tip rule
func (s *DefaultTipsService) UpdateTipRule(rule *models.TipRule) error {
	return s.repo.UpdateTipRule(rule)
}

// DeleteTipRule deletes a tip rule by its ID
func (s *DefaultTipsService) DeleteTipRule(id uint) error {
	return s.repo.DeleteTipRule(id)
}

// RecordTipView records that a user has viewed a tip
func (s *DefaultTipsService) RecordTipView(userID, tipID uint) error {
	// Check if a record already exists
	userTip, err := s.repo.GetUserTipByUserAndTipID(userID, tipID)
	if err == nil && userTip != nil {
		// Record already exists, update it
		userTip.Viewed = true
		return s.repo.UpdateUserTip(userTip)
	}
	
	// Create a new record
	userTip = &models.UserTip{
		UserID:  userID,
		TipID:   tipID,
		Viewed:  true,
	}
	return s.repo.CreateUserTip(userTip)
}

// RecordTipDismiss records that a user has dismissed a tip
func (s *DefaultTipsService) RecordTipDismiss(userID, tipID uint) error {
	// Check if a record already exists
	userTip, err := s.repo.GetUserTipByUserAndTipID(userID, tipID)
	if err == nil && userTip != nil {
		// Record already exists, update it
		userTip.Dismissed = true
		return s.repo.UpdateUserTip(userTip)
	}
	
	// Create a new record
	userTip = &models.UserTip{
		UserID:    userID,
		TipID:     tipID,
		Viewed:    true,
		Dismissed: true,
	}
	return s.repo.CreateUserTip(userTip)
}

// RecordTipClick records that a user has clicked a tip
func (s *DefaultTipsService) RecordTipClick(userID, tipID uint) error {
	// Check if a record already exists
	userTip, err := s.repo.GetUserTipByUserAndTipID(userID, tipID)
	if err == nil && userTip != nil {
		// Record already exists, update it
		userTip.Clicked = true
		return s.repo.UpdateUserTip(userTip)
	}
	
	// Create a new record
	userTip = &models.UserTip{
		UserID:  userID,
		TipID:   tipID,
		Viewed:  true,
		Clicked: true,
	}
	return s.repo.CreateUserTip(userTip)
}

// SubmitTipFeedback submits feedback for a tip
func (s *DefaultTipsService) SubmitTipFeedback(feedback *models.TipFeedback) error {
	return s.repo.CreateTipFeedback(feedback)
}

// GetTipStatistics retrieves statistics for a tip
func (s *DefaultTipsService) GetTipStatistics(tipID uint) (*models.TipStatistics, error) {
	return s.repo.GetTipStatistics(tipID)
}

// GetAllTipStatistics retrieves statistics for all tips
func (s *DefaultTipsService) GetAllTipStatistics() ([]models.TipStatistics, error) {
	return s.repo.GetAllTipStatistics()
}

// GetContextualTips retrieves tips based on context
func (s *DefaultTipsService) GetContextualTips(request *models.TipRequest) ([]models.Tip, error) {
	// Get tips based on context
	tips, err := s.repo.GetContextualTips(request)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get contextual tips")
		return nil, err
	}
	
	// Sort tips by priority (highest first)
	sort.Slice(tips, func(i, j int) bool {
		return tips[i].Priority > tips[j].Priority
	})
	
	// Limit to a reasonable number of tips (max 3)
	if len(tips) > 3 {
		tips = tips[:3]
	}
	
	// Record views for authenticated users
	if request.UserID > 0 {
		for _, tip := range tips {
			if err := s.RecordTipView(request.UserID, tip.ID); err != nil {
				s.logger.WithError(err).WithFields(logrus.Fields{
					"userID": request.UserID,
					"tipID":  tip.ID,
				}).Warn("Failed to record tip view")
			}
		}
	}
	
	return tips, nil
}
