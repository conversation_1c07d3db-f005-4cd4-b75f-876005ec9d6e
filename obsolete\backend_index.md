# Great Nigeria Library Backend Index

## Go Backend Structure

### Main Service Entry Points
- `/cmd/api-gateway/main.go` - API Gateway service entry point
- `/cmd/auth-service/main.go` - Authentication service entry point
- `/cmd/content-importer/main.go` - Content importer service
- `/cmd/content-service/main.go` - Content service entry point
- `/cmd/discussion-service/main.go` - Discussion service entry point
- `/cmd/livestream-service/main.go` - Livestream service entry point
- `/cmd/payment-service/main.go` - Payment service entry point
- `/cmd/points-service/main.go` - Points service entry point

### Internal Packages

#### Auth Package
- `/internal/auth/auth.go` - Main auth package file
- `/internal/auth/handlers/` - Auth handlers (multiple files)
- `/internal/auth/repository/` - Auth repositories (multiple files)
- `/internal/auth/service/` - Auth services (multiple files)

#### Celebration Package
- `/internal/celebrate/` - Celebration handlers and services
- `/internal/celebration/` - Celebration implementation files

#### Content Package
- `/internal/content/handlers/` - Content handlers (multiple files)
- `/internal/content/models/` - Content models (multiple files)
- `/internal/content/repository/` - Content repositories (multiple files)
- `/internal/content/service/` - Content services (multiple files)

#### Discussion Package
- `/internal/discussion/handlers/` - Discussion handlers (multiple files)
- `/internal/discussion/models/` - Discussion models (multiple files)
- `/internal/discussion/repository/` - Discussion repositories (multiple files)
- `/internal/discussion/service/` - Discussion services (multiple files)

#### Gateway Package
- `/internal/gateway/router.go` - API Gateway router

#### Gifts Package
- `/internal/gifts/handlers/` - Gift handlers
- `/internal/gifts/models/` - Gift models
- `/internal/gifts/repository/` - Gift repositories
- `/internal/gifts/service/` - Gift services

#### Livestream Package
- `/internal/livestream/handlers/` - Livestream handlers (multiple files)
- `/internal/livestream/models/` - Livestream models
- `/internal/livestream/repository/` - Livestream repositories (multiple files)
- `/internal/livestream/service/` - Livestream services (multiple files)

#### Payment Package
- `/internal/payment/api/` - Payment API
- `/internal/payment/handlers/` - Payment handlers (multiple files)
- `/internal/payment/models/` - Payment models (multiple files)
- `/internal/payment/repository/` - Payment repositories (multiple files)
- `/internal/payment/service/` - Payment services (multiple files)

#### Points Package
- `/internal/points/handlers/` - Points handlers (multiple files)
- `/internal/points/models/` - Points models
- `/internal/points/repository/` - Points repositories
- `/internal/points/service/` - Points services

#### Project Package
- `/internal/project/handlers/` - Project handlers
- `/internal/project/models/` - Project models
- `/internal/project/service/` - Project services

#### Report Package
- `/internal/report/handlers/` - Report handlers
- `/internal/report/models/` - Report models
- `/internal/report/service/` - Report services

#### Resource Package
- `/internal/resource/handlers/` - Resource handlers
- `/internal/resource/models/` - Resource models
- `/internal/resource/service/` - Resource services

#### Template Package
- `/internal/template/handlers/` - Template handlers (multiple files)

### Source Files (src directory)
- `/src/generators/README.md` - Generators documentation
- `/src/templates/README.md` - Templates documentation
- `/src/tests/README.md` - Tests documentation
- `/src/tools/README.md` - Tools documentation
- `/src/add_book2_chapter.go` - Book chapter addition
- `/src/batch_add_subsection.go` - Batch subsection addition
- `/src/book_section_template.go` - Book section template
- `/src/book1_content.go` - Book 1 content
- `/src/citation_management.go` - Citation management
- `/src/citation_tracker.go` - Citation tracker
- `/src/enhance_content_generator.go` - Content enhancement generator
- `/src/generate_content_outline.go` - Content outline generator
- `/src/README.md` - Source directory documentation
- `/src/section_content_generator.go` - Section content generator
- `/src/section_template_generator.go` - Section template generator
- `/src/section_template.go` - Section template
- `/src/serve_frontend.go` - Frontend server
- `/src/test_db_connection.go` - Database connection test
- `/src/test_web_server.go` - Web server test
- `/src/test.go` - Test file

### Web Static Files
- `/web/static/js/accessibility.js` - Accessibility JavaScript
- `/web/static/js/celebration/` - Celebration JavaScript files (multiple)
- `/web/static/js/feedback.js` - Feedback JavaScript
- `/web/static/js/main.js` - Main JavaScript file

### Scripts
- `/scripts/export_entries.go` - Export entries script
- `/scripts/ORGANIZATION_SCRIPT.md` - Organization script documentation
- `/scripts/populate_celebration.go` - Populate celebration script
- `/scripts/query_celebration.go` - Query celebration script
- `/scripts/update_entry_status.go` - Update entry status script

### Other Go Files
- `/serve_static.go` - Static file server

## Documentation Files

### Main Documentation
- `/README.md` - Main project README
- `/AI_AGENT_INSTRUCTIONS.md` - AI agent instructions
- `/database/README.md` - Database documentation

### Documentation Directory
- `/docs/README.md` - Documentation overview
- `/docs/SERVER_SETUP_GUIDE.md` - Server setup guide

#### Admin Documentation
- `/docs/admin/CELEBRATE_NIGERIA_ADMIN.md` - Admin documentation

#### API Documentation
- `/docs/api/API_DOCUMENTATION_*.md` - API documentation files
- `/docs/api/README.md` - API documentation overview

#### Architecture Documentation
- `/docs/architecture/ARCHITECTURE_*.md` - Architecture documentation files
- `/docs/architecture/FRONTEND_ARCHITECTURE.md` - Frontend architecture
- `/docs/architecture/README.md` - Architecture documentation overview

#### Code Documentation
- `/docs/code/CELEBRATE_NIGERIA_CODE.md` - Code documentation
- `/docs/code/CODE_ANALYSIS_*.md` - Code analysis files
- `/docs/code/README.md` - Code documentation overview

#### Content Documentation
- `/docs/content/BOOK_STRUCTURE.md` - Book structure
- `/docs/content/CONTENT_*.md` - Content documentation files
- `/docs/content/IMPROVED_*.md` - Improved content files
- `/docs/content/PAGE_CONTENT.md` - Page content
- `/docs/content/PAGE_ELEMENTS.md` - Page elements
- `/docs/content/README.md` - Content documentation overview

#### Database Documentation
- `/docs/database/DATABASE_*.md` - Database documentation files
- `/docs/database/README.md` - Database documentation overview

#### Design Documentation
- `/docs/design/DESIGN_GUIDELINES_*.md` - Design guidelines
- `/docs/design/README.md` - Design documentation overview

#### Development Documentation
- `/docs/development/DEVELOPMENT_GUIDE.md` - Development guide
- `/docs/development/README.md` - Development documentation overview
- `/docs/development/SETUP_GUIDE.md` - Setup guide

#### Features Documentation
- `/docs/features/CELEBRATE_NIGERIA_FEATURES.md` - Features documentation
- `/docs/features/FEATURE_*.md` - Feature documentation files
- `/docs/features/README.md` - Features documentation overview

#### Implementation Documentation
- `/docs/implementation/CELEBRATE_*.md` - Implementation documentation files
- `/docs/implementation/README.md` - Implementation documentation overview

#### Livestream Documentation
- `/docs/livestream/README.md` - Livestream documentation

#### Project Documentation
- `/docs/project/API_ENDPOINTS.md` - API endpoints
- `/docs/project/BACKEND_*.md` - Backend documentation files
- `/docs/project/CELEBRATE_*.md` - Project celebration files
- `/docs/project/COMPREHENSIVE_*.md` - Comprehensive documentation files
- `/docs/project/CORS_CONFIG.md` - CORS configuration
- `/docs/project/FRONTEND_*.md` - Frontend documentation files
- `/docs/project/GO_BACKEND_*.md` - Go backend documentation
- `/docs/project/IMPLEMENTATION_*.md` - Implementation documentation files
- `/docs/project/PAGE_ELEMENTS.md` - Page elements
- `/docs/project/PROJECT_*.md` - Project documentation files
- `/docs/project/REACT_*.md` - React documentation files
- `/docs/project/README.md` - Project documentation overview
- `/docs/project/REMAINING_*.md` - Remaining tasks
- `/docs/project/TASK_LIST_*.md` - Task lists
- `/docs/project/UPDATED_*.md` - Updated documentation files

#### Reference Documentation
- `/docs/reference/citation_*.md` - Citation reference files
- `/docs/reference/README.md` - Reference documentation overview

#### User Documentation
- `/docs/user/CELEBRATE_NIGERIA_USER.md` - User documentation

#### Website Documentation
- `/docs/website/README.md` - Website documentation overview
- `/docs/website/WEBSITE_*.md` - Website documentation files

## Obsolete Files
- Various obsolete files in `/obsolete/` directory (not listed individually)
