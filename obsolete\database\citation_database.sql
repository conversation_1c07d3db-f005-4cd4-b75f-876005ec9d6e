-- SQL Schema for Citations Database

-- Table for storing citations
CREATE TABLE IF NOT EXISTS citations (
    id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL,
    citation_key VARCHAR(100) NOT NULL,
    ref_number INTEGER NOT NULL,
    author TEXT NOT NULL,
    year VARCHAR(20) NOT NULL,
    title TEXT NOT NULL,
    source TEXT NOT NULL,
    url TEXT,
    type VARCHAR(50) NOT NULL,
    cited_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(book_id, citation_key)
);

-- Table for tracking where each citation is used
CREATE TABLE IF NOT EXISTS citation_usages (
    id SERIAL PRIMARY KEY,
    citation_id INTEGER NOT NULL REFERENCES citations(id),
    book_id INTEGER NOT NULL,
    chapter_id INTEGER NOT NULL,
    section_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(citation_id, book_id, chapter_id, section_id)
);

-- Table for storing bibliography metadata
CREATE TABLE IF NOT EXISTS bibliographies (
    id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL UNIQUE,
    title TEXT NOT NULL,
    description TEXT,
    last_generated TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for tracking citation categories
CREATE TABLE IF NOT EXISTS citation_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name)
);

-- Insert default citation categories with display order
INSERT INTO citation_categories (name, description, display_order)
VALUES 
    ('book', 'Academic books and monographs', 1),
    ('journal', 'Academic journal articles', 2),
    ('report', 'Research reports and working papers', 3),
    ('government', 'Government and institutional publications', 4),
    ('interview', 'Field interviews and focus groups', 5),
    ('survey', 'Surveys and statistical data', 6),
    ('media', 'Media and online resources', 7)
ON CONFLICT (name) DO NOTHING;

-- Create index for efficient queries
CREATE INDEX IF NOT EXISTS idx_citations_book_id ON citations(book_id);
CREATE INDEX IF NOT EXISTS idx_citations_type ON citations(type);
CREATE INDEX IF NOT EXISTS idx_citation_usages_citation_id ON citation_usages(citation_id);
CREATE INDEX IF NOT EXISTS idx_citation_usages_book_chapter_section ON citation_usages(book_id, chapter_id, section_id);

-- Sample SQL functions for citation management

-- Function to add a new citation
CREATE OR REPLACE FUNCTION add_citation(
    p_book_id INTEGER,
    p_citation_key VARCHAR(100),
    p_ref_number INTEGER,
    p_author TEXT,
    p_year VARCHAR(20),
    p_title TEXT,
    p_source TEXT,
    p_url TEXT,
    p_type VARCHAR(50)
) RETURNS INTEGER AS $$
DECLARE
    v_citation_id INTEGER;
BEGIN
    INSERT INTO citations (
        book_id, citation_key, ref_number, author, year, title, source, url, type
    ) VALUES (
        p_book_id, p_citation_key, p_ref_number, p_author, p_year, p_title, p_source, p_url, p_type
    )
    ON CONFLICT (book_id, citation_key) DO UPDATE
    SET ref_number = p_ref_number,
        author = p_author,
        year = p_year,
        title = p_title,
        source = p_source,
        url = p_url,
        type = p_type,
        updated_at = CURRENT_TIMESTAMP
    RETURNING id INTO v_citation_id;
    
    RETURN v_citation_id;
END;
$$ LANGUAGE plpgsql;

-- Function to record citation usage
CREATE OR REPLACE FUNCTION use_citation(
    p_citation_id INTEGER,
    p_book_id INTEGER,
    p_chapter_id INTEGER,
    p_section_id INTEGER
) RETURNS VOID AS $$
BEGIN
    -- Record usage
    INSERT INTO citation_usages (
        citation_id, book_id, chapter_id, section_id
    ) VALUES (
        p_citation_id, p_book_id, p_chapter_id, p_section_id
    )
    ON CONFLICT (citation_id, book_id, chapter_id, section_id) DO NOTHING;
    
    -- Update citation count
    UPDATE citations
    SET cited_count = cited_count + 1,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_citation_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get all citations for a book (ordered by ref_number)
CREATE OR REPLACE FUNCTION get_book_citations(p_book_id INTEGER)
RETURNS TABLE (
    id INTEGER,
    citation_key VARCHAR(100),
    ref_number INTEGER,
    author TEXT,
    year VARCHAR(20),
    title TEXT,
    source TEXT,
    url TEXT,
    type VARCHAR(50),
    cited_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT c.id, c.citation_key, c.ref_number, c.author, c.year, c.title, c.source, c.url, c.type, c.cited_count
    FROM citations c
    WHERE c.book_id = p_book_id
    ORDER BY c.ref_number;
END;
$$ LANGUAGE plpgsql;

-- Function to get citations by type for a book
CREATE OR REPLACE FUNCTION get_citations_by_type(p_book_id INTEGER, p_type VARCHAR(50))
RETURNS TABLE (
    id INTEGER,
    citation_key VARCHAR(100),
    ref_number INTEGER,
    author TEXT,
    year VARCHAR(20),
    title TEXT,
    source TEXT,
    url TEXT,
    cited_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT c.id, c.citation_key, c.ref_number, c.author, c.year, c.title, c.source, c.url, c.cited_count
    FROM citations c
    WHERE c.book_id = p_book_id AND c.type = p_type
    ORDER BY c.author, c.year;
END;
$$ LANGUAGE plpgsql;

-- Function to get citation usage stats
CREATE OR REPLACE FUNCTION get_citation_usage_stats(p_book_id INTEGER)
RETURNS TABLE (
    type VARCHAR(50),
    count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT c.type, COUNT(c.id)
    FROM citations c
    WHERE c.book_id = p_book_id
    GROUP BY c.type
    ORDER BY COUNT(c.id) DESC;
END;
$$ LANGUAGE plpgsql;

-- Example SQL queries for working with citations

-- Get all citations for book 3
-- SELECT * FROM get_book_citations(3);

-- Get all book citations for book 2
-- SELECT * FROM get_citations_by_type(2, 'book');

-- Get citation usage stats for book 1
-- SELECT * FROM get_citation_usage_stats(1);

-- Get most cited sources across all books
-- SELECT c.author, c.title, c.cited_count
-- FROM citations c
-- ORDER BY c.cited_count DESC
-- LIMIT 10;

-- Get all sections that cite a specific source
-- SELECT cu.book_id, cu.chapter_id, cu.section_id
-- FROM citation_usages cu
-- JOIN citations c ON cu.citation_id = c.id
-- WHERE c.citation_key = 'achebe1983'
-- ORDER BY cu.book_id, cu.chapter_id, cu.section_id;