# Great Nigeria Frontend Index

## React Frontend Structure

### Main Application Files
- `/src/App.tsx` - Main application component
- `/src/index.tsx` - Application entry point
- `/src/redux.d.ts` - Redux type definitions
- `/src/reportWebVitals.ts` - Web vitals reporting
- `/.eslintrc.js` - ESLint configuration

### API Services
- `/src/api/affiliateService.ts` - Affiliate API service
- `/src/api/authService.ts` - Authentication API service
- `/src/api/badgeService.ts` - Badge API service
- `/src/api/bookService.ts` - Book API service
- `/src/api/celebrateService.ts` - Celebration API service
- `/src/api/client.ts` - API client configuration
- `/src/api/escrowService.ts` - Escrow API service
- `/src/api/featuresService.ts` - Features API service
- `/src/api/forumService.ts` - Forum API service
- `/src/api/index.ts` - API services index
- `/src/api/livestreamService.ts` - Livestream API service
- `/src/api/marketplaceService.ts` - Marketplace API service
- `/src/api/resourceService.ts` - Resource API service
- `/src/api/userService.ts` - User API service
- `/src/api/walletService.ts` - Wallet API service

### Components

#### Affiliate Components
- `/src/components/affiliate/AffiliateCommissionSettings.tsx`
- `/src/components/affiliate/AffiliateEarningsChart.tsx`
- `/src/components/affiliate/AffiliateLinks.tsx`
- `/src/components/affiliate/AffiliatePerformance.tsx`
- `/src/components/affiliate/AffiliateReferralList.tsx`
- `/src/components/affiliate/AffiliateSettings.tsx`
- `/src/components/affiliate/AffiliateStats.tsx`

#### Escrow Components
- `/src/components/escrow/EscrowDetails.tsx`
- `/src/components/escrow/EscrowTransactionList.tsx`

#### Feature Components
- `/src/components/features/FeatureToggle.tsx`
- `/src/components/features/FeaturesList.tsx`

#### Livestream Components
- `/src/components/livestream/LivestreamChat.tsx`
- `/src/components/livestream/LivestreamComments.tsx`
- `/src/components/livestream/LivestreamControls.tsx`
- `/src/components/livestream/LivestreamCreator.tsx`
- `/src/components/livestream/LivestreamDetails.tsx`
- `/src/components/livestream/LivestreamList.tsx`
- `/src/components/livestream/LivestreamPlayer.tsx`
- `/src/components/livestream/LivestreamPreview.tsx`
- `/src/components/livestream/LivestreamSchedule.tsx`
- `/src/components/livestream/LivestreamSettings.tsx`
- `/src/components/livestream/LivestreamStats.tsx`
- `/src/components/livestream/LivestreamThumbnail.tsx`
- `/src/components/livestream/LivestreamTips.tsx`
- `/src/components/livestream/LivestreamViewer.tsx`
- `/src/components/livestream/StreamerDashboard.tsx`
- `/src/components/livestream/StreamerProfile.tsx`
- `/src/components/livestream/StreamerSettings.tsx`
- `/src/components/livestream/StreamerStats.tsx`

#### Marketplace Components
- `/src/components/marketplace/MarketplaceFilters.tsx`
- `/src/components/marketplace/MarketplaceItem.tsx`
- `/src/components/marketplace/ProductDetails.tsx`

#### Profile Components
- `/src/components/profile/ProfileBadges.tsx`
- `/src/components/profile/ProfileHeader.tsx`
- `/src/components/profile/ProfileStats.tsx`

#### Tipping Components
- `/src/components/tipping/TipCreator.tsx`
- `/src/components/tipping/TippingHistory.tsx`

#### Other Components
- `/src/components/Footer.tsx`
- `/src/components/Header.tsx`
- `/src/components/PrivateRoute.tsx`

### Redux Features
- `/src/features/auth/authSlice.ts`
- `/src/features/badges/badgesSlice.ts`
- `/src/features/books/booksSlice.ts`
- `/src/features/celebrate/celebrateSlice.ts`
- `/src/features/escrow/escrowSlice.ts`
- `/src/features/features/featuresSlice.ts`
- `/src/features/forum/forumSlice.ts`
- `/src/features/livestream/livestreamSlice.ts`
- `/src/features/marketplace/marketplaceSlice.ts`
- `/src/features/profile/profileSlice.ts`
- `/src/features/resources/resourcesSlice.ts`
- `/src/features/wallet/walletSlice.ts`

### Hooks
- `/src/hooks/useWebSocket.ts`

### Layouts
- `/src/layouts/MainLayout.tsx`

### Pages
- `/src/pages/AboutPage.tsx`
- `/src/pages/AffiliatePage.tsx`
- `/src/pages/BadgesPage.tsx`
- `/src/pages/BookListPage.tsx`
- `/src/pages/BookViewPage.tsx`
- `/src/pages/CelebratePage.tsx`
- `/src/pages/CelebrationsListPage.tsx`
- `/src/pages/ContactPage.tsx`
- `/src/pages/CreatorRevenuePage.tsx`
- `/src/pages/DisputeDetailsPage.tsx`
- `/src/pages/EnhancedContentPage.tsx`
- `/src/pages/EscrowPage.tsx`
- `/src/pages/ForumPage.tsx`
- `/src/pages/ForumTopicPage.tsx`
- `/src/pages/HomePage.tsx`
- `/src/pages/LivestreamPage.tsx`
- `/src/pages/LivestreamsListPage.tsx`
- `/src/pages/LoginPage.tsx`
- `/src/pages/MarketplacePage.tsx`
- `/src/pages/NotFoundPage.tsx`
- `/src/pages/ProfilePage.tsx`
- `/src/pages/RegisterPage.tsx`
- `/src/pages/ResourcesPage.tsx`
- `/src/pages/StreamDashboardPage.tsx`
- `/src/pages/StreamerAnalyticsPage.tsx`
- `/src/pages/StreamerSettingsPage.tsx`
- `/src/pages/VirtualLibraryPage.tsx`
- `/src/pages/WalletPage.tsx`

### Store
- `/src/store/index.ts`

### Types
- `/src/types/index.ts`

### Utils
- `/src/utils/markdownParser.ts`
