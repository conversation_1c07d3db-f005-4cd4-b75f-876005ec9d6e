# Great Nigeria Platform - Architecture Overview

This document provides a comprehensive overview of the Great Nigeria platform's architecture, including the microservices design, scalability approach, data management, and implementation details.

## Table of Contents

1. [Architecture Fundamentals](#architecture-fundamentals)
2. [Microservices Architecture](#microservices-architecture)
3. [Core Services](#core-services)
4. [Data Architecture](#data-architecture)
5. [Scalability Strategy](#scalability-strategy)
6. [Security Architecture](#security-architecture)
7. [Deployment Architecture](#deployment-architecture)
8. [Monitoring & Observability](#monitoring--observability)
9. [Migration Strategy](#migration-strategy)
10. [Implementation Timeline](#implementation-timeline)

## Architecture Fundamentals

### Key Architectural Principles

The Great Nigeria platform's architecture is built on these foundational principles:

1. **Microservices Design**: Modular, independently deployable services
2. **Horizontal Scalability**: Services can scale out across multiple instances
3. **Statelessness**: Core services maintain no local state, allowing for easy replication
4. **Asynchronous Processing**: Non-critical operations are handled asynchronously
5. **Isolation of Failure Domains**: Issues in one service won't cascade to others
6. **Distributed Data Management**: Data is partitioned and replicated appropriately

### Go's Advantages for Our Architecture

Go provides several key advantages that make it ideal for our requirements:

1. **Efficient Resource Utilization**:
   - Low memory footprint (typically 2-5MB per instance vs. 50-100MB for JVM-based services)
   - Efficient garbage collection with sub-millisecond pauses
   - Direct compilation to machine code for optimal CPU usage

2. **Concurrency Model**:
   - Goroutines are lightweight (2KB initial stack) compared to OS threads
   - Can efficiently run hundreds of thousands of concurrent goroutines on modest hardware
   - Built-in channels for safe communication between goroutines

3. **Fast Startup Time**:
   - Services initialize in milliseconds, enabling rapid scaling
   - Quick recovery from failures
   - Efficient container orchestration

4. **Standard Library Strength**:
   - High-performance networking stack
   - Efficient HTTP implementation
   - Built-in profiling and diagnostic tools

## Microservices Architecture

The Great Nigeria platform adopts a microservices architecture with Go as the primary backend language. This architecture provides better scalability, maintainability, and resilience.

### System Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                         Client Applications                      │
│    (React Web App, Mobile Web, Progressive Web App, Native)      │
└───────────────────────────────┬─────────────────────────────────┘
                                │
┌───────────────────────────────▼─────────────────────────────────┐
│                            API Gateway                           │
│     (Authentication, Routing, Rate Limiting, Load Balancing)     │
└─┬──────────┬──────────┬──────────┬──────────┬──────────┬────────┘
  │          │          │          │          │          │
┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐
│  Auth  │ │ User   │ │Content │ │ Social │ │ Market │ │Payment │
│ Service│ │Service │ │Service │ │Service │ │Service │ │Service │
└────────┘ └────────┘ └────────┘ └────────┘ └────────┘ └────────┘
  │          │          │          │          │          │
  │          │          │          │          │          │
┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐ ┌─▼──────┐
│Analytics│ │  Chat  │ │Streaming│ │Rewards │ │ Search │ │Notif.  │
│Service │ │Service │ │Service  │ │Service │ │Service │ │Service │
└────────┘ └────────┘ └────────┘ └────────┘ └────────┘ └────────┘
            │                                            │
┌───────────▼────────────────────────────────────────────▼──────┐
│                     Event Bus / Message Queue                  │
│               (NATS, RabbitMQ, or Google Pub/Sub)              │
└─────────────────────────────┬─────────────────────────────────┘
                              │
┌─────────────────────────────▼─────────────────────────────────┐
│                         Data Storage                           │
│     (PostgreSQL, Redis, Object Storage, Time Series DB)        │
└─────────────────────────────────────────────────────────────────┘
```

### API Gateway

The API Gateway serves as the entry point for all client requests, providing:

- Routing to appropriate microservices
- Authentication and authorization
- Request/response transformation
- Rate limiting and throttling
- API documentation (Swagger)

```go
// Example API Gateway route configuration in Go/Gin
func setupRoutes(r *gin.Engine) {
    // Public routes
    r.GET("/api/health", HealthCheck)
    r.GET("/api/books", ForwardToContentService)
    r.GET("/api/books/:id", ForwardToContentService)
    
    // Authentication routes
    r.POST("/api/register", ForwardToAuthService)
    r.POST("/api/login", ForwardToAuthService)
    r.POST("/api/logout", ForwardToAuthService)
    
    // Protected routes - require authentication
    authorized := r.Group("/api")
    authorized.Use(AuthMiddleware())
    {
        authorized.GET("/user", ForwardToAuthService)
        authorized.GET("/user/progress", ForwardToContentService)
        authorized.POST("/discussions", ForwardToDiscussionService)
        authorized.GET("/points", ForwardToPointsService)
        authorized.POST("/payments", ForwardToPaymentService)
    }
}
```

### Common Packages

Shared functionality across services:

#### Database
- Connection management with retry logic
- GORM integration
- Migration utilities
- Transaction support

#### Middleware
- Authentication
- Logging
- Rate limiting
- CORS handling
- Request validation

#### Models
- Shared domain models
- Data transfer objects (DTOs)
- Validation schemas

#### Utils
- Logging
- Error handling
- Date/time utilities
- Security functions

## Core Services

The application is divided into the following microservices:

### Auth Service

**Responsibilities**:
- User registration and authentication
- JWT token generation and validation
- Password management
- OAuth/social login integration
- Two-factor authentication
- Session management
- Permission validation

**API Endpoints**:
- POST /auth/register
- POST /auth/login
- POST /auth/refresh-token
- POST /auth/password/reset
- POST /auth/logout
- GET /auth/oauth/{provider}
- POST /auth/2fa/enable
- POST /auth/2fa/validate

### User Service

**Responsibilities**:
- User profile management
- User preferences and settings
- Friendship and follow relationships
- User activity tracking
- Feature toggle management
- Membership tiers
- Account management

**API Endpoints**:
- GET /users/{id}
- PATCH /users/{id}
- GET /users/{id}/profile
- GET /users/{id}/friends
- POST /users/{id}/friends/request
- GET /users/{id}/followers
- POST /users/{id}/follow
- GET /users/{id}/features
- PATCH /users/{id}/features/{featureId}

### Content Service

**Responsibilities**:
- Book content management
- Chapter/section organization
- Content access control
- Progress tracking
- Bookmarks and notes
- Media management
- Content versioning

**API Endpoints**:
- GET /books
- GET /books/{id}
- GET /books/{id}/chapters
- GET /books/{id}/chapters/{chapterId}
- GET /books/{id}/sections/{sectionId}
- POST /books/{id}/progress
- POST /books/{id}/bookmarks
- POST /books/{id}/notes

### Social Service

**Responsibilities**:
- Timeline and feed management
- Post creation and management
- Comments and reactions
- @mentions and #hashtags
- Content sharing
- Groups and pages
- Discover and trending

**API Endpoints**:
- GET /feed
- POST /posts
- GET /posts/{id}
- PATCH /posts/{id}
- DELETE /posts/{id}
- POST /posts/{id}/comments
- POST /posts/{id}/reactions
- GET /groups
- POST /groups
- GET /groups/{id}/posts

### Discussion Service

**Responsibilities**:
- Forum topics and discussions
- Comments and replies
- Moderation capabilities
- Notification triggers

### Points Service

**Responsibilities**:
- Points calculation and assignment
- User activity tracking
- Membership level management
- Achievement and badge system

### Payment Service

**Responsibilities**:
- Payment processing (Paystack, Flutterwave, Squad)
- Transaction management
- Digital wallet operations
- Subscription management
- Financial reporting
- Invoice generation
- Payout management

**API Endpoints**:
- POST /payments/intent
- POST /payments/process
- GET /payments/transactions
- GET /wallet/balance
- POST /wallet/deposit
- POST /wallet/withdraw
- GET /subscriptions
- POST /subscriptions

### Additional Services

The platform includes several other specialized services:

- **Analytics Service**: User behavior tracking, content performance metrics
- **Chat Service**: Real-time messaging, group chat management
- **Streaming Service**: Live video streaming, RTMP/WebRTC handling
- **Rewards Service**: Points system, achievements, leaderboards
- **Search Service**: Full-text search, recommendations
- **Notification Service**: In-app, push, email notifications

## Data Architecture

### Database Design

PostgreSQL serves as the primary database with the following design principles:
- Normalized schema for relational data
- Optimized indexes for common query patterns
- JSON columns for flexible, schema-less data where appropriate
- Foreign key constraints for data integrity

### Database Schema (Simplified)

**Users & Authentication**:
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active',
    membership_tier INTEGER DEFAULT 1
);

CREATE TABLE profiles (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    display_name VARCHAR(100),
    bio TEXT,
    avatar_url VARCHAR(255),
    cover_url VARCHAR(255),
    location VARCHAR(100),
    website VARCHAR(255),
    phone VARCHAR(20),
    points INTEGER DEFAULT 0,
    enabled_features JSONB DEFAULT '{}'::JSONB
);
```

**Content & Books**:
```sql
CREATE TABLE books (
    id UUID PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    cover_image_url VARCHAR(255),
    publish_date TIMESTAMP WITH TIME ZONE,
    access_level INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE chapters (
    id UUID PRIMARY KEY,
    book_id UUID REFERENCES books(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    order_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE sections (
    id UUID PRIMARY KEY,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    order_number INTEGER NOT NULL,
    points_reward INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Social & Engagement**:
```sql
CREATE TABLE posts (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT,
    media_urls JSONB DEFAULT '[]'::JSONB,
    location JSONB,
    feeling VARCHAR(50),
    activity VARCHAR(100),
    privacy VARCHAR(20) DEFAULT 'public',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE comments (
    id UUID PRIMARY KEY,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE reactions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    target_type VARCHAR(20) NOT NULL,
    target_id UUID NOT NULL,
    reaction_type VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Primary Datastores

**PostgreSQL**:
- Relational data with complex relationships
- Transactional data
- User and content data
- JSONB for semi-structured data

**Redis**:
- Caching layer
- Session storage
- Rate limiting
- Leaderboards
- Pub/Sub for real-time features

**Object Storage (S3-compatible)**:
- Media storage (images, videos, documents)
- Backup storage
- Static asset hosting
- Content delivery integration

**Time Series Database (InfluxDB)**:
- Metrics and telemetry
- Performance monitoring
- User activity trends
- Real-time analytics

### Cross-Service Communication

Services communicate with each other using:
1. **HTTP/REST**: For synchronous requests
2. **Message Queue**: For asynchronous communication, using NATS or RabbitMQ

## Scalability Strategy

### Service-Level Scalability

#### API Gateway Scaling

1. **Load Balancing Strategy**:
   - Layer 7 load balancing with Nginx or Traefik
   - Consistent hashing for routing to minimize cache invalidation
   - Health checking with automatic instance removal

2. **Rate Limiting Implementation**:
   - Token bucket algorithm with Redis backend
   - Tiered rate limits (by user tier, by IP, by endpoint)
   - Graceful limiting with retry headers

3. **Gateway Autoscaling**:
   - Scale based on request rate and latency metrics
   - Predictive scaling based on historical patterns
   - Minimum instance guarantees with burst capacity

#### Core Service Scaling

Each core service implements these scalability patterns:

1. **Instance Management**:
   - Multiple identical instances behind internal load balancers
   - No local state - all state in distributed datastores
   - Independent scaling based on service-specific metrics

2. **Connection Pooling**:
   - Optimized database connection pools
   - Backpressure mechanisms to prevent overload
   - Circuit breakers for dependent service failures

3. **Request Processing**:
   - Non-blocking I/O throughout
   - Goroutine per request model
   - Context propagation for deadlines and cancellation

### Data Layer Scalability

#### Database Scaling Strategies

1. **Read/Write Separation**:
   - Primary instance for writes
   - Multiple read replicas for queries
   - Intelligent routing based on query type

2. **Horizontal Partitioning (Sharding)**:
   - User data sharded by user ID
   - Content sharded by content type and ID
   - Activity data sharded by time periods

3. **Query Optimization**:
   - Denormalization of frequently accessed data
   - Materialized views for complex aggregations
   - Covering indexes for common query patterns

#### Caching Architecture

Multi-level caching dramatically reduces database load:

1. **Cache Hierarchy**:
   - L1: In-memory service cache (using Go sync.Map or similar)
   - L2: Distributed Redis cache clusters
   - L3: Database result caching

2. **Caching Strategies**:
   - Write-through for critical data
   - Cache-aside for read-heavy data
   - Time-based expiration with stale-while-revalidate

3. **Cache Coherence**:
   - Event-based cache invalidation
   - Version-tagged cache keys
   - Graceful degradation during invalidation storms

### Performance Optimization Techniques

Specific techniques for maximizing Go performance:

1. **Memory Management**:
   - Object pooling for frequent allocations
   - Pre-allocation for known-size collections
   - Careful use of string concatenation and conversions

2. **Concurrency Control**:
   - Worker pools with optimal size (typically NumCPU)
   - Context propagation for cancellation
   - Proper error handling and resource cleanup

3. **Network Optimization**:
   - Keep-alive connections with optimal timeouts
   - Connection pooling for all external services
   - Protocol buffers for internal service communication

### Capacity Planning Guidelines

Guidelines for planning infrastructure needs:

1. **User-Based Estimation**:
   - 100,000 registered users: 3-5 instances per core service
   - 1,000,000 registered users: 10-15 instances per core service
   - 10,000,000 registered users: 30-50 instances per core service, with database sharding

2. **Activity-Based Estimation**:
   - 10 requests/second: Single instance of each service
   - 100 requests/second: 3 instances of each service
   - 1,000 requests/second: 10 instances with enhanced caching
   - 10,000+ requests/second: Full distributed architecture with CDN

## Security Architecture

### Authentication & Authorization

- JWT-based authentication
- Role-based access control
- Secure password hashing (bcrypt)
- Two-factor authentication (optional)

**JWT-Based Auth**:
- Authentication header: `Authorization: Bearer {token}`
- Token structure: Header.Payload.Signature
- Claims: `sub`, `exp`, `iat`, `roles`, `permissions`
- Refresh token mechanism

**API Key Auth (for services)**:
- API key header: `X-API-Key: {api_key}`
- Rate limiting by key
- Permission scoping

### Data Security

- HTTPS for all communications
- Encryption of sensitive data
- Input validation and sanitization
- Protection against common attacks (CSRF, XSS, SQL Injection)

**Encryption**:
- Data encryption at rest
- TLS for data in transit
- Encryption key management
- Sensitive data handling

**Access Control**:
- Role-based access control
- Attribute-based access control
- Least privilege principle
- Regular access review

### Security Practices

**Secure Development**:
- Dependency scanning
- Static code analysis
- Dynamic application security testing
- Security code review

**Operational Security**:
- Regular security patching
- Network segmentation
- Intrusion detection
- Security monitoring

## Deployment Architecture

### Containerization

All services are containerized using Docker:
- Each microservice has its own Dockerfile
- Shared base image for common dependencies
- Multi-stage builds for optimized image size

**Docker**:
- Base image: `golang:1.20-alpine`
- Multi-stage builds
- Minimal final images
- Health check configuration
- Environment variable configuration

### Orchestration

Kubernetes is recommended for production deployments:
- Service discovery and load balancing
- Horizontal scaling
- Self-healing capabilities
- Configuration management

**Kubernetes Resources**:
- Deployments for stateless services
- StatefulSets for stateful services
- Services for service discovery
- Ingress for external access
- ConfigMaps and Secrets for configuration

### Simplified Deployment for Replit

For Replit deployment, a simplified setup is used:
- All services compiled to binaries
- Single entry point script to launch all services
- Environment-based configuration
- Logging to files for debugging

```bash
#!/bin/bash
# start.sh - Launches all Great Nigeria microservices

# Launch API Gateway
./bin/api-gateway &

# Launch services
./bin/auth-service &
./bin/content-service &
./bin/discussion-service &
./bin/points-service &
./bin/payment-service &

# Launch frontend
cd client && npm run build && npm run serve
```

### Environment Configuration

Configuration is managed through environment variables:
- Database connection string
- JWT secret
- API keys for payment providers
- Service ports and URLs
- Logging levels

Example `.env` file structure:
```
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/greatnigeria
DATABASE_MAX_CONNS=20
DATABASE_MAX_IDLE=5

# Security
JWT_SECRET=your-secret-key
JWT_EXPIRY=72h

# Services
AUTH_SERVICE_URL=http://localhost:8081
CONTENT_SERVICE_URL=http://localhost:8082
DISCUSSION_SERVICE_URL=http://localhost:8083
POINTS_SERVICE_URL=http://localhost:8084
PAYMENT_SERVICE_URL=http://localhost:8085

# Payment Providers
PAYSTACK_SECRET_KEY=sk_xxxx
PAYSTACK_PUBLIC_KEY=pk_xxxx
FLUTTERWAVE_SECRET_KEY=xxxx
FLUTTERWAVE_PUBLIC_KEY=xxxx
SQUAD_SECRET_KEY=xxxx
```

## Monitoring & Observability

### Logging

**Log Structure**:
```json
{
  "timestamp": "2025-04-19T14:30:00Z",
  "level": "INFO",
  "service": "user-service",
  "trace_id": "trace-uuid",
  "message": "User profile updated",
  "data": {
    "user_id": "user-uuid",
    "fields_updated": ["avatar_url", "bio"]
  }
}
```

**Log Aggregation**:
- Centralized logging with Elasticsearch
- Log visualization with Kibana
- Log retention policies
- Structured query capability

### Metrics

**Application Metrics**:
- Request rates and latencies
- Error rates
- Active users
- Business metrics (registrations, posts, transactions)

**System Metrics**:
- CPU and memory usage
- Network I/O
- Disk usage
- Container health

**Collection & Visualization**:
- Prometheus for collection
- Grafana for dashboards
- Alerting based on thresholds
- Trend analysis

### Tracing

**Distributed Tracing**:
- OpenTelemetry instrumentation
- Request flow visualization
- Performance bottleneck identification
- Error correlation

## Migration Strategy

The transition from Node.js/Express to Go will follow these phases:

1. **Phase 1: Infrastructure & Foundation**:
   - Set up Go microservices infrastructure
   - Implement core services (Auth, User)
   - Develop common packages
   - Establish CI/CD pipeline

2. **Phase 2: Core Functionality**:
   - Content service for book access
   - Basic social features
   - Payment processing
   - Points and rewards system

3. **Phase 3: Advanced Features**:
   - Marketplace functionality
   - Real-time chat
   - Streaming capabilities
   - Enhanced social features

4. **Phase 4: Legacy System Retirement**:
   - Data migration completion
   - Traffic shifting to new services
   - Legacy system decommissioning
   - Performance optimization

### Data Migration

**Strategy**:
- Snapshot initial data
- Synchronization mechanism for updates
- Dual-write during transition
- Validation and reconciliation

**Tools**:
- Custom ETL processes
- Database migration scripts
- Consistency checking tools
- Rollback capability

## Implementation Timeline

### Initial Development (3 Months)

**Month 1: Foundation**
- Set up development environment
- Implement common packages
- Develop Auth Service
- Develop User Service

**Month 2: Core Features**
- Implement Content Service
- Develop Social Service
- Set up data storage
- Implement API Gateway

**Month 3: MVP Release**
- Develop initial UI integration
- Implement MVP features
- Conduct system testing
- Deploy to staging environment

### Feature Expansion (6 Months)

**Months 4-6: Enhanced Features**
- Develop Marketplace Service
- Implement Payment Service
- Develop Chat Service
- Implement Notifications

**Months 7-9: Advanced Capabilities**
- Develop Streaming Service
- Implement Rewards Service
- Develop Analytics Service
- Enhanced social features

### Production & Optimization (3 Months)

**Month 10: Production Preparation**
- Performance optimization
- Security hardening
- Documentation completion
- User acceptance testing

**Month 11: Production Deployment**
- Phased rollout
- Monitoring setup
- Support procedures
- Incident response preparation

**Month 12: Stabilization**
- Bug fixing
- Performance tuning
- Feature refinement
- Planning for next phase

## Conclusion

This architecture document provides a comprehensive blueprint for implementing the Great Nigeria platform as a Go-based microservices system. The architecture is designed to support all the enhanced community features while providing scalability, maintainability, and performance. By following the incremental migration approach, the transition from the current system can be achieved with minimal disruption while enabling the platform to scale and adapt to future requirements.

The key to this scalability is the combination of stateless services, distributed data management, asynchronous processing, and multi-level caching. These approaches, combined with the operational practices outlined in this document, will ensure that the platform remains responsive and reliable even during peak usage periods or viral content surges.

The implementation plan provides a phased approach to scaling, allowing for appropriate infrastructure investment aligned with actual user growth. This ensures cost-effectiveness while maintaining the ability to scale rapidly when needed.
