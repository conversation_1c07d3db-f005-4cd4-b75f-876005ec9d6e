# Great Nigeria Platform - Design Guide (Part 3)

## UI Components

The Great Nigeria platform uses a consistent set of UI components to create a cohesive user experience across the platform.

### Buttons

Buttons are used for primary and secondary actions throughout the interface.

#### Button Types

**Primary Button**
- Background: Nigerian Green (#008751)
- Text: White (#FFFFFF)
- Border: None
- Hover: Darker green (#006B3F)
- Active: Even darker green (#005A35)
- Disabled: Light gray background (#E0E0E0) with medium gray text (#767676)

**Secondary Button**
- Background: White (#FFFFFF)
- Text: Nigerian Green (#008751)
- Border: 1px solid Nigerian Green (#008751)
- Hover: Light green background (#E6F4EE)
- Active: Slightly darker light green (#D1E9DF)
- Disabled: Light gray border and text (#E0E0E0, #767676)

**Tertiary Button (Text Button)**
- Background: Transparent
- Text: Nigerian Green (#008751)
- Border: None
- Hover: Light green background (#E6F4EE)
- Active: Slightly darker light green (#D1E9DF)
- Disabled: Medium gray text (#767676)

**Danger But<PERSON>**
- Background: Earth Red (#C84E32)
- Text: White (#FFFFFF)
- Border: None
- Hover: Darker red (#A33D27)
- Active: Even darker red (#8A3421)
- Disabled: Light gray background (#E0E0E0) with medium gray text (#767676)

#### Button Sizes

**Large**
- Height: 48px
- Padding: 16px 24px
- Font: Lato Bold, 16px
- Border radius: 4px
- Use: Primary page actions, hero CTAs

**Medium (Default)**
- Height: 40px
- Padding: 12px 16px
- Font: Lato Bold, 16px
- Border radius: 4px
- Use: Most interface actions

**Small**
- Height: 32px
- Padding: 8px 12px
- Font: Lato Bold, 14px
- Border radius: 4px
- Use: Inline actions, compact UIs

**Icon Button**
- Size: 40px x 40px (medium), 32px x 32px (small)
- Icon: 24px (medium), 16px (small)
- Border radius: 4px (square) or 50% (circular)
- Use: Toolbar actions, compact UIs

#### Button Implementation Guidelines

- Use primary buttons for the main action on a page
- Limit primary buttons to one per section
- Use secondary buttons for alternative actions
- Use tertiary buttons for less important actions
- Maintain minimum touch target size of 44x44px on mobile
- Include hover and active states for all buttons
- Provide clear visual feedback for all button states
- Use consistent button ordering across the platform (primary on right)
- Include appropriate aria attributes for accessibility

### Form Elements

Form elements are used for user input throughout the platform.

#### Text Inputs

**Default State**
- Height: 40px
- Padding: 12px 16px
- Border: 1px solid Light Gray (#E0E0E0)
- Border radius: 4px
- Background: White (#FFFFFF)
- Text: Nigerian Black (#111111)
- Font: Lato Regular, 16px

**Focus State**
- Border: 2px solid Nigerian Green (#008751)
- Box shadow: 0 0 0 3px rgba(0, 135, 81, 0.2)

**Hover State**
- Border: 1px solid Medium Gray (#767676)

**Disabled State**
- Background: Light Gray (#F5F5F5)
- Border: 1px solid Light Gray (#E0E0E0)
- Text: Medium Gray (#767676)

**Error State**
- Border: 1px solid Earth Red (#C84E32)
- Error text: Earth Red (#C84E32)
- Icon: Error icon in Earth Red

#### Checkboxes and Radio Buttons

**Checkbox (Unchecked)**
- Size: 20px x 20px
- Border: 1px solid Light Gray (#E0E0E0)
- Border radius: 4px
- Background: White (#FFFFFF)

**Checkbox (Checked)**
- Background: Nigerian Green (#008751)
- Border: 1px solid Nigerian Green (#008751)
- Checkmark: White (#FFFFFF)

**Radio Button (Unchecked)**
- Size: 20px x 20px
- Border: 1px solid Light Gray (#E0E0E0)
- Border radius: 50%
- Background: White (#FFFFFF)

**Radio Button (Checked)**
- Border: 1px solid Nigerian Green (#008751)
- Inner circle: Nigerian Green (#008751), 10px diameter

**Focus State (Both)**
- Border: 2px solid Nigerian Green (#008751)
- Box shadow: 0 0 0 3px rgba(0, 135, 81, 0.2)

#### Dropdown Selects

**Default State**
- Height: 40px
- Padding: 12px 16px
- Border: 1px solid Light Gray (#E0E0E0)
- Border radius: 4px
- Background: White (#FFFFFF)
- Text: Nigerian Black (#111111)
- Icon: Dropdown arrow in Medium Gray (#767676)

**Open State**
- Border: 1px solid Nigerian Green (#008751)
- Border radius: 4px 4px 0 0 (if dropdown opens below)
- Box shadow: 0 4px 8px rgba(0, 0, 0, 0.1)

**Dropdown Menu**
- Background: White (#FFFFFF)
- Border: 1px solid Light Gray (#E0E0E0)
- Border radius: 0 0 4px 4px (if dropdown opens below)
- Box shadow: 0 4px 8px rgba(0, 0, 0, 0.1)
- Option padding: 12px 16px
- Selected option: Light green background (#E6F4EE)
- Hover option: Light Gray background (#F5F5F5)

#### Form Layout Guidelines

- Group related fields together
- Use clear, concise labels above input fields
- Provide helper text for complex inputs
- Show validation errors inline, next to the relevant field
- Use placeholder text sparingly and not as a replacement for labels
- Maintain consistent spacing between form elements (16px)
- Align labels and fields consistently across forms
- Indicate required fields with an asterisk (*)
- Provide clear error messages that suggest how to fix the issue

### Cards

Cards are used to group related content and actions throughout the platform.

#### Card Types

**Content Card**
- Background: White (#FFFFFF)
- Border: None
- Border radius: 8px
- Box shadow: 0 2px 4px rgba(0, 0, 0, 0.1)
- Padding: 24px
- Use: General content containers

**Interactive Card**
- Same as Content Card, plus:
- Hover state: Box shadow: 0 4px 8px rgba(0, 0, 0, 0.1)
- Active state: Box shadow: 0 1px 2px rgba(0, 0, 0, 0.1)
- Use: Clickable content blocks

**Feature Card**
- Background: White (#FFFFFF)
- Border: None
- Border radius: 8px
- Box shadow: 0 2px 4px rgba(0, 0, 0, 0.1)
- Top accent: 4px solid Nigerian Green (#008751)
- Padding: 24px
- Use: Highlighting important features

**Status Card**
- Background: White (#FFFFFF)
- Border: None
- Border radius: 8px
- Box shadow: 0 2px 4px rgba(0, 0, 0, 0.1)
- Left accent: 4px solid (color varies by status)
- Padding: 24px
- Use: Displaying status information

#### Card Content Structure

**Header**
- Title: Lato Bold, 20px, Nigerian Black (#111111)
- Subtitle: Lato Regular, 16px, Dark Gray (#333333)
- Icon/Avatar: Left-aligned or top-right corner
- Action: Optional icon button in top-right corner

**Body**
- Text: Lato Regular, 16px, Nigerian Black (#111111)
- Supporting visuals: Images, charts, etc.
- Metadata: Lato Regular, 14px, Medium Gray (#767676)

**Footer**
- Actions: Primary and secondary buttons
- Links: Tertiary buttons or text links
- Metadata: Timestamps, author information, etc.

#### Card Implementation Guidelines

- Use consistent card types for similar content
- Maintain consistent spacing within cards (16px between elements)
- Limit the amount of content in each card to maintain focus
- Ensure card actions are clearly distinguishable
- Use appropriate card types based on the content purpose
- Implement responsive behavior for cards (full width on mobile)
- Consider loading states and empty states for dynamic card content

### Navigation

Navigation components provide consistent ways for users to move through the platform.

#### Primary Navigation

**Top Navigation Bar**
- Height: 64px
- Background: White (#FFFFFF)
- Border bottom: 1px solid Light Gray (#E0E0E0)
- Logo: Left-aligned
- Nav items: Horizontal list, center or right-aligned
- User menu: Right-aligned
- Active item: Nigerian Green text (#008751), 2px bottom border
- Hover item: Light green background (#E6F4EE)

**Mobile Navigation**
- Hamburger menu icon: Right-aligned in header
- Menu panel: Slides in from left
- Background: White (#FFFFFF)
- Width: 80% of screen width
- Nav items: Vertical list with icons
- Active item: Nigerian Green text (#008751), light green background (#E6F4EE)

#### Secondary Navigation

**Sidebar Navigation**
- Width: 240px (desktop), collapsible on tablet
- Background: White (#FFFFFF) or Light Gray (#F5F5F5)
- Border right: 1px solid Light Gray (#E0E0E0)
- Section headers: Lato Bold, 14px, Dark Gray (#333333)
- Nav items: Lato Regular, 16px, Nigerian Black (#111111)
- Active item: Nigerian Green text (#008751), light green background (#E6F4EE)
- Hover item: Light Gray background (#F5F5F5)
- Icons: Left-aligned, 20px, same color as text

**Tab Navigation**
- Height: 48px
- Background: Transparent
- Border bottom: 1px solid Light Gray (#E0E0E0)
- Tab items: Lato Bold, 16px
- Inactive tab: Dark Gray text (#333333)
- Active tab: Nigerian Green text (#008751), 2px bottom border
- Hover tab: Medium Gray text (#767676)

#### Contextual Navigation

**Breadcrumbs**
- Height: 40px
- Text: Lato Regular, 14px, Medium Gray (#767676)
- Separator: Forward slash (/) in Light Gray (#E0E0E0)
- Current page: Lato Regular, 14px, Nigerian Black (#111111)
- Hover: Nigerian Green text (#008751)

**Pagination**
- Height: 40px
- Background: Transparent
- Page numbers: Lato Regular, 16px, Dark Gray (#333333)
- Current page: Lato Bold, 16px, White (#FFFFFF) on Nigerian Green (#008751)
- Hover: Light green background (#E6F4EE)
- Previous/Next: Icon buttons with text

#### Navigation Implementation Guidelines

- Provide clear visual indication of current location
- Maintain consistent navigation patterns across the platform
- Ensure all navigation is keyboard accessible
- Implement responsive adjustments for different screen sizes
- Use appropriate ARIA attributes for accessibility
- Consider touch targets for mobile navigation (minimum 44x44px)
- Provide smooth transitions for navigation state changes
- Ensure navigation labels are clear and descriptive

### Notifications and Alerts

Notifications and alerts provide feedback and important information to users.

#### Toast Notifications

**Success Toast**
- Background: Light green (#E6F4EE)
- Icon: Checkmark in Nigerian Green (#008751)
- Border left: 4px solid Nigerian Green (#008751)
- Text: Nigerian Black (#111111)
- Action: Optional close button or action link
- Duration: Auto-dismiss after 5 seconds (configurable)
- Position: Top-right corner

**Error Toast**
- Background: Light red (#FAEDEB)
- Icon: Error icon in Earth Red (#C84E32)
- Border left: 4px solid Earth Red (#C84E32)
- Text: Nigerian Black (#111111)
- Action: Close button
- Duration: Remains until dismissed
- Position: Top-right corner

**Info Toast**
- Background: Light blue (#EBF5FA)
- Icon: Info icon in River Blue (#3D85C6)
- Border left: 4px solid River Blue (#3D85C6)
- Text: Nigerian Black (#111111)
- Action: Optional close button or action link
- Duration: Auto-dismiss after 5 seconds (configurable)
- Position: Top-right corner

**Warning Toast**
- Background: Light gold (#FBF6E9)
- Icon: Warning icon in Accent Gold (#F7C35F)
- Border left: 4px solid Accent Gold (#F7C35F)
- Text: Nigerian Black (#111111)
- Action: Optional close button or action link
- Duration: Auto-dismiss after 8 seconds (configurable)
- Position: Top-right corner

#### Inline Alerts

**Success Alert**
- Background: Light green (#E6F4EE)
- Icon: Checkmark in Nigerian Green (#008751)
- Border: 1px solid Nigerian Green (#008751)
- Text: Nigerian Black (#111111)
- Action: Optional close button or action link

**Error Alert**
- Background: Light red (#FAEDEB)
- Icon: Error icon in Earth Red (#C84E32)
- Border: 1px solid Earth Red (#C84E32)
- Text: Nigerian Black (#111111)
- Action: Optional close button or action link

**Info Alert**
- Background: Light blue (#EBF5FA)
- Icon: Info icon in River Blue (#3D85C6)
- Border: 1px solid River Blue (#3D85C6)
- Text: Nigerian Black (#111111)
- Action: Optional close button or action link

**Warning Alert**
- Background: Light gold (#FBF6E9)
- Icon: Warning icon in Accent Gold (#F7C35F)
- Border: 1px solid Accent Gold (#F7C35F)
- Text: Nigerian Black (#111111)
- Action: Optional close button or action link

#### Modal Alerts

**Confirmation Modal**
- Background overlay: Semi-transparent black (rgba(0, 0, 0, 0.5))
- Modal background: White (#FFFFFF)
- Border radius: 8px
- Box shadow: 0 4px 16px rgba(0, 0, 0, 0.2)
- Title: Lato Bold, 20px, Nigerian Black (#111111)
- Content: Lato Regular, 16px, Nigerian Black (#111111)
- Primary action: Primary button
- Secondary action: Secondary button
- Close icon: Top-right corner

**Error Modal**
- Same as Confirmation Modal, plus:
- Icon: Error icon in Earth Red (#C84E32)
- Border top: 4px solid Earth Red (#C84E32)

#### Notification Implementation Guidelines

- Use appropriate notification types based on message importance
- Keep notification messages clear and concise
- Provide actionable information when possible
- Ensure notifications are accessible to screen readers
- Position notifications consistently throughout the platform
- Consider stacking behavior for multiple notifications
- Implement appropriate animation for notification appearance/disappearance
- Allow users to configure notification preferences when appropriate

### Data Visualization

Data visualization components present information in visual formats throughout the platform.

#### Charts and Graphs

**Bar Charts**
- Bar color: Nigerian Green (#008751) for single series
- Multiple series: Nigerian Green, River Blue, Accent Gold, Earth Red
- Axis lines: Light Gray (#E0E0E0)
- Axis labels: Lato Regular, 12px, Medium Gray (#767676)
- Chart title: Lato Bold, 16px, Nigerian Black (#111111)
- Tooltip: White background, 1px border, 4px radius, box shadow

**Line Charts**
- Line color: Nigerian Green (#008751) for single series
- Line thickness: 2px
- Data points: 6px circles, white fill with colored border
- Multiple series: Nigerian Green, River Blue, Accent Gold, Earth Red
- Area fill: Semi-transparent color (20% opacity)
- Grid lines: Light Gray (#E0E0E0), dashed
- Other elements: Same as Bar Charts

**Pie/Donut Charts**
- Segment colors: Nigerian Green, River Blue, Accent Gold, Earth Red, plus extended palette
- Segment separation: 1px white space
- Center label (donut): Lato Bold, 16px, Nigerian Black (#111111)
- Legend: Colored squares with labels, Lato Regular, 14px

#### Progress Indicators

**Progress Bar**
- Height: 8px
- Background: Light Gray (#E0E0E0)
- Fill: Nigerian Green (#008751)
- Border radius: 4px
- Label: Lato Regular, 14px, Dark Gray (#333333)
- Percentage: Lato Bold, 14px, Nigerian Black (#111111)

**Circular Progress**
- Size: 64px diameter
- Stroke width: 4px
- Background: Light Gray (#E0E0E0)
- Fill: Nigerian Green (#008751)
- Center label: Lato Bold, 16px, Nigerian Black (#111111)

**Step Indicator**
- Step circles: 24px diameter
- Completed step: Nigerian Green (#008751) fill, white checkmark
- Current step: White fill, Nigerian Green border, Nigerian Green number
- Future step: Light Gray (#E0E0E0) fill, Dark Gray number
- Connector line: 2px height, Nigerian Green for completed, Light Gray for future
- Labels: Lato Regular, 14px, Dark Gray (#333333) for all, Lato Bold for current

#### Data Tables

**Table Header**
- Background: Light Gray (#F5F5F5)
- Text: Lato Bold, 14px, Nigerian Black (#111111)
- Border bottom: 2px solid Light Gray (#E0E0E0)
- Sort icon: 16px, Medium Gray (#767676)
- Padding: 12px 16px

**Table Rows**
- Background: White (#FFFFFF)
- Alternate rows: Very Light Gray (#FAFAFA) (optional)
- Text: Lato Regular, 14px, Nigerian Black (#111111)
- Border bottom: 1px solid Light Gray (#E0E0E0)
- Hover: Light green background (#E6F4EE)
- Selected: Light green background (#E6F4EE), 2px left border in Nigerian Green
- Padding: 12px 16px

**Table Actions**
- Icon buttons: 16px icons
- Text actions: Lato Regular, 14px, Nigerian Green (#008751)
- Dropdown: Same as form dropdowns

#### Data Visualization Implementation Guidelines

- Use appropriate chart types based on the data and purpose
- Maintain consistent colors and styles across all visualizations
- Provide clear labels and legends for all data elements
- Ensure visualizations are accessible (text alternatives, keyboard navigation)
- Implement responsive behavior for different screen sizes
- Consider loading and empty states for dynamic data
- Provide interactive elements (tooltips, filters) when appropriate
- Limit the amount of data displayed to maintain clarity
- Use animation sparingly and purposefully
