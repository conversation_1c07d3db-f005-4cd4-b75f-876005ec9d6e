# Great Nigeria Library - AI Agent Instructions

This document provides instructions for AI agents working on the Great Nigeria Library project.

## Project Organization Standards

When working on this project, please follow these organization standards:

1. **Directory Structure**:
   - cmd/: Service entry points
   - database/: Database backups and scripts
   - docs/: Documentation
     - rchitecture/: System architecture documentation
     - code/: Code documentation and analysis
     - content/: Book content structure and guidelines
     - development/: Development guides and standards
     - eatures/: Feature specifications
     - project/: Project management documentation
   - internal/: Core business logic
   - obsolete/: Obsolete files
   - pkg/: Shared utilities
   - scripts/: Utility scripts
   - web/: Frontend assets

2. **Documentation Updates**:
   - When implementing a new feature, update the task list in docs/project/TASK_LIST.md
   - Add the file location of the implementation to the task list
   - Update relevant documentation files to reflect the changes

3. **Code Organization**:
   - Place new code in the appropriate directories based on functionality
   - Follow the established naming conventions
   - Group related functionality together

4. **Obsolete Files**:
   - Do not delete files that are no longer needed
   - Move them to the obsolete directory instead

5. **Script Usage**:
   - Use the scripts in the scripts directory for common tasks
   - Run scripts from the root directory of the project

## Task Completion Checklist

When completing a task, follow this checklist:

1. **Update Task List**:
   - Mark the task as completed in docs/project/TASK_LIST.md
   - Add the file location of the implementation

2. **Update Documentation**:
   - Update relevant documentation files to reflect the changes
   - Add any new documentation needed for the feature

3. **Test the Feature**:
   - Write tests for the feature
   - Run the tests to ensure they pass

4. **Update README**:
   - If the feature is significant, update the main README.md file

## Critical Files

The following files are critical to the project and should not be modified without careful consideration:

- Core service files in internal/ directory
- Frontend files in web/static/ directory
- Common utilities in pkg/common/ directory
- Nigerian Virtual Gifts System files
- Celebrate Nigeria Feature files

See docs/project/TASK_LIST.md for a complete list of implemented features and their file locations.
