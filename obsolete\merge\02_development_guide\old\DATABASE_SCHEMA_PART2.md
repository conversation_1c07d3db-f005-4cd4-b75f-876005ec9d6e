# Great Nigeria Platform - Database Schema (Part 2)

## Discussion System

### Discussions

The Discussions table stores forum topics and other discussion-based content, with links to relevant book sections.

```go
type DiscussionType string

const (
    DiscussionTypeGeneral      DiscussionType = "general"
    DiscussionTypeForumTopic   DiscussionType = "forum_topic"
    DiscussionTypeActionableStep DiscussionType = "actionable_step"
)

type Discussion struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    Title       string `gorm:"size:255;not null"`
    Content     string `gorm:"type:text;not null"`
    Type        DiscussionType `gorm:"type:varchar(20);default:'general'"`
    BookID      uint `gorm:"index"`
    ChapterID   uint `gorm:"index"`
    SectionID   uint `gorm:"index"`
    Views       int `gorm:"default:0"`
    IsSticky    bool `gorm:"default:false"`
    IsClosed    bool `gorm:"default:false"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    DeletedAt   gorm.DeletedAt `gorm:"index"`
    
    // Relationships
    User        User
    Comments    []Comment `gorm:"foreignKey:DiscussionID"`
    Likes       []UserLike `gorm:"foreignKey:DiscussionID;foreignKey:EntityType:discussion"`
}
```

**Key Features:**
- Multiple discussion types (general, forum topic, actionable step)
- Association with a specific user (creator)
- Optional association with book content (book, chapter, section)
- View count tracking
- Moderation controls (sticky, closed)
- Soft delete support
- Relationships to comments and likes

### Comments

The Comments table stores user responses to discussions, with support for threaded conversations.

```go
type Comment struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    DiscussionID uint `gorm:"index;not null"`
    ParentID    *uint `gorm:"index"` // For nested comments
    Content     string `gorm:"type:text;not null"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    DeletedAt   gorm.DeletedAt `gorm:"index"`
    
    // Relationships
    User        User
    Discussion  Discussion
    Parent      *Comment `gorm:"foreignKey:ParentID"`
    Replies     []Comment `gorm:"foreignKey:ParentID"`
    Likes       []UserLike `gorm:"foreignKey:CommentID;foreignKey:EntityType:comment"`
}
```

**Key Features:**
- Association with a specific user (author)
- Link to the parent discussion
- Support for nested comments (replies to comments)
- Soft delete support
- Relationship to likes

### UserLikes

The UserLikes table tracks user reactions to discussions and comments.

```go
type UserLike struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    EntityID    uint `gorm:"index;not null"` // ID of the entity being liked
    EntityType  string `gorm:"size:20;not null"` // Type of entity (discussion, comment)
    CreatedAt   time.Time
    
    // Relationships
    User        User
}
```

**Key Features:**
- Association with a specific user
- Polymorphic relationship to liked entities (discussions, comments)
- Timestamp for like tracking

## Points and Rewards System

### UserActivities

The UserActivities table records user actions that earn points, providing a detailed activity history.

```go
type ActivityType string

const (
    ActivityTypeRead       ActivityType = "read"
    ActivityTypeComment    ActivityType = "comment"
    ActivityTypeDiscussion ActivityType = "discussion"
    ActivityTypeShare      ActivityType = "share"
    ActivityTypeComplete   ActivityType = "complete"
)

type UserActivity struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    ActivityType ActivityType `gorm:"type:varchar(20);not null"`
    EntityID    uint `gorm:"index"` // ID of the entity related to this activity
    EntityType  string `gorm:"size:20"` // Type of entity (book, chapter, discussion, etc.)
    Points      int `gorm:"default:0"`
    Metadata    string `gorm:"type:jsonb"` // Additional activity data
    CreatedAt   time.Time
    
    // Relationships
    User        User
}
```

**Key Features:**
- Association with a specific user
- Activity type categorization
- Polymorphic relationship to activity targets
- Points awarded for the activity
- JSON metadata for additional context
- Timestamp for activity tracking

### TopicCompletions

The TopicCompletions table tracks when users complete specific sections, awarding points for completion.

```go
type TopicCompletion struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    BookID      uint `gorm:"index;not null"`
    ChapterID   uint `gorm:"index;not null"`
    SectionID   uint `gorm:"index;not null"`
    Points      int `gorm:"default:0"`
    CompletedAt time.Time
    
    // Relationships
    User        User
}
```

**Key Features:**
- Association with a specific user
- Records the specific book, chapter, and section completed
- Points awarded for completion
- Timestamp for completion tracking

### MembershipLevels

The MembershipLevels table defines the different membership tiers and their requirements.

```go
type MembershipLevel struct {
    ID          uint `gorm:"primaryKey"`
    Level       int `gorm:"unique;not null"`
    Name        string `gorm:"size:50;not null"`
    Description string `gorm:"type:text"`
    PointsRequired int `gorm:"not null"`
    Benefits    string `gorm:"type:jsonb"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
}
```

**Key Features:**
- Numeric level identifier
- Name and description for the membership level
- Points threshold required to achieve the level
- JSON-encoded benefits associated with the level

## Payment System

### Purchases

The Purchases table records all financial transactions on the platform.

```go
type PaymentGateway string

const (
    PaymentGatewayPaystack    PaymentGateway = "paystack"
    PaymentGatewayFlutterwave PaymentGateway = "flutterwave"
    PaymentGatewaySquad       PaymentGateway = "squad"
)

type PaymentStatus string

const (
    PaymentStatusPending    PaymentStatus = "pending"
    PaymentStatusCompleted  PaymentStatus = "completed"
    PaymentStatusFailed     PaymentStatus = "failed"
    PaymentStatusRefunded   PaymentStatus = "refunded"
)

type PaymentType string

const (
    PaymentTypePremium      PaymentType = "premium"
    PaymentTypeDonation     PaymentType = "donation"
)

type Currency string

const (
    CurrencyNGN Currency = "NGN"
    CurrencyUSD Currency = "USD"
)

type Purchase struct {
    ID             uint `gorm:"primaryKey"`
    UserID         uint `gorm:"index;not null"`
    Amount         float64 `gorm:"not null"`
    Currency       Currency `gorm:"type:varchar(3);default:'NGN'"`
    Description    string `gorm:"type:text"`
    PaymentType    PaymentType `gorm:"type:varchar(20);not null"`
    Gateway        PaymentGateway `gorm:"type:varchar(20);not null"`
    Status         PaymentStatus `gorm:"type:varchar(20);default:'pending'"`
    TransactionID  string `gorm:"size:100;index"`
    GatewayReference string `gorm:"size:100;index"`
    PurchaseDate   time.Time
    ExpiryDate     *time.Time
    Metadata       string `gorm:"type:jsonb"` // Additional purchase data
    CreatedAt      time.Time
    UpdatedAt      time.Time
    
    // Relationships
    User           User
}
```

**Key Features:**
- Association with a specific user
- Transaction amount and currency
- Payment type categorization
- Support for multiple Nigerian payment gateways
- Payment status tracking
- Transaction reference IDs
- Purchase and expiry dates
- JSON metadata for additional transaction details

### Plans

The Plans table defines the subscription plans available on the platform.

```go
type PlanDuration string

const (
    PlanDurationMonthly PlanDuration = "monthly"
    PlanDurationQuarterly PlanDuration = "quarterly"
    PlanDurationAnnual PlanDuration = "annual"
)

type Plan struct {
    ID          uint `gorm:"primaryKey"`
    Name        string `gorm:"size:100;not null"`
    Description string `gorm:"type:text"`
    Duration    PlanDuration `gorm:"type:varchar(20);not null"`
    Price       float64 `gorm:"not null"`
    Currency    Currency `gorm:"type:varchar(3);default:'NGN'"`
    Features    string `gorm:"type:jsonb"`
    IsActive    bool `gorm:"default:true"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    Subscriptions []Subscription `gorm:"foreignKey:PlanID"`
}
```

**Key Features:**
- Plan name and description
- Duration options (monthly, quarterly, annual)
- Price and currency
- JSON-encoded features included in the plan
- Active status flag

### Subscriptions

The Subscriptions table tracks user subscriptions to premium plans.

```go
type SubscriptionStatus string

const (
    SubscriptionStatusActive    SubscriptionStatus = "active"
    SubscriptionStatusCancelled SubscriptionStatus = "cancelled"
    SubscriptionStatusExpired   SubscriptionStatus = "expired"
)

type Subscription struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    PlanID      uint `gorm:"index;not null"`
    Status      SubscriptionStatus `gorm:"type:varchar(20);default:'active'"`
    StartDate   time.Time
    EndDate     time.Time
    CancelledAt *time.Time
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    User        User
    Plan        Plan
}
```

**Key Features:**
- Association with a specific user
- Link to the subscription plan
- Subscription status tracking
- Start and end dates
- Cancellation tracking
