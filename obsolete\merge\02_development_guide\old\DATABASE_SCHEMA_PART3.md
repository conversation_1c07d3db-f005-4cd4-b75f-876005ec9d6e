# Great Nigeria Platform - Database Schema (Part 3)

## Citation System

The citation system uses a separate set of tables to track citations and bibliographic references across all books.

### Citations

```sql
CREATE TABLE IF NOT EXISTS citations (
    id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL,
    citation_key VARCHAR(100) NOT NULL,
    ref_number INTEGER NOT NULL,
    author TEXT NOT NULL,
    year VARCHAR(20) NOT NULL,
    title TEXT NOT NULL,
    source TEXT NOT NULL,
    url TEXT,
    type VARCHAR(50) NOT NULL,
    cited_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(book_id, citation_key)
);
```

**Key Features:**
- Unique citation key per book
- Reference number for in-text citations
- Author, year, title, and source information
- URL for online sources
- Citation type categorization
- Usage count tracking

### Citation Usages

```sql
CREATE TABLE IF NOT EXISTS citation_usages (
    id SERIAL PRIMARY KEY,
    citation_id INTEGER NOT NULL REFERENCES citations(id),
    book_id INTEGER NOT NULL,
    chapter_id INTEGER NOT NULL,
    section_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(citation_id, book_id, chapter_id, section_id)
);
```

**Key Features:**
- Links citations to specific locations in the books
- Tracks where each citation is used
- Prevents duplicate usage records

### Bibliographies

```sql
CREATE TABLE IF NOT EXISTS bibliographies (
    id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL UNIQUE,
    title TEXT NOT NULL,
    description TEXT,
    last_generated TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Features:**
- One bibliography per book
- Title and description for the bibliography
- Timestamp for when the bibliography was last generated

### Citation Categories

```sql
CREATE TABLE IF NOT EXISTS citation_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name)
);
```

**Key Features:**
- Categorizes citations by type
- Provides display order for bibliography organization
- Includes descriptions for each category

## Database Management

The Great Nigeria platform includes several tools and scripts for database management:

### Database Connection

The database connection is managed through a dedicated package that handles connection pooling, configuration, and migrations:

```go
// NewDatabase creates a new database connection
func NewDatabase(cfg *config.Config) (*gorm.DB, error) {
    var gormConfig gorm.Config

    // Configure logger based on debug mode
    if cfg.DebugMode {
        gormConfig.Logger = logger.Default.LogMode(logger.Info)
    } else {
        gormConfig.Logger = logger.Default.LogMode(logger.Error)
    }

    // Connect to database
    db, err := gorm.Open(postgres.Open(cfg.GetDatabaseURL()), &gormConfig)
    if err != nil {
        return nil, fmt.Errorf("failed to connect to database: %w", err)
    }

    // Configure connection pool
    sqlDB, err := db.DB()
    if err != nil {
        return nil, fmt.Errorf("failed to get SQL DB: %w", err)
    }

    // Set connection pool parameters
    sqlDB.SetMaxIdleConns(10)
    sqlDB.SetMaxOpenConns(100)
    sqlDB.SetConnMaxLifetime(time.Hour)

    // Auto-migrate schemas
    if err := autoMigrate(db); err != nil {
        return nil, fmt.Errorf("failed to auto-migrate database: %w", err)
    }

    return db, nil
}
```

**Key Features:**
- Configurable logging based on debug mode
- Connection pooling with optimized parameters
- Automatic schema migration

### Auto-Migration

The platform uses GORM's auto-migration feature to manage database schema changes:

```go
// autoMigrate automatically migrates the database schemas
func autoMigrate(db *gorm.DB) error {
    // Auto-migrate all models
    return db.AutoMigrate(
        // User-related models
        &models.User{},
        
        // Authentication-related models
        &models.VerificationStatus{},
        &models.VerificationRequest{},
        &models.UserBadge{},
        &models.ProfileCompletionStatus{},
        
        // Book-related models
        &models.Book{},
        &models.Chapter{},
        &models.Section{},
        &models.UserProgress{},
        &models.Bookmark{},
        
        // Discussion-related models
        &models.Discussion{},
        &models.Comment{},
        &models.UserLike{},
        
        // Feedback-related models
        &models.ContentMoodFeedback{},
        &models.ContentDifficultyFeedback{},
        
        // Points-related models
        &models.UserActivity{},
        &models.TopicCompletion{},
        &models.MembershipLevel{},
        
        // Payment-related models
        &models.Purchase{},
        &models.Plan{},
        &models.Subscription{},
    )
}
```

**Key Features:**
- Automatic schema creation and updates
- Organized by model category
- Comprehensive coverage of all database models

### Transaction Support

The platform includes a helper function for transaction management:

```go
// Transaction executes the given function in a database transaction
func Transaction(db *gorm.DB, fn func(tx *gorm.DB) error) error {
    return db.Transaction(fn)
}
```

**Key Features:**
- Simplified transaction handling
- Automatic rollback on error
- Consistent transaction pattern across the codebase

## Backup and Restoration

The Great Nigeria platform includes comprehensive tools for database backup and restoration:

### Database Backup

The platform uses a PowerShell script (`manage_database.ps1`) to handle database backups:

```powershell
# Backup database
function Backup-Database {
    $params = Get-DbParams
    Set-PgPassword -Password $params.Password
    
    try {
        if (-not (Test-DatabaseExists -DbName $params.Name)) {
            Write-Host "Database '$($params.Name)' does not exist." -ForegroundColor Yellow
            return
        }
        
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupDir = ".\backups"
        
        if (-not (Test-Path $backupDir)) {
            New-Item -ItemType Directory -Path $backupDir | Out-Null
        }
        
        if (-not $BackupFile) {
            $BackupFile = "$backupDir\$($params.Name)_$timestamp.sql"
        }
        
        Write-Host "Backing up database '$($params.Name)' to '$BackupFile'..." -ForegroundColor Cyan
        pg_dump -h $params.Host -p $params.Port -U $params.User -F p -f $BackupFile $params.Name
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Database backup created successfully: $BackupFile" -ForegroundColor Green
        } else {
            Write-Host "Failed to create database backup. Error code: $LASTEXITCODE" -ForegroundColor Red
        }
    } catch {
        Write-Host "Error backing up database: $_" -ForegroundColor Red
    } finally {
        Clear-PgPassword
    }
}
```

**Key Features:**
- Timestamp-based backup naming
- Automatic backup directory creation
- Comprehensive error handling
- Success/failure reporting

### Database Restoration

The platform includes a restoration function to recover from backups:

```powershell
# Restore database
function Restore-Database {
    if (-not $BackupFile) {
        Write-Host "Backup file not specified. Use -BackupFile parameter." -ForegroundColor Red
        return
    }
    
    if (-not (Test-Path $BackupFile)) {
        Write-Host "Backup file '$BackupFile' not found." -ForegroundColor Red
        return
    }
    
    $params = Get-DbParams
    Set-PgPassword -Password $params.Password
    
    try {
        # Create database if it doesn't exist
        if (-not (Test-DatabaseExists -DbName $params.Name)) {
            Write-Host "Database '$($params.Name)' does not exist. Creating it..." -ForegroundColor Yellow
            psql -h $params.Host -p $params.Port -U $params.User -c "CREATE DATABASE $($params.Name)" postgres
        } else {
            $confirmation = Read-Host "Database '$($params.Name)' already exists. Do you want to drop and recreate it? (y/n)"
            if ($confirmation -eq "y") {
                Write-Host "Dropping database '$($params.Name)'..." -ForegroundColor Cyan
                psql -h $params.Host -p $params.Port -U $params.User -c "DROP DATABASE $($params.Name)" postgres
                
                Write-Host "Creating database '$($params.Name)'..." -ForegroundColor Cyan
                psql -h $params.Host -p $params.Port -U $params.User -c "CREATE DATABASE $($params.Name)" postgres
            }
        }
        
        Write-Host "Restoring database from '$BackupFile'..." -ForegroundColor Cyan
        psql -h $params.Host -p $params.Port -U $params.User -d $params.Name -f $BackupFile
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Database restored successfully." -ForegroundColor Green
        } else {
            Write-Host "Failed to restore database. Error code: $LASTEXITCODE" -ForegroundColor Red
        }
    } catch {
        Write-Host "Error restoring database: $_" -ForegroundColor Red
    } finally {
        Clear-PgPassword
    }
}
```

**Key Features:**
- Validation of backup file existence
- Database creation if needed
- Option to drop and recreate existing database
- Comprehensive error handling
- Success/failure reporting

## Indexes and Performance

The database schema includes several indexes to optimize query performance:

1. Primary keys on all tables
2. Foreign key indexes (UserID, BookID, ChapterID, SectionID)
3. Unique indexes for username, email, and other unique fields
4. Composite indexes for common query patterns
5. Soft delete indexes (DeletedAt)

## Data Access Patterns

The platform uses repository interfaces to encapsulate data access patterns:

```go
// UserRepository interface example
type UserRepository interface {
    GetByID(id uint) (*User, error)
    GetByUsername(username string) (*User, error)
    GetByEmail(email string) (*User, error)
    Create(user *User) error
    Update(user *User) error
    Delete(id uint) error
    UpdateMembershipLevel(userID uint, level int) error
    AddPoints(userID uint, points int) error
}

// Implementation example
type PostgresUserRepository struct {
    DB *gorm.DB
}

func (r *PostgresUserRepository) GetByID(id uint) (*User, error) {
    var user User
    err := r.DB.First(&user, id).Error
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil // User not found
        }
        return nil, err
    }
    return &user, nil
}

// Other methods would be implemented similarly...
```

**Key Features:**
- Clean separation of data access logic
- Consistent error handling
- Repository pattern for testability
- Type-safe operations

## Conclusion

The Great Nigeria platform's database schema is designed to support all aspects of the application, from content management to user engagement and payment processing. The schema follows best practices for relational database design, with proper normalization, indexing, and relationship modeling.

The platform includes comprehensive tools for database management, including connection pooling, migrations, backups, and restorations. The repository pattern provides a clean abstraction for data access, making the codebase more maintainable and testable.

As the platform evolves, the database schema can be extended to support new features while maintaining backward compatibility through careful migration management.
