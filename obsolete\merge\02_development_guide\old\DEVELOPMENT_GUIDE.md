# Great Nigeria Platform - Development Guide

## Overview

This guide provides comprehensive information for developers working on the Great Nigeria platform. It covers coding standards, development workflows, testing procedures, and best practices.

## Table of Contents

1. [Development Environment](#development-environment)
2. [Coding Standards](#coding-standards)
3. [Development Workflow](#development-workflow)
4. [Testing](#testing)
5. [Documentation](#documentation)
6. [Performance Considerations](#performance-considerations)
7. [Security Guidelines](#security-guidelines)
8. [Deployment](#deployment)

## Development Environment

### Recommended Tools

- **IDE**: Visual Studio Code with the following extensions:
  - Go (by Go Team at Google)
  - ESLint (by Microsoft)
  - Prettier (by <PERSON>tti<PERSON>)
  - EditorConfig (by EditorConfig)
  - GitLens (by GitKraken)

- **API Testing**: Postman or Insomnia
- **Database Management**: pgAdmin or DBeaver
- **Git Client**: GitKraken, SourceTree, or command line

### Local Development Setup

Refer to the [Setup Guide](SETUP_GUIDE.md) for detailed instructions on setting up your local development environment.

### Development Modes

The platform supports several development modes:

1. **Standard Mode**: Default development mode with live API and local database
2. **Mock Mode**: Uses mock data instead of real API calls (faster for UI development)
3. **Hybrid Mode**: Uses real API for some services and mock data for others

To enable different modes, set the `DEV_MODE` environment variable:
```
DEV_MODE=standard|mock|hybrid
```

## Coding Standards

### Go Code Standards

- Follow the [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
- Use [gofmt](https://golang.org/cmd/gofmt/) to format code
- Follow the standard Go project layout
- Use meaningful variable and function names
- Write comprehensive comments for public functions and packages
- Keep functions small and focused on a single responsibility
- Use error handling consistently

### JavaScript/TypeScript Standards

- Follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use ESLint and Prettier for code formatting
- Use TypeScript for type safety
- Prefer functional components with hooks in React
- Use meaningful variable and function names
- Document complex functions and components

### CSS/SCSS Standards

- Follow the [BEM (Block Element Modifier)](http://getbem.com/) methodology
- Use SCSS for styling
- Keep selectors simple and avoid deep nesting
- Use variables for colors, fonts, and other repeated values
- Organize styles by component
- Ensure responsive design for all components

### Commit Message Standards

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

Types:
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: Code changes that neither fix a bug nor add a feature
- `perf`: Performance improvements
- `test`: Adding or correcting tests
- `chore`: Changes to the build process or auxiliary tools

Example:
```
feat(auth): implement two-factor authentication

- Add SMS verification
- Add email verification
- Update user settings page

Closes #123
```

## Development Workflow

### Git Workflow

The project follows a modified Git Flow workflow:

1. **main**: Production-ready code
2. **develop**: Integration branch for features
3. **feature/***:  Feature branches
4. **bugfix/***:  Bug fix branches
5. **release/***:  Release preparation branches
6. **hotfix/***:  Hotfix branches for production issues

#### Feature Development Process

1. Create a new feature branch from `develop`:
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/your-feature-name
   ```

2. Develop and test your feature

3. Commit changes following the commit message standards

4. Push your branch to the remote repository:
   ```bash
   git push -u origin feature/your-feature-name
   ```

5. Create a pull request to merge into `develop`

6. Address code review feedback

7. Once approved, merge the pull request

### Code Review Process

All code changes must go through code review before being merged:

1. **Self-review**: Review your own code before submitting
2. **Peer review**: At least one other developer must review the code
3. **Automated checks**: All automated tests and linting must pass
4. **Review criteria**:
   - Code correctness
   - Code quality and style
   - Test coverage
   - Documentation
   - Performance considerations
   - Security implications

### Dependency Management

#### Go Dependencies

- Use Go modules for dependency management
- Pin dependencies to specific versions
- Document third-party dependencies in the README
- Regularly update dependencies for security patches

#### JavaScript Dependencies

- Use npm for dependency management
- Pin dependencies to specific versions in package.json
- Use `npm audit` to check for security vulnerabilities
- Regularly update dependencies for security patches

## Testing

### Testing Strategy

The project follows a comprehensive testing strategy:

1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test interactions between components
3. **End-to-End Tests**: Test complete user flows
4. **Performance Tests**: Test system performance under load
5. **Security Tests**: Test for security vulnerabilities

### Backend Testing

- Use the standard Go testing package
- Aim for at least 80% code coverage
- Use table-driven tests where appropriate
- Mock external dependencies
- Use testify for assertions and mocks

Example Go test:
```go
func TestUserService_GetByID(t *testing.T) {
    // Setup
    mockRepo := new(mocks.UserRepository)
    service := NewUserService(mockRepo)
    
    // Test cases
    tests := []struct {
        name     string
        userID   uint
        mockUser *models.User
        mockErr  error
        wantUser *models.User
        wantErr  bool
    }{
        {
            name:     "successful retrieval",
            userID:   1,
            mockUser: &models.User{ID: 1, Username: "testuser"},
            mockErr:  nil,
            wantUser: &models.User{ID: 1, Username: "testuser"},
            wantErr:  false,
        },
        {
            name:     "user not found",
            userID:   2,
            mockUser: nil,
            mockErr:  nil,
            wantUser: nil,
            wantErr:  false,
        },
        {
            name:     "repository error",
            userID:   3,
            mockUser: nil,
            mockErr:  errors.New("database error"),
            wantUser: nil,
            wantErr:  true,
        },
    }
    
    // Run tests
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup expectations
            mockRepo.On("GetByID", tt.userID).Return(tt.mockUser, tt.mockErr)
            
            // Call the function
            user, err := service.GetByID(tt.userID)
            
            // Assert results
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
            assert.Equal(t, tt.wantUser, user)
            
            // Verify expectations
            mockRepo.AssertExpectations(t)
        })
    }
}
```

### Frontend Testing

- Use Jest for unit testing
- Use React Testing Library for component testing
- Use Cypress for end-to-end testing
- Test component rendering, user interactions, and state changes

Example React component test:
```javascript
import { render, screen, fireEvent } from '@testing-library/react';
import UserProfile from './UserProfile';

describe('UserProfile', () => {
  const mockUser = {
    id: 1,
    username: 'testuser',
    fullName: 'Test User',
    email: '<EMAIL>',
  };

  test('renders user information correctly', () => {
    render(<UserProfile user={mockUser} />);
    
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('@testuser')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  test('edit button toggles edit mode', () => {
    render(<UserProfile user={mockUser} />);
    
    // Initially not in edit mode
    expect(screen.queryByLabelText('Full Name')).not.toBeInTheDocument();
    
    // Click edit button
    fireEvent.click(screen.getByText('Edit Profile'));
    
    // Now in edit mode
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test User')).toBeInTheDocument();
  });
});
```

### Running Tests

#### Backend Tests

```bash
# Run all tests
go test ./...

# Run tests for a specific package
go test ./internal/auth/...

# Run tests with coverage
go test -cover ./...

# Generate coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

#### Frontend Tests

```bash
# Run all tests
npm test

# Run tests for a specific component
npm test -- UserProfile

# Run tests with coverage
npm test -- --coverage

# Run end-to-end tests
npm run cypress:open
```

## Documentation

### Code Documentation

- Document all public functions, types, and packages
- Use [GoDoc](https://blog.golang.org/godoc) style for Go code
- Use [JSDoc](https://jsdoc.app/) for JavaScript/TypeScript
- Include examples for complex functions
- Document assumptions and edge cases

Example Go documentation:
```go
// UserService provides methods for managing users.
type UserService struct {
    repo UserRepository
}

// NewUserService creates a new UserService with the given repository.
func NewUserService(repo UserRepository) *UserService {
    return &UserService{
        repo: repo,
    }
}

// GetByID retrieves a user by their ID.
// Returns nil if the user is not found.
// Returns an error if the repository operation fails.
func (s *UserService) GetByID(id uint) (*User, error) {
    return s.repo.GetByID(id)
}
```

Example JavaScript documentation:
```javascript
/**
 * UserProfile component displays and allows editing of user information.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.user - User object containing profile information
 * @param {Function} props.onUpdate - Callback function called when user is updated
 * @returns {JSX.Element} Rendered component
 */
function UserProfile({ user, onUpdate }) {
    // Component implementation
}
```

### API Documentation

- Document all API endpoints
- Include request and response formats
- Document authentication requirements
- Include example requests and responses
- Document error responses

The API documentation is available at `/api/docs` when running in development mode.

## Performance Considerations

### Backend Performance

- Use database indexes for frequently queried fields
- Implement caching for expensive operations
- Use connection pooling for database connections
- Optimize database queries (avoid N+1 queries)
- Use pagination for large result sets
- Profile and optimize hot code paths

### Frontend Performance

- Minimize bundle size with code splitting
- Optimize images and assets
- Implement lazy loading for components and routes
- Use memoization for expensive calculations
- Optimize rendering with React.memo and useMemo
- Implement virtualization for long lists

### Monitoring and Profiling

- Use pprof for Go profiling
- Use Lighthouse for frontend performance auditing
- Implement logging for performance-critical operations
- Set up monitoring for production performance

## Security Guidelines

### Authentication and Authorization

- Use JWT for authentication
- Implement proper authorization checks
- Use secure password hashing (bcrypt)
- Implement rate limiting for authentication endpoints
- Use secure cookie settings (HttpOnly, Secure, SameSite)

### Data Protection

- Validate all user input
- Use parameterized queries to prevent SQL injection
- Implement proper error handling to avoid information leakage
- Use HTTPS for all communications
- Encrypt sensitive data at rest

### Common Vulnerabilities

- Prevent Cross-Site Scripting (XSS) with proper output encoding
- Prevent Cross-Site Request Forgery (CSRF) with tokens
- Avoid Server-Side Request Forgery (SSRF) by validating URLs
- Implement proper Content Security Policy (CSP)
- Regularly update dependencies to patch security vulnerabilities

## Deployment

### Deployment Environments

1. **Development**: For active development
2. **Staging**: For testing before production
3. **Production**: Live environment

### Deployment Process

1. Merge changes into the appropriate branch
2. Run the CI/CD pipeline
3. Deploy to the target environment
4. Run smoke tests
5. Monitor for issues

### Deployment Configuration

- Use environment variables for configuration
- Use different configuration files for different environments
- Document required environment variables

### Rollback Procedure

In case of deployment issues:

1. Identify the issue
2. Decide whether to fix forward or roll back
3. If rolling back:
   - Revert to the previous version in the deployment system
   - Verify the rollback was successful
   - Document the issue for future reference

## Conclusion

Following these development guidelines will ensure a consistent, high-quality codebase for the Great Nigeria platform. If you have questions or suggestions for improving these guidelines, please contact the development team.
