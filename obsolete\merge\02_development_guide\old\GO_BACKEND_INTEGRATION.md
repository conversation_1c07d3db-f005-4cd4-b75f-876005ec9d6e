# Go Backend Integration Guide

This document provides instructions for configuring the Go backend to work with the React TypeScript frontend.

## Overview

The Go backend needs to be configured to:

1. Allow Cross-Origin Resource Sharing (CORS) for the React frontend
2. Serve API endpoints that the React frontend can consume
3. Handle authentication and authorization for protected routes

## CORS Configuration

### 1. Install CORS Middleware

```bash
go get github.com/gin-contrib/cors
```

### 2. Import the CORS Package

In `cmd/api-gateway/main.go`, add the CORS package to the imports:

```go
import (
    // ... other imports
    "github.com/gin-contrib/cors"
)
```

### 3. Configure CORS Middleware

Add the CORS middleware to the router before registering any routes:

```go
// CORS configuration
router.Use(cors.New(cors.Config{
    AllowOrigins:     []string{"http://localhost:3000", "https://your-production-frontend-domain.com"},
    AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
    AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
    ExposeHeaders:    []string{"Content-Length"},
    AllowCredentials: true,
    MaxAge:           12 * time.Hour,
}))
```

### 4. Environment-Specific Configuration

For better flexibility, make the CORS configuration environment-specific:

```go
// CORS configuration
var allowedOrigins []string
if os.Getenv("ENV") == "production" {
    allowedOrigins = []string{"https://your-production-frontend-domain.com"}
} else {
    allowedOrigins = []string{"http://localhost:3000"}
}

router.Use(cors.New(cors.Config{
    AllowOrigins:     allowedOrigins,
    AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
    AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
    ExposeHeaders:    []string{"Content-Length"},
    AllowCredentials: true,
    MaxAge:           12 * time.Hour,
}))
```

## API Endpoints

The React frontend expects the following API endpoints:

### Authentication Endpoints

- `POST /api/auth/register`: Register a new user
- `POST /api/auth/login`: Login a user
- `GET /api/auth/me`: Get the current user

### Book Endpoints

- `GET /api/books`: Get all books
- `GET /api/books/:id`: Get a book by ID
- `GET /api/books/:id/chapters`: Get chapters for a book
- `GET /api/books/chapters/:id`: Get a chapter by ID
- `GET /api/books/sections/:id`: Get a section by ID
- `POST /api/books/:id/progress`: Save reading progress
- `GET /api/books/:id/progress`: Get reading progress for a book
- `POST /api/books/:id/bookmarks`: Add a bookmark
- `GET /api/books/:id/bookmarks`: Get bookmarks for a book
- `DELETE /api/books/bookmarks/:id`: Delete a bookmark

### User Profile Endpoints

- `GET /api/users/:id/profile`: Get user profile
- `PUT /api/users/:id/profile`: Update user profile
- `GET /api/users/:id/reading-stats`: Get reading statistics
- `GET /api/users/:id/bookmarks`: Get user bookmarks
- `GET /api/users/:id/activities`: Get user activities
- `POST /api/users/:id/change-password`: Change user password
- `POST /api/users/:id/avatar`: Upload user avatar

### Forum Endpoints

- `GET /api/forum/categories`: Get all forum categories
- `GET /api/forum/categories/:id/topics`: Get topics by category
- `GET /api/forum/topics/:id`: Get a topic by ID
- `POST /api/forum/topics`: Create a new topic
- `POST /api/forum/topics/:id/replies`: Create a reply to a topic
- `POST /api/forum/replies/:id/vote`: Vote on a reply
- `GET /api/forum/search`: Search topics
- `DELETE /api/forum/topics/:id`: Delete a topic
- `DELETE /api/forum/replies/:id`: Delete a reply

### Resource Endpoints

- `GET /api/resources/categories`: Get all resource categories
- `GET /api/resources/categories/:id/resources`: Get resources by category
- `GET /api/resources/:id`: Get a resource by ID
- `GET /api/resources/:id/download`: Download a resource
- `POST /api/resources/:id/track-download`: Track resource download
- `GET /api/resources/search`: Search resources

### Celebrate Nigeria Endpoints

- `GET /api/celebrate/featured`: Get featured entries
- `GET /api/celebrate/:type/:slug`: Get an entry by type and slug
- `GET /api/celebrate/search`: Search entries
- `GET /api/celebrate/categories`: Get all categories
- `POST /api/celebrate/submit`: Submit a new entry
- `POST /api/celebrate/entries/:id/vote`: Vote for an entry
- `POST /api/celebrate/entries/:id/comments`: Comment on an entry
- `GET /api/celebrate/random`: Get a random entry

## Authentication and Authorization

### JWT Authentication

The React frontend expects JWT authentication:

1. When a user logs in or registers, the backend should return a JWT token
2. The frontend will include this token in the `Authorization` header of subsequent requests
3. The backend should validate this token for protected routes

### Example Login Handler

```go
func (h *AuthHandler) Login(c *gin.Context) {
    var loginRequest struct {
        Email    string `json:"email" binding:"required,email"`
        Password string `json:"password" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&loginRequest); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    user, err := h.authService.Authenticate(loginRequest.Email, loginRequest.Password)
    if err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
        return
    }
    
    token, err := h.authService.GenerateToken(user.ID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "user": user,
        "token": token,
    })
}
```

### Example Auth Middleware

```go
func AuthMiddleware(authService *service.AuthService) gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
            return
        }
        
        // Extract token from "Bearer <token>"
        tokenParts := strings.Split(authHeader, " ")
        if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
            return
        }
        
        token := tokenParts[1]
        userID, err := authService.ValidateToken(token)
        if err != nil {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
            return
        }
        
        // Set user ID in context for later use
        c.Set("userID", userID)
        c.Next()
    }
}
```

## Error Handling

The React frontend expects consistent error responses:

```go
func ErrorResponse(c *gin.Context, statusCode int, errorCode string, message string, details string) {
    c.JSON(statusCode, gin.H{
        "error": gin.H{
            "code": errorCode,
            "message": message,
            "details": details,
        },
    })
}
```

Common error codes:
- `unauthorized`: Authentication required or token invalid
- `forbidden`: User does not have permission to access the resource
- `not_found`: Resource not found
- `validation_error`: Request validation failed
- `server_error`: Internal server error

## Testing the Integration

### 1. Start the Go Backend

```bash
go run cmd/api-gateway/main.go
```

### 2. Start the React Frontend

```bash
cd great-nigeria-frontend
npm start
```

### 3. Test API Endpoints

Use tools like Postman or curl to test the API endpoints:

```bash
# Test authentication
curl -X POST http://localhost:5000/api/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"password123"}'

# Test protected endpoint
curl -X GET http://localhost:5000/api/auth/me -H "Authorization: Bearer <token>"
```

### 4. Test CORS

Check the browser's developer console for any CORS-related errors when the React frontend makes requests to the Go backend.

## Conclusion

By following these instructions, you will have configured the Go backend to work with the React TypeScript frontend. The backend will handle API requests, authentication, and authorization, while the frontend will provide a modern, responsive user interface.
