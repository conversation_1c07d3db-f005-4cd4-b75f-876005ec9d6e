# Great Nigeria Platform - Development Documentation

This directory contains comprehensive documentation for developing and setting up the Great Nigeria platform.

## Main Documentation Files

- [SETUP_GUIDE.md](SETUP_GUIDE.md) - Detailed instructions for setting up the development environment
- [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md) - Coding standards, workflows, and best practices

## Overview

The development documentation provides essential information for developers working on the Great Nigeria platform. It covers environment setup, coding standards, development workflows, testing procedures, and best practices.

### Key Development Areas

1. **Environment Setup**
   - Prerequisites and required software
   - Repository setup and structure
   - Backend and frontend setup
   - Database configuration
   - Running the application locally

2. **Development Standards**
   - Coding standards for Go, JavaScript/TypeScript, and CSS/SCSS
   - Commit message standards
   - Git workflow and branching strategy
   - Code review process
   - Dependency management

3. **Testing Procedures**
   - Testing strategy and coverage requirements
   - Backend testing with Go
   - Frontend testing with Jest and React Testing Library
   - End-to-end testing with Cypress
   - Running and automating tests

4. **Performance and Security**
   - Backend and frontend performance considerations
   - Authentication and authorization best practices
   - Data protection guidelines
   - Common vulnerability prevention
   - Monitoring and profiling

### Development Workflow

The Great Nigeria platform follows a modified Git Flow workflow:

1. **Feature Development**:
   - Create feature branch from `develop`
   - Develop and test feature
   - Submit pull request
   - Address code review feedback
   - Merge to `develop`

2. **Release Process**:
   - Create release branch from `develop`
   - Perform final testing
   - Fix any release-specific issues
   - Merge to `main` and tag with version
   - Merge back to `develop`

3. **Hotfix Process**:
   - Create hotfix branch from `main`
   - Fix critical issue
   - Test thoroughly
   - Merge to `main` and tag with version
   - Merge to `develop`

## Getting Started

New developers should follow these steps to get started:

1. Read the [Setup Guide](SETUP_GUIDE.md) to set up your development environment
2. Review the [Development Guide](DEVELOPMENT_GUIDE.md) to understand coding standards and workflows
3. Set up the local development environment
4. Run the application locally
5. Make a small change to get familiar with the workflow

## Related Documentation

For more information about the project, refer to:
- [Architecture Documentation](../architecture/ARCHITECTURE_OVERVIEW.md) - Technical architecture
- [API Documentation](../api/) - API endpoints and usage
- [Database Documentation](../database/) - Database schema and management
