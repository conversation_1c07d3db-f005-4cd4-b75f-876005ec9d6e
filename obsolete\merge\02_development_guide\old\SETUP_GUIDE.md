# Great Nigeria Platform - Setup Guide

## Overview

This guide provides comprehensive instructions for setting up the Great Nigeria platform development environment. Follow these steps to get the platform running locally for development and testing.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Repository Setup](#repository-setup)
3. [Backend Setup](#backend-setup)
4. [Frontend Setup](#frontend-setup)
5. [Database Setup](#database-setup)
6. [Configuration](#configuration)
7. [Running the Application](#running-the-application)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

Before setting up the Great Nigeria platform, ensure you have the following prerequisites installed:

### Required Software

- **Git**: Version control system
  - Version 2.30.0 or higher
  - [Download Git](https://git-scm.com/downloads)

- **Go**: Backend programming language
  - Version 1.18 or higher
  - [Download Go](https://golang.org/dl/)

- **Node.js**: JavaScript runtime for frontend development
  - Version 16.x or higher
  - [Download Node.js](https://nodejs.org/)

- **PostgreSQL**: Database system
  - Version 13.x or higher
  - [Download PostgreSQL](https://www.postgresql.org/download/)

- **Docker** (optional): Containerization platform
  - Version 20.10.x or higher
  - [Download Docker](https://www.docker.com/products/docker-desktop)

### Development Tools

- **Visual Studio Code** (recommended): Code editor
  - [Download VS Code](https://code.visualstudio.com/)
  - Recommended extensions:
    - Go (by Go Team at Google)
    - ESLint (by Microsoft)
    - Prettier (by Prettier)
    - Docker (by Microsoft)

- **Postman** (optional): API testing tool
  - [Download Postman](https://www.postman.com/downloads/)

### System Requirements

- **Operating System**: Windows 10/11, macOS 10.15+, or Linux
- **RAM**: 8GB minimum, 16GB recommended
- **Disk Space**: 5GB minimum for source code, dependencies, and database

## Repository Setup

### Clone the Repository

1. Open a terminal or command prompt
2. Clone the repository:
   ```bash
   git clone https://github.com/greatnigeria/platform.git
   ```
3. Navigate to the project directory:
   ```bash
   cd platform
   ```

### Repository Structure

The repository is organized as follows:

```
platform/
├── cmd/                  # Application entry points
│   ├── api/              # API server
│   ├── worker/           # Background worker
│   └── cli/              # Command-line tools
├── internal/             # Internal packages
│   ├── auth/             # Authentication service
│   ├── content/          # Content service
│   ├── discussion/       # Discussion service
│   ├── payment/          # Payment service
│   ├── points/           # Points service
│   └── ...               # Other services
├── pkg/                  # Public packages
│   ├── common/           # Common utilities
│   ├── config/           # Configuration
│   └── models/           # Shared models
├── web/                  # Frontend code
│   ├── static/           # Static assets
│   ├── templates/        # HTML templates
│   └── src/              # JavaScript source
├── scripts/              # Utility scripts
├── migrations/           # Database migrations
├── docs/                 # Documentation
└── .env.example          # Example environment variables
```

## Backend Setup

### Install Go Dependencies

1. Navigate to the project root directory
2. Install Go dependencies:
   ```bash
   go mod download
   ```

### Build Backend Services

1. Build the API server:
   ```bash
   go build -o bin/api ./cmd/api
   ```

2. Build the worker (optional):
   ```bash
   go build -o bin/worker ./cmd/worker
   ```

## Frontend Setup

### Install Node.js Dependencies

1. Navigate to the web directory:
   ```bash
   cd web
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

### Build Frontend Assets

1. Build the frontend assets:
   ```bash
   npm run build
   ```

## Database Setup

### Configure PostgreSQL

1. Start PostgreSQL service if not already running
2. Create a new database:
   ```bash
   createdb great_nigeria
   ```

### Run Database Migrations

1. Navigate to the project root directory
2. Run the database migrations:
   ```bash
   ./scripts/migrate.ps1
   ```

### Load Sample Data (Optional)

1. Load sample data for development:
   ```bash
   ./scripts/seed_data.ps1
   ```

## Configuration

### Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your local configuration:
   ```
   # Database Configuration
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=great_nigeria
   DB_USER=postgres
   DB_PASSWORD=your_password

   # Server Configuration
   PORT=8080
   ENV=development
   DEBUG=true

   # JWT Configuration
   JWT_SECRET=your_jwt_secret
   JWT_EXPIRY=24h

   # Payment Gateway Configuration (if needed)
   PAYSTACK_SECRET_KEY=your_paystack_secret
   FLUTTERWAVE_SECRET_KEY=your_flutterwave_secret
   ```

### Application Configuration

1. Navigate to the `pkg/config` directory
2. Review and modify configuration files as needed:
   - `config.go`: Main configuration structure
   - `database.go`: Database configuration
   - `server.go`: Server configuration

## Running the Application

### Start the Backend Server

1. Navigate to the project root directory
2. Start the API server:
   ```bash
   ./bin/api
   ```
   Or using Go directly:
   ```bash
   go run ./cmd/api
   ```

### Start the Frontend Development Server (Optional)

1. Navigate to the web directory:
   ```bash
   cd web
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

### Using Docker (Optional)

1. Build and start all services using Docker Compose:
   ```bash
   docker-compose up --build
   ```

## Testing

### Run Backend Tests

1. Navigate to the project root directory
2. Run all tests:
   ```bash
   go test ./...
   ```

3. Run tests for a specific package:
   ```bash
   go test ./internal/auth/...
   ```

### Run Frontend Tests

1. Navigate to the web directory:
   ```bash
   cd web
   ```

2. Run frontend tests:
   ```bash
   npm test
   ```

## Troubleshooting

### Common Issues

#### Database Connection Issues

**Problem**: Unable to connect to the database
**Solution**:
1. Verify PostgreSQL is running:
   ```bash
   pg_isready
   ```
2. Check database credentials in `.env` file
3. Ensure the database exists:
   ```bash
   psql -l
   ```

#### Port Already in Use

**Problem**: Port already in use when starting the server
**Solution**:
1. Find the process using the port:
   ```bash
   netstat -ano | findstr :8080
   ```
2. Kill the process:
   ```bash
   taskkill /PID <PID> /F
   ```
3. Or change the port in the `.env` file

#### Go Module Issues

**Problem**: Issues with Go modules or dependencies
**Solution**:
1. Clear Go module cache:
   ```bash
   go clean -modcache
   ```
2. Update Go modules:
   ```bash
   go mod tidy
   ```

#### Frontend Build Issues

**Problem**: Errors when building frontend assets
**Solution**:
1. Clear Node.js cache:
   ```bash
   npm cache clean --force
   ```
2. Delete node_modules and reinstall:
   ```bash
   rm -rf node_modules
   npm install
   ```

### Getting Help

If you encounter issues not covered in this guide:

1. Check the project documentation in the `docs` directory
2. Search for similar issues in the project issue tracker
3. Contact the development <NAME_EMAIL>
