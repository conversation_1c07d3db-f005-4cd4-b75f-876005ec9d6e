

## API_DOCUMENTATION_PART1.md

# Great Nigeria Platform - API Documentation (Part 1)

## Overview

This document provides comprehensive documentation for the Great Nigeria platform's API. The API follows RESTful principles and is implemented using Go with the Gin web framework. The API is organized around microservices, each responsible for specific functionality.

## Table of Contents

1. [Overview](#overview)
2. [API Gateway](#api-gateway)
3. [Authentication](#authentication)
4. [Error Handling](#error-handling)
5. [Rate Limiting](#rate-limiting)
6. [Core Services](#core-services)
   - [Auth Service](#auth-service)
   - [User Service](#user-service)
   - [Content Service](#content-service)
   - [Social Service](#social-service)
   - [Discussion Service](#discussion-service)
   - [Points Service](#points-service)
   - [Payment Service](#payment-service)
7. [Additional Services](#additional-services)
8. [Webhooks](#webhooks)
9. [API Versioning](#api-versioning)
10. [Testing](#testing)

## API Gateway

The API Gateway serves as the central entry point for all client requests, providing:

- Routing to appropriate microservices
- Authentication and authorization
- Request/response transformation
- Rate limiting and throttling
- API documentation (Swagger)

### Base URL

```
https://api.greatnigeria.net/api
```

### Route Configuration

```go
// Example API Gateway route configuration in Go/Gin
func setupRoutes(r *gin.Engine) {
    // Public routes
    r.GET("/api/health", HealthCheck)
    r.GET("/api/books", ForwardToContentService)
    r.GET("/api/books/:id", ForwardToContentService)
    
    // Authentication routes
    r.POST("/api/register", ForwardToAuthService)
    r.POST("/api/login", ForwardToAuthService)
    r.POST("/api/logout", ForwardToAuthService)
    
    // Protected routes - require authentication
    authorized := r.Group("/api")
    authorized.Use(AuthMiddleware())
    {
        authorized.GET("/user", ForwardToAuthService)
        authorized.GET("/user/progress", ForwardToContentService)
        authorized.POST("/discussions", ForwardToDiscussionService)
        authorized.GET("/points", ForwardToPointsService)
        authorized.POST("/payments", ForwardToPaymentService)
    }
}
```

## Authentication

The Great Nigeria platform uses JWT (JSON Web Token) for authentication.

### JWT-Based Authentication

- Authentication header: `Authorization: Bearer {token}`
- Token structure: Header.Payload.Signature
- Claims: `sub`, `exp`, `iat`, `roles`, `permissions`
- Refresh token mechanism

### API Key Authentication (for services)

- API key header: `X-API-Key: {api_key}`
- Rate limiting by key
- Permission scoping

### Authentication Flow

1. **Registration**:
   - Client sends registration data to `/api/register`
   - Server validates data and creates user account
   - Server returns JWT token and refresh token

2. **Login**:
   - Client sends credentials to `/api/login`
   - Server validates credentials
   - Server returns JWT token and refresh token

3. **Token Refresh**:
   - Client sends refresh token to `/api/refresh-token`
   - Server validates refresh token
   - Server returns new JWT token

4. **Logout**:
   - Client sends request to `/api/logout`
   - Server invalidates refresh token
   - Client discards JWT token

### Example Authentication Requests

#### Registration

```http
POST /api/register HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "full_name": "New User"
}
```

Response:

```json
{
  "status": "success",
  "message": "User registered successfully",
  "data": {
    "user_id": "user-uuid",
    "username": "newuser",
    "email": "<EMAIL>",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh-token-uuid"
  }
}
```

#### Login

```http
POST /api/login HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

Response:

```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "user_id": "user-uuid",
    "username": "newuser",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh-token-uuid"
  }
}
```

## Error Handling

The API uses consistent error responses across all endpoints.

### Error Response Format

```json
{
  "status": "error",
  "code": "error_code",
  "message": "Human-readable error message",
  "details": {
    "field1": "Error details for field1",
    "field2": "Error details for field2"
  }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| `authentication_required` | Authentication is required for this endpoint |
| `invalid_credentials` | Invalid username or password |
| `invalid_token` | Invalid or expired token |
| `permission_denied` | User does not have permission for this action |
| `resource_not_found` | The requested resource was not found |
| `validation_error` | Request data failed validation |
| `rate_limit_exceeded` | Rate limit has been exceeded |
| `server_error` | Internal server error |

## Rate Limiting

The API implements rate limiting to prevent abuse and ensure fair usage.

### Rate Limit Implementation

- Token bucket algorithm with Redis backend
- Tiered rate limits based on user tier, IP address, and endpoint
- Graceful limiting with retry headers

### Rate Limit Headers

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1619194800
```

### Rate Limit Tiers

| User Tier | Requests per Minute |
|-----------|---------------------|
| Anonymous | 30 |
| Basic | 60 |
| Engaged | 100 |
| Active | 150 |
| Premium | 300 |

### Rate Limit Response

When a rate limit is exceeded, the API returns a 429 Too Many Requests response:

```json
{
  "status": "error",
  "code": "rate_limit_exceeded",
  "message": "Rate limit exceeded. Please try again later.",
  "details": {
    "retry_after": 30
  }
}
```


## API_DOCUMENTATION_PART2.md

# Great Nigeria Platform - API Documentation (Part 2)

## Core Services

### Auth Service

The Auth Service handles user authentication, registration, and authorization.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST | `/auth/register` | Register a new user | None |
| POST | `/auth/login` | Authenticate a user | None |
| POST | `/auth/refresh-token` | Refresh an access token | None |
| POST | `/auth/password/reset` | Request a password reset | None |
| POST | `/auth/logout` | Logout a user | Required |
| GET | `/auth/oauth/{provider}` | Authenticate with OAuth provider | None |
| POST | `/auth/2fa/enable` | Enable two-factor authentication | Required |
| POST | `/auth/2fa/validate` | Validate 2FA code | Required |

#### Example: Password Reset Request

```http
POST /api/auth/password/reset HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

Response:

```json
{
  "status": "success",
  "message": "Password reset instructions sent to your email"
}
```

### User Service

The User Service manages user profiles, relationships, and settings.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/users/{id}` | Get user details | Required |
| PATCH | `/users/{id}` | Update user details | Required |
| GET | `/users/{id}/profile` | Get user profile | Required |
| GET | `/users/{id}/friends` | Get user friends | Required |
| POST | `/users/{id}/friends/request` | Send friend request | Required |
| GET | `/users/{id}/followers` | Get user followers | Required |
| POST | `/users/{id}/follow` | Follow a user | Required |
| GET | `/users/{id}/features` | Get user feature toggles | Required |
| PATCH | `/users/{id}/features/{featureId}` | Update feature toggle | Required |

#### Example: Update User Profile

```http
PATCH /api/users/user-uuid HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "full_name": "Updated Name",
  "bio": "This is my updated bio",
  "profile_image": "https://example.com/image.jpg"
}
```

Response:

```json
{
  "status": "success",
  "message": "User updated successfully",
  "data": {
    "id": "user-uuid",
    "username": "username",
    "full_name": "Updated Name",
    "bio": "This is my updated bio",
    "profile_image": "https://example.com/image.jpg",
    "updated_at": "2025-04-23T14:30:00Z"
  }
}
```

### Content Service

The Content Service manages book content, chapters, sections, and user progress.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/books` | Get all books | Optional |
| GET | `/books/{id}` | Get book details | Optional |
| GET | `/books/{id}/chapters` | Get book chapters | Optional |
| GET | `/books/{id}/chapters/{chapterId}` | Get chapter details | Optional |
| GET | `/books/{id}/sections/{sectionId}` | Get section content | Optional |
| POST | `/books/{id}/progress` | Update reading progress | Required |
| POST | `/books/{id}/bookmarks` | Create bookmark | Required |
| POST | `/books/{id}/notes` | Create note | Required |

#### Example: Get Book Details

```http
GET /api/books/book-uuid HTTP/1.1
Host: api.greatnigeria.net
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Response:

```json
{
  "status": "success",
  "data": {
    "id": "book-uuid",
    "title": "Book Title",
    "description": "Book description",
    "author": "Author Name",
    "cover_image": "https://example.com/cover.jpg",
    "access_level": 1,
    "published_at": "2025-01-15T00:00:00Z",
    "chapters": [
      {
        "id": "chapter-uuid-1",
        "title": "Chapter 1",
        "order_number": 1
      },
      {
        "id": "chapter-uuid-2",
        "title": "Chapter 2",
        "order_number": 2
      }
    ]
  }
}
```

#### Example: Update Reading Progress

```http
POST /api/books/book-uuid/progress HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "chapter_id": "chapter-uuid",
  "section_id": "section-uuid",
  "position": 1250,
  "percent_complete": 45.5
}
```

Response:

```json
{
  "status": "success",
  "message": "Progress updated successfully",
  "data": {
    "book_id": "book-uuid",
    "chapter_id": "chapter-uuid",
    "section_id": "section-uuid",
    "position": 1250,
    "percent_complete": 45.5,
    "last_updated": "2025-04-23T14:35:00Z"
  }
}
```

### Social Service

The Social Service manages social interactions, posts, and content sharing.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/feed` | Get user feed | Required |
| POST | `/posts` | Create a post | Required |
| GET | `/posts/{id}` | Get post details | Required |
| PATCH | `/posts/{id}` | Update a post | Required |
| DELETE | `/posts/{id}` | Delete a post | Required |
| POST | `/posts/{id}/comments` | Comment on a post | Required |
| POST | `/posts/{id}/reactions` | React to a post | Required |
| GET | `/groups` | Get user groups | Required |
| POST | `/groups` | Create a group | Required |
| GET | `/groups/{id}/posts` | Get group posts | Required |

#### Example: Create a Post

```http
POST /api/posts HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "content": "This is my new post about the Great Nigeria platform!",
  "media_urls": ["https://example.com/image1.jpg"],
  "feeling": "excited",
  "privacy": "public"
}
```

Response:

```json
{
  "status": "success",
  "message": "Post created successfully",
  "data": {
    "id": "post-uuid",
    "user_id": "user-uuid",
    "content": "This is my new post about the Great Nigeria platform!",
    "media_urls": ["https://example.com/image1.jpg"],
    "feeling": "excited",
    "privacy": "public",
    "created_at": "2025-04-23T14:40:00Z"
  }
}
```


## API_DOCUMENTATION_PART3.md

# Great Nigeria Platform - API Documentation (Part 3)

## Core Services (Continued)

### Discussion Service

The Discussion Service manages forum topics, discussions, and comments.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/discussions/topics` | Get all topics | Optional |
| POST | `/discussions/topics` | Create a topic | Required |
| GET | `/discussions/topics/{id}` | Get topic details | Optional |
| PATCH | `/discussions/topics/{id}` | Update a topic | Required |
| DELETE | `/discussions/topics/{id}` | Delete a topic | Required |
| POST | `/discussions/topics/{id}/comments` | Comment on a topic | Required |
| GET | `/discussions/topics/{id}/comments` | Get topic comments | Optional |
| POST | `/discussions/comments/{id}/replies` | Reply to a comment | Required |
| PATCH | `/discussions/comments/{id}` | Update a comment | Required |
| DELETE | `/discussions/comments/{id}` | Delete a comment | Required |

#### Example: Create a Discussion Topic

```http
POST /api/discussions/topics HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "title": "Discussing Chapter 3 of Book 1",
  "content": "I found the analysis of Nigeria's economic challenges in Chapter 3 very insightful. What do others think?",
  "type": "forum_topic",
  "book_id": "book-uuid",
  "chapter_id": "chapter-uuid",
  "section_id": "section-uuid"
}
```

Response:

```json
{
  "status": "success",
  "message": "Topic created successfully",
  "data": {
    "id": "topic-uuid",
    "user_id": "user-uuid",
    "title": "Discussing Chapter 3 of Book 1",
    "content": "I found the analysis of Nigeria's economic challenges in Chapter 3 very insightful. What do others think?",
    "type": "forum_topic",
    "book_id": "book-uuid",
    "chapter_id": "chapter-uuid",
    "section_id": "section-uuid",
    "views": 0,
    "created_at": "2025-04-23T14:45:00Z"
  }
}
```

### Points Service

The Points Service manages the points-based reward system.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/points/balance` | Get user points balance | Required |
| GET | `/points/history` | Get points transaction history | Required |
| GET | `/points/leaderboard` | Get points leaderboard | Optional |
| POST | `/points/award` | Award points (admin only) | Required |
| GET | `/points/levels` | Get membership levels | Optional |
| GET | `/points/user/level` | Get user's current level | Required |

#### Example: Get Points Balance

```http
GET /api/points/balance HTTP/1.1
Host: api.greatnigeria.net
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Response:

```json
{
  "status": "success",
  "data": {
    "user_id": "user-uuid",
    "points_balance": 1250,
    "membership_level": 2,
    "level_name": "Engaged",
    "next_level": {
      "level": 3,
      "name": "Active",
      "points_required": 2000,
      "points_needed": 750
    }
  }
}
```

### Payment Service

The Payment Service handles payment processing, transactions, and subscriptions.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST | `/payments/intent` | Create payment intent | Required |
| POST | `/payments/process` | Process payment | Required |
| GET | `/payments/transactions` | Get transaction history | Required |
| GET | `/wallet/balance` | Get wallet balance | Required |
| POST | `/wallet/deposit` | Deposit to wallet | Required |
| POST | `/wallet/withdraw` | Withdraw from wallet | Required |
| GET | `/subscriptions` | Get user subscriptions | Required |
| POST | `/subscriptions` | Create subscription | Required |
| GET | `/plans` | Get available plans | Optional |

#### Example: Create Payment Intent

```http
POST /api/payments/intent HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "amount": 5000,
  "currency": "NGN",
  "payment_type": "premium",
  "gateway": "paystack",
  "description": "Premium membership subscription"
}
```

Response:

```json
{
  "status": "success",
  "message": "Payment intent created",
  "data": {
    "intent_id": "intent-uuid",
    "amount": 5000,
    "currency": "NGN",
    "payment_type": "premium",
    "gateway": "paystack",
    "authorization_url": "https://checkout.paystack.com/0x8f5tghjk",
    "reference": "GN-PAY-12345",
    "expires_at": "2025-04-23T15:45:00Z"
  }
}
```

## Additional Services

### Analytics Service

The Analytics Service tracks user behavior and content performance.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/analytics/user/activity` | Get user activity | Required |
| GET | `/analytics/content/performance` | Get content performance | Required (Admin) |
| GET | `/analytics/trends` | Get trending content | Required (Admin) |

### Chat Service

The Chat Service manages real-time messaging.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/chat/conversations` | Get user conversations | Required |
| POST | `/chat/conversations` | Create conversation | Required |
| GET | `/chat/conversations/{id}/messages` | Get conversation messages | Required |
| POST | `/chat/conversations/{id}/messages` | Send message | Required |

### Notification Service

The Notification Service manages user notifications.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/notifications` | Get user notifications | Required |
| PATCH | `/notifications/{id}` | Mark notification as read | Required |
| PATCH | `/notifications/read-all` | Mark all notifications as read | Required |
| GET | `/notifications/settings` | Get notification settings | Required |
| PATCH | `/notifications/settings` | Update notification settings | Required |

## Webhooks

The Great Nigeria platform provides webhooks for integrating with external systems.

### Available Webhooks

| Event | Description |
|-------|-------------|
| `user.registered` | Triggered when a new user registers |
| `user.upgraded` | Triggered when a user upgrades their membership |
| `payment.completed` | Triggered when a payment is completed |
| `payment.failed` | Triggered when a payment fails |
| `content.published` | Triggered when new content is published |

### Webhook Payload Format

```json
{
  "event": "payment.completed",
  "timestamp": "2025-04-23T14:50:00Z",
  "data": {
    // Event-specific data
  }
}
```

### Webhook Security

- Webhooks are signed using HMAC-SHA256
- The signature is included in the `X-Signature` header
- Verify the signature using your webhook secret

## API Versioning

The Great Nigeria platform uses URL-based versioning for the API.

### Version Format

```
https://api.greatnigeria.net/api/v1/resource
```

### Current Versions

| Version | Status | Release Date | End of Life |
|---------|--------|--------------|-------------|
| v1 | Current | 2025-01-01 | TBD |

### Version Changes

When a new version is released, the previous version will be supported for at least 6 months. Deprecation notices will be sent to all API users.

## Testing

The Great Nigeria platform provides a sandbox environment for testing API integrations.

### Sandbox Environment

```
https://sandbox-api.greatnigeria.net/api
```

### Test Accounts

| Email | Password | Description |
|-------|----------|-------------|
| `<EMAIL>` | `test123` | Basic user |
| `<EMAIL>` | `test123` | Premium user |
| `<EMAIL>` | `test123` | Admin user |

### Test Payment Cards

| Card Number | Expiry | CVV | Description |
|-------------|--------|-----|-------------|
| 4111 1111 1111 1111 | 12/25 | 123 | Successful payment |
| 4242 4242 4242 4242 | 12/25 | 123 | Failed payment |

## Conclusion

This documentation provides a comprehensive overview of the Great Nigeria platform's API. For additional support or questions, please contact the API <NAME_EMAIL>.


## API_ENDPOINTS_FOR_FRONTEND.md

# API Endpoints for Frontend Integration

This document outlines the API endpoints that the React frontend will need to interact with. It serves as a reference for frontend developers implementing the React TypeScript frontend.

## Authentication Endpoints

### Register User

- **URL**: `/api/auth/register`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "name": "User Name",
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "user": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>"
    },
    "token": "jwt_token"
  }
  ```

### Login User

- **URL**: `/api/auth/login`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "user": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>"
    },
    "token": "jwt_token"
  }
  ```

### Get Current User

- **URL**: `/api/auth/me`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>"
  }
  ```

## Book Endpoints

### Get All Books

- **URL**: `/api/books`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "title": "Book 1: Awakening the Giant",
      "description": "Book description",
      "cover_image": "/static/images/book1_cover.jpg",
      "author": "Author Name",
      "published_date": "2023-01-01"
    },
    {
      "id": "2",
      "title": "Book 2: Building the Future",
      "description": "Book description",
      "cover_image": "/static/images/book2_cover.jpg",
      "author": "Author Name",
      "published_date": "2023-02-01"
    }
  ]
  ```

### Get Book by ID

- **URL**: `/api/books/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Book 1: Awakening the Giant",
    "description": "Book description",
    "cover_image": "/static/images/book1_cover.jpg",
    "author": "Author Name",
    "published_date": "2023-01-01",
    "chapters_count": 10,
    "total_pages": 250
  }
  ```

### Get Book Chapters

- **URL**: `/api/books/:id/chapters`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "title": "Chapter 1: Introduction",
      "order": 1,
      "sections": [
        {
          "id": "1",
          "title": "Section 1.1",
          "order": 1
        },
        {
          "id": "2",
          "title": "Section 1.2",
          "order": 2
        }
      ]
    },
    {
      "id": "2",
      "title": "Chapter 2: Getting Started",
      "order": 2,
      "sections": [
        {
          "id": "3",
          "title": "Section 2.1",
          "order": 1
        },
        {
          "id": "4",
          "title": "Section 2.2",
          "order": 2
        }
      ]
    }
  ]
  ```

### Get Chapter by ID

- **URL**: `/api/books/chapters/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Chapter 1: Introduction",
    "order": 1,
    "book_id": "1",
    "sections": [
      {
        "id": "1",
        "title": "Section 1.1",
        "order": 1
      },
      {
        "id": "2",
        "title": "Section 1.2",
        "order": 2
      }
    ]
  }
  ```

### Get Section by ID

- **URL**: `/api/books/sections/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Section 1.1",
    "order": 1,
    "chapter_id": "1",
    "content": "<p>Section content in HTML format</p>",
    "next_section_id": "2",
    "prev_section_id": null
  }
  ```

### Save Reading Progress

- **URL**: `/api/books/:id/progress`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer jwt_token`
- **Request Body**:
  ```json
  {
    "sectionId": "1"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Reading progress saved"
  }
  ```

## User Profile Endpoints

### Get User Profile

- **URL**: `/api/users/:id/profile`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "bio": "User bio",
    "avatar": "/static/images/avatars/user_avatar.jpg",
    "createdAt": "2023-01-01T00:00:00Z",
    "points": 100,
    "level": 2
  }
  ```

### Get Reading Statistics

- **URL**: `/api/users/:id/reading-stats`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  {
    "books_started": 3,
    "books_completed": 1,
    "total_pages_read": 150,
    "reading_streak": 5,
    "average_reading_time": 30,
    "reading_history": [
      {
        "date": "2023-04-01",
        "pages_read": 10
      },
      {
        "date": "2023-04-02",
        "pages_read": 15
      }
    ]
  }
  ```

### Get User Bookmarks

- **URL**: `/api/users/:id/bookmarks`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "book_id": "1",
      "book_title": "Book 1: Awakening the Giant",
      "section_id": "1",
      "section_title": "Section 1.1",
      "created_at": "2023-04-01T00:00:00Z",
      "note": "Important section about leadership"
    },
    {
      "id": "2",
      "book_id": "2",
      "book_title": "Book 2: Building the Future",
      "section_id": "3",
      "section_title": "Section 2.1",
      "created_at": "2023-04-02T00:00:00Z",
      "note": "Key concepts about community building"
    }
  ]
  ```

### Get User Activities

- **URL**: `/api/users/:id/activities`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer jwt_token`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "type": "reading",
      "description": "Started reading Book 1",
      "created_at": "2023-04-01T00:00:00Z",
      "book_id": "1",
      "book_title": "Book 1: Awakening the Giant"
    },
    {
      "id": "2",
      "type": "forum",
      "description": "Posted in forum topic: Community Building",
      "created_at": "2023-04-02T00:00:00Z",
      "topic_id": "1",
      "topic_title": "Community Building"
    }
  ]
  ```

## Forum Endpoints

### Get Forum Categories

- **URL**: `/api/forum/categories`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "name": "General Discussion",
      "description": "General discussion about Nigeria",
      "topics_count": 10
    },
    {
      "id": "2",
      "name": "Book Discussion",
      "description": "Discuss the books and their content",
      "topics_count": 5
    }
  ]
  ```

### Get Topics by Category

- **URL**: `/api/forum/categories/:id/topics`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "title": "Community Building",
      "author": {
        "id": "user_id",
        "name": "User Name"
      },
      "created_at": "2023-04-01T00:00:00Z",
      "replies_count": 5,
      "last_reply_at": "2023-04-02T00:00:00Z"
    },
    {
      "id": "2",
      "title": "Leadership in Nigeria",
      "author": {
        "id": "user_id",
        "name": "User Name"
      },
      "created_at": "2023-04-02T00:00:00Z",
      "replies_count": 3,
      "last_reply_at": "2023-04-03T00:00:00Z"
    }
  ]
  ```

### Get Topic by ID

- **URL**: `/api/forum/topics/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Community Building",
    "content": "<p>Topic content in HTML format</p>",
    "author": {
      "id": "user_id",
      "name": "User Name",
      "avatar": "/static/images/avatars/user_avatar.jpg"
    },
    "created_at": "2023-04-01T00:00:00Z",
    "category": {
      "id": "1",
      "name": "General Discussion"
    },
    "replies": [
      {
        "id": "1",
        "content": "<p>Reply content in HTML format</p>",
        "author": {
          "id": "user_id",
          "name": "User Name",
          "avatar": "/static/images/avatars/user_avatar.jpg"
        },
        "created_at": "2023-04-02T00:00:00Z",
        "votes": 5
      }
    ]
  }
  ```

### Create Topic

- **URL**: `/api/forum/topics`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer jwt_token`
- **Request Body**:
  ```json
  {
    "title": "New Topic",
    "content": "<p>Topic content in HTML format</p>",
    "category_id": "1"
  }
  ```
- **Response**:
  ```json
  {
    "id": "3",
    "title": "New Topic",
    "content": "<p>Topic content in HTML format</p>",
    "author": {
      "id": "user_id",
      "name": "User Name",
      "avatar": "/static/images/avatars/user_avatar.jpg"
    },
    "created_at": "2023-04-03T00:00:00Z",
    "category": {
      "id": "1",
      "name": "General Discussion"
    }
  }
  ```

### Create Reply

- **URL**: `/api/forum/topics/:id/replies`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer jwt_token`
- **Request Body**:
  ```json
  {
    "content": "<p>Reply content in HTML format</p>"
  }
  ```
- **Response**:
  ```json
  {
    "id": "2",
    "content": "<p>Reply content in HTML format</p>",
    "author": {
      "id": "user_id",
      "name": "User Name",
      "avatar": "/static/images/avatars/user_avatar.jpg"
    },
    "created_at": "2023-04-03T00:00:00Z",
    "votes": 0
  }
  ```

## Resources Endpoints

### Get Resource Categories

- **URL**: `/api/resources/categories`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "name": "Implementation Tools",
      "description": "Tools for implementing the ideas from the books",
      "resources_count": 5
    },
    {
      "id": "2",
      "name": "Templates",
      "description": "Templates for various documents and plans",
      "resources_count": 3
    }
  ]
  ```

### Get Resources by Category

- **URL**: `/api/resources/categories/:id/resources`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "title": "Community Building Toolkit",
      "description": "A toolkit for building communities",
      "file_type": "pdf",
      "file_size": 1024,
      "download_url": "/api/resources/1/download",
      "created_at": "2023-04-01T00:00:00Z",
      "downloads_count": 100
    },
    {
      "id": "2",
      "title": "Leadership Workshop Guide",
      "description": "A guide for conducting leadership workshops",
      "file_type": "pdf",
      "file_size": 2048,
      "download_url": "/api/resources/2/download",
      "created_at": "2023-04-02T00:00:00Z",
      "downloads_count": 50
    }
  ]
  ```

### Get Resource by ID

- **URL**: `/api/resources/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "title": "Community Building Toolkit",
    "description": "A toolkit for building communities",
    "content": "<p>Resource description in HTML format</p>",
    "file_type": "pdf",
    "file_size": 1024,
    "download_url": "/api/resources/1/download",
    "created_at": "2023-04-01T00:00:00Z",
    "downloads_count": 100,
    "category": {
      "id": "1",
      "name": "Implementation Tools"
    },
    "related_resources": [
      {
        "id": "2",
        "title": "Leadership Workshop Guide"
      }
    ]
  }
  ```

### Download Resource

- **URL**: `/api/resources/:id/download`
- **Method**: `GET`
- **Response**: Binary file download

## Celebrate Nigeria Endpoints

### Get Featured Entries

- **URL**: `/api/celebrate/featured`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "type": "person",
      "name": "Chinua Achebe",
      "slug": "chinua-achebe",
      "image_url": "/static/images/celebrate/chinua-achebe.jpg",
      "summary": "Renowned Nigerian novelist, poet, and critic",
      "featured": true
    },
    {
      "id": "2",
      "type": "place",
      "name": "Zuma Rock",
      "slug": "zuma-rock",
      "image_url": "/static/images/celebrate/zuma-rock.jpg",
      "summary": "Monolith located in Niger State, Nigeria",
      "featured": true
    }
  ]
  ```

### Get Entry by Type and Slug

- **URL**: `/api/celebrate/:type/:slug`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "id": "1",
    "type": "person",
    "name": "Chinua Achebe",
    "slug": "chinua-achebe",
    "image_url": "/static/images/celebrate/chinua-achebe.jpg",
    "summary": "Renowned Nigerian novelist, poet, and critic",
    "description": "<p>Detailed description in HTML format</p>",
    "birth_date": "1930-11-16",
    "death_date": "2013-03-21",
    "achievements": [
      "Published 'Things Fall Apart' in 1958",
      "Won the Man Booker International Prize in 2007"
    ],
    "facts": [
      {
        "title": "Education",
        "content": "University College, Ibadan"
      },
      {
        "title": "Notable Works",
        "content": "Things Fall Apart, No Longer at Ease, Arrow of God"
      }
    ],
    "media": [
      {
        "type": "image",
        "url": "/static/images/celebrate/chinua-achebe-1.jpg",
        "caption": "Chinua Achebe in 1960"
      },
      {
        "type": "video",
        "url": "https://www.youtube.com/watch?v=example",
        "caption": "Interview with Chinua Achebe"
      }
    ],
    "related_entries": [
      {
        "id": "3",
        "type": "person",
        "name": "Wole Soyinka",
        "slug": "wole-soyinka"
      }
    ]
  }
  ```

### Search Entries

- **URL**: `/api/celebrate/search`
- **Method**: `GET`
- **Query Parameters**:
  - `q`: Search query
  - `type`: Entry type (person, place, event)
  - `category`: Category ID
- **Response**:
  ```json
  [
    {
      "id": "1",
      "type": "person",
      "name": "Chinua Achebe",
      "slug": "chinua-achebe",
      "image_url": "/static/images/celebrate/chinua-achebe.jpg",
      "summary": "Renowned Nigerian novelist, poet, and critic"
    },
    {
      "id": "3",
      "type": "person",
      "name": "Wole Soyinka",
      "slug": "wole-soyinka",
      "image_url": "/static/images/celebrate/wole-soyinka.jpg",
      "summary": "Nigerian playwright, novelist, and poet"
    }
  ]
  ```

### Get Categories

- **URL**: `/api/celebrate/categories`
- **Method**: `GET`
- **Response**:
  ```json
  [
    {
      "id": "1",
      "name": "Literature",
      "entries_count": 5
    },
    {
      "id": "2",
      "name": "Natural Landmarks",
      "entries_count": 3
    }
  ]
  ```

### Submit Entry

- **URL**: `/api/celebrate/submit`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer jwt_token`
- **Request Body**:
  ```json
  {
    "type": "person",
    "name": "New Person",
    "summary": "Summary of the person",
    "description": "<p>Detailed description in HTML format</p>",
    "birth_date": "1980-01-01",
    "achievements": [
      "Achievement 1",
      "Achievement 2"
    ],
    "facts": [
      {
        "title": "Education",
        "content": "University of Lagos"
      }
    ],
    "category_ids": [1, 2]
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Entry submitted for moderation",
    "entry_id": "4"
  }
  ```

## Error Responses

All endpoints return standard error responses in the following format:

```json
{
  "error": {
    "code": "error_code",
    "message": "Error message",
    "details": "Additional error details (optional)"
  }
}
```

Common error codes:
- `unauthorized`: Authentication required or token invalid
- `forbidden`: User does not have permission to access the resource
- `not_found`: Resource not found
- `validation_error`: Request validation failed
- `server_error`: Internal server error


## README.md

# Great Nigeria Platform - API Documentation

This directory contains comprehensive documentation for the Great Nigeria platform's API.

## Main Documentation Files

- [API_DOCUMENTATION_PART1.md](API_DOCUMENTATION_PART1.md) - Part 1 of the API documentation, covering overview, API gateway, authentication, error handling, and rate limiting
- [API_DOCUMENTATION_PART2.md](API_DOCUMENTATION_PART2.md) - Part 2 of the API documentation, covering core services (Auth, User, Content, Social)
- [API_DOCUMENTATION_PART3.md](API_DOCUMENTATION_PART3.md) - Part 3 of the API documentation, covering additional services, webhooks, versioning, and testing

## Overview

The Great Nigeria platform provides a comprehensive RESTful API that allows developers to interact with all aspects of the platform. The API is organized around microservices, each responsible for specific functionality.

### Key Features

- **RESTful Design**: Follows REST principles for intuitive resource-based URLs
- **JWT Authentication**: Secure authentication using JSON Web Tokens
- **Comprehensive Error Handling**: Consistent error responses with detailed information
- **Rate Limiting**: Tiered rate limits to ensure fair usage
- **Versioning**: URL-based versioning for API stability
- **Webhooks**: Event-based integration with external systems
- **Sandbox Environment**: Testing environment for integration development

## API Gateway

The API Gateway serves as the central entry point for all client requests, providing:

- Routing to appropriate microservices
- Authentication and authorization
- Request/response transformation
- Rate limiting and throttling
- API documentation (Swagger)

## Core Services

The API is organized into several core services:

- **Auth Service**: User authentication, registration, and authorization
- **User Service**: User profiles, relationships, and settings
- **Content Service**: Book content, chapters, sections, and user progress
- **Social Service**: Social interactions, posts, and content sharing
- **Discussion Service**: Forum topics, discussions, and comments
- **Points Service**: Points-based reward system
- **Payment Service**: Payment processing, transactions, and subscriptions

## Additional Services

The platform also includes several specialized services:

- **Analytics Service**: User behavior tracking and content performance metrics
- **Chat Service**: Real-time messaging
- **Notification Service**: User notifications

## Related Documentation

For more information about the project, refer to:
- [Architecture Documentation](../architecture/ARCHITECTURE_OVERVIEW.md) - Details of the system architecture
- [Database Documentation](../database/) - Database schema and management
- [Code Analysis Documentation](../code/) - Analysis of the codebase
