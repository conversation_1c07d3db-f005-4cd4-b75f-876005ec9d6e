# Great Nigeria Platform - API Documentation (Part 3)

## Core Services (Continued)

### Discussion Service

The Discussion Service manages forum topics, discussions, and comments.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/discussions/topics` | Get all topics | Optional |
| POST | `/discussions/topics` | Create a topic | Required |
| GET | `/discussions/topics/{id}` | Get topic details | Optional |
| PATCH | `/discussions/topics/{id}` | Update a topic | Required |
| DELETE | `/discussions/topics/{id}` | Delete a topic | Required |
| POST | `/discussions/topics/{id}/comments` | Comment on a topic | Required |
| GET | `/discussions/topics/{id}/comments` | Get topic comments | Optional |
| POST | `/discussions/comments/{id}/replies` | Reply to a comment | Required |
| PATCH | `/discussions/comments/{id}` | Update a comment | Required |
| DELETE | `/discussions/comments/{id}` | Delete a comment | Required |

#### Example: Create a Discussion Topic

```http
POST /api/discussions/topics HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "title": "Discussing Chapter 3 of Book 1",
  "content": "I found the analysis of Nigeria's economic challenges in Chapter 3 very insightful. What do others think?",
  "type": "forum_topic",
  "book_id": "book-uuid",
  "chapter_id": "chapter-uuid",
  "section_id": "section-uuid"
}
```

Response:

```json
{
  "status": "success",
  "message": "Topic created successfully",
  "data": {
    "id": "topic-uuid",
    "user_id": "user-uuid",
    "title": "Discussing Chapter 3 of Book 1",
    "content": "I found the analysis of Nigeria's economic challenges in Chapter 3 very insightful. What do others think?",
    "type": "forum_topic",
    "book_id": "book-uuid",
    "chapter_id": "chapter-uuid",
    "section_id": "section-uuid",
    "views": 0,
    "created_at": "2025-04-23T14:45:00Z"
  }
}
```

### Points Service

The Points Service manages the points-based reward system.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/points/balance` | Get user points balance | Required |
| GET | `/points/history` | Get points transaction history | Required |
| GET | `/points/leaderboard` | Get points leaderboard | Optional |
| POST | `/points/award` | Award points (admin only) | Required |
| GET | `/points/levels` | Get membership levels | Optional |
| GET | `/points/user/level` | Get user's current level | Required |

#### Example: Get Points Balance

```http
GET /api/points/balance HTTP/1.1
Host: api.greatnigeria.net
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Response:

```json
{
  "status": "success",
  "data": {
    "user_id": "user-uuid",
    "points_balance": 1250,
    "membership_level": 2,
    "level_name": "Engaged",
    "next_level": {
      "level": 3,
      "name": "Active",
      "points_required": 2000,
      "points_needed": 750
    }
  }
}
```

### Payment Service

The Payment Service handles payment processing, transactions, and subscriptions.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST | `/payments/intent` | Create payment intent | Required |
| POST | `/payments/process` | Process payment | Required |
| GET | `/payments/transactions` | Get transaction history | Required |
| GET | `/wallet/balance` | Get wallet balance | Required |
| POST | `/wallet/deposit` | Deposit to wallet | Required |
| POST | `/wallet/withdraw` | Withdraw from wallet | Required |
| GET | `/subscriptions` | Get user subscriptions | Required |
| POST | `/subscriptions` | Create subscription | Required |
| GET | `/plans` | Get available plans | Optional |

#### Example: Create Payment Intent

```http
POST /api/payments/intent HTTP/1.1
Host: api.greatnigeria.net
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "amount": 5000,
  "currency": "NGN",
  "payment_type": "premium",
  "gateway": "paystack",
  "description": "Premium membership subscription"
}
```

Response:

```json
{
  "status": "success",
  "message": "Payment intent created",
  "data": {
    "intent_id": "intent-uuid",
    "amount": 5000,
    "currency": "NGN",
    "payment_type": "premium",
    "gateway": "paystack",
    "authorization_url": "https://checkout.paystack.com/0x8f5tghjk",
    "reference": "GN-PAY-12345",
    "expires_at": "2025-04-23T15:45:00Z"
  }
}
```

## Additional Services

### Analytics Service

The Analytics Service tracks user behavior and content performance.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/analytics/user/activity` | Get user activity | Required |
| GET | `/analytics/content/performance` | Get content performance | Required (Admin) |
| GET | `/analytics/trends` | Get trending content | Required (Admin) |

### Chat Service

The Chat Service manages real-time messaging.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/chat/conversations` | Get user conversations | Required |
| POST | `/chat/conversations` | Create conversation | Required |
| GET | `/chat/conversations/{id}/messages` | Get conversation messages | Required |
| POST | `/chat/conversations/{id}/messages` | Send message | Required |

### Notification Service

The Notification Service manages user notifications.

#### Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/notifications` | Get user notifications | Required |
| PATCH | `/notifications/{id}` | Mark notification as read | Required |
| PATCH | `/notifications/read-all` | Mark all notifications as read | Required |
| GET | `/notifications/settings` | Get notification settings | Required |
| PATCH | `/notifications/settings` | Update notification settings | Required |

## Webhooks

The Great Nigeria platform provides webhooks for integrating with external systems.

### Available Webhooks

| Event | Description |
|-------|-------------|
| `user.registered` | Triggered when a new user registers |
| `user.upgraded` | Triggered when a user upgrades their membership |
| `payment.completed` | Triggered when a payment is completed |
| `payment.failed` | Triggered when a payment fails |
| `content.published` | Triggered when new content is published |

### Webhook Payload Format

```json
{
  "event": "payment.completed",
  "timestamp": "2025-04-23T14:50:00Z",
  "data": {
    // Event-specific data
  }
}
```

### Webhook Security

- Webhooks are signed using HMAC-SHA256
- The signature is included in the `X-Signature` header
- Verify the signature using your webhook secret

## API Versioning

The Great Nigeria platform uses URL-based versioning for the API.

### Version Format

```
https://api.greatnigeria.net/api/v1/resource
```

### Current Versions

| Version | Status | Release Date | End of Life |
|---------|--------|--------------|-------------|
| v1 | Current | 2025-01-01 | TBD |

### Version Changes

When a new version is released, the previous version will be supported for at least 6 months. Deprecation notices will be sent to all API users.

## Testing

The Great Nigeria platform provides a sandbox environment for testing API integrations.

### Sandbox Environment

```
https://sandbox-api.greatnigeria.net/api
```

### Test Accounts

| Email | Password | Description |
|-------|----------|-------------|
| `<EMAIL>` | `test123` | Basic user |
| `<EMAIL>` | `test123` | Premium user |
| `<EMAIL>` | `test123` | Admin user |

### Test Payment Cards

| Card Number | Expiry | CVV | Description |
|-------------|--------|-----|-------------|
| 4111 1111 1111 1111 | 12/25 | 123 | Successful payment |
| 4242 4242 4242 4242 | 12/25 | 123 | Failed payment |

## Conclusion

This documentation provides a comprehensive overview of the Great Nigeria platform's API. For additional support or questions, please contact the API <NAME_EMAIL>.
