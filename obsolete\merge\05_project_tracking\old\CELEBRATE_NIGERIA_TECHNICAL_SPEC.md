# Celebrate Nigeria Feature - Technical Specification

## Architecture Overview

The Celebrate Nigeria feature follows a layered architecture pattern:

1. **Models Layer**: Data structures and relationships
2. **Repository Layer**: Database operations and data access
3. **Service Layer**: Business logic and operations
4. **Handler Layer**: API endpoints and request handling
5. **Frontend Layer**: User interface and client-side logic

## Database Schema

### Core Tables

#### `celebration_categories`
```sql
CREATE TABLE celebration_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    slug VARCHAR(100) NOT NULL UNIQUE,
    parent_id INTEGER REFERENCES celebration_categories(id),
    image_url VARCHAR(255),
    icon_svg TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    visible BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### `celebration_entries`
```sql
CREATE TABLE celebration_entries (
    id SERIAL PRIMARY KEY,
    entry_type VARCHAR(20) NOT NULL, -- 'person', 'place', 'event'
    slug VARCHAR(100) NOT NULL UNIQUE,
    title VARCHAR(200) NOT NULL,
    short_desc VARCHAR(500) NOT NULL,
    full_desc TEXT,
    primary_image_url VARCHAR(255),
    location VARCHAR(255),
    featured_rank INTEGER NOT NULL DEFAULT 0,
    view_count BIGINT NOT NULL DEFAULT 0,
    like_count BIGINT NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'draft', -- 'draft', 'published', 'archived'
    search_vector TSVECTOR,
    submitted_by INTEGER,
    approved_by INTEGER,
    approved_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### Type-Specific Tables

#### `celebration_people`
```sql
CREATE TABLE celebration_people (
    id INTEGER PRIMARY KEY REFERENCES celebration_entries(id),
    birth_date DATE,
    death_date DATE,
    profession VARCHAR(255),
    achievements TEXT,
    contributions TEXT,
    education TEXT,
    related_links TEXT
);
```

#### `celebration_places`
```sql
CREATE TABLE celebration_places (
    id INTEGER PRIMARY KEY REFERENCES celebration_entries(id),
    place_type VARCHAR(100),
    latitude FLOAT,
    longitude FLOAT,
    address TEXT,
    visiting_hours TEXT,
    visiting_fees TEXT,
    accessibility TEXT,
    history TEXT
);
```

#### `celebration_events`
```sql
CREATE TABLE celebration_events (
    id INTEGER PRIMARY KEY REFERENCES celebration_entries(id),
    event_type VARCHAR(100),
    start_date DATE,
    end_date DATE,
    is_recurring BOOLEAN NOT NULL DEFAULT false,
    recurrence_pattern VARCHAR(255),
    organizer VARCHAR(255),
    contact_info TEXT,
    event_history TEXT
);
```

### Relationship Tables

#### `celebration_entry_categories`
```sql
CREATE TABLE celebration_entry_categories (
    entry_id INTEGER REFERENCES celebration_entries(id),
    category_id INTEGER REFERENCES celebration_categories(id),
    PRIMARY KEY (entry_id, category_id)
);
```

#### `celebration_entry_media`
```sql
CREATE TABLE celebration_entry_media (
    id SERIAL PRIMARY KEY,
    celebration_entry_id INTEGER REFERENCES celebration_entries(id),
    media_type VARCHAR(50) NOT NULL, -- 'image', 'video', 'audio', 'document'
    url VARCHAR(255) NOT NULL,
    caption TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### `celebration_entry_facts`
```sql
CREATE TABLE celebration_entry_facts (
    id SERIAL PRIMARY KEY,
    celebration_entry_id INTEGER REFERENCES celebration_entries(id),
    label VARCHAR(100) NOT NULL,
    value TEXT NOT NULL,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### User Interaction Tables

#### `celebration_entry_comments`
```sql
CREATE TABLE celebration_entry_comments (
    id SERIAL PRIMARY KEY,
    celebration_entry_id INTEGER REFERENCES celebration_entries(id),
    user_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
    likes INTEGER NOT NULL DEFAULT 0,
    parent_id INTEGER REFERENCES celebration_entry_comments(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### `celebration_entry_submissions`
```sql
CREATE TABLE celebration_entry_submissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    entry_type VARCHAR(20) NOT NULL, -- 'person', 'place', 'event'
    target_entry_id INTEGER REFERENCES celebration_entries(id), -- NULL for new entries
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
    admin_notes TEXT,
    reviewed_by INTEGER,
    reviewed_at TIMESTAMP,
    vote_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## API Endpoints

### Categories

- `GET /api/celebrate/categories` - Get all categories
- `GET /api/celebrate/categories/:id` - Get category by ID
- `GET /api/celebrate/categories/:slug` - Get category by slug
- `GET /api/celebrate/categories/:id/entries` - Get entries for a category

### Entries

- `GET /api/celebrate/entries` - Get all entries (with pagination)
- `GET /api/celebrate/entries/:id` - Get entry by ID
- `GET /api/celebrate/entries/:slug` - Get entry by slug
- `GET /api/celebrate/entries/featured` - Get featured entries
- `GET /api/celebrate/entries/type/:type` - Get entries by type (person, place, event)
- `POST /api/celebrate/entries/:id/like` - Like an entry
- `POST /api/celebrate/entries/:id/view` - Record a view for an entry

### Search

- `GET /api/celebrate/search` - Search entries with query parameters:
  - `q` - Search query
  - `type` - Filter by entry type
  - `category` - Filter by category
  - `sort` - Sort order (relevance, newest, popular)
  - `page` - Page number
  - `limit` - Results per page

### User Interactions

- `POST /api/celebrate/comments` - Add a comment
- `GET /api/celebrate/entries/:id/comments` - Get comments for an entry
- `POST /api/celebrate/submissions` - Submit a new entry
- `GET /api/celebrate/submissions/pending` - Get pending submissions
- `POST /api/celebrate/submissions/:id/vote` - Vote for a submission

## Frontend Components

### Pages

1. **Main Page** (`/celebrate.html`)
   - Featured entries
   - Category navigation
   - Search functionality
   - Community voting

2. **Category Pages** (`/celebrate/[category].html`)
   - Category description
   - List of entries in the category
   - Filtering and sorting options

3. **Entry Detail Pages** (`/celebrate/[type]/[slug].html`)
   - Entry details and media
   - Related entries
   - Comments section
   - Like and share functionality

4. **Search Results Page** (`/celebrate/search.html`)
   - Search results with filtering
   - Pagination
   - Sort options

5. **Submission Page** (`/celebrate/suggest.html`)
   - Entry submission form
   - Guidelines for submission
   - Preview functionality

6. **Voting Page** (`/celebrate/voting.html`)
   - List of pending submissions
   - Voting interface
   - Submission details

### Components

1. **Entry Card**
   - Display entry summary
   - Image
   - Title and short description
   - Category badge
   - View details link

2. **Category Navigation**
   - Hierarchical category display
   - Category icons
   - Selection indicators

3. **Search Bar**
   - Search input
   - Search button
   - Advanced search toggle

4. **Comment Section**
   - Comment list
   - Comment form
   - Reply functionality
   - Like button for comments

5. **Submission Form**
   - Entry type selection
   - Form fields based on entry type
   - Media upload
   - Preview functionality

6. **Voting Component**
   - Submission display
   - Vote button
   - Vote count
   - Submission details

## Data Population Strategy

### Initial Data

1. **Categories**
   - Create main categories (People, Places, Events)
   - Create subcategories for each main category
   - Assign icons and descriptions

2. **Featured Entries**
   - Create at least 3 featured entries for each main category
   - Include high-quality images and comprehensive descriptions
   - Assign to appropriate categories

3. **Regular Entries**
   - Create at least 7 additional entries for each main category
   - Ensure diverse representation across Nigeria
   - Include essential metadata and media

### Data Import Process

1. Create SQL scripts for initial data import
2. Implement a Go-based importer for complex data relationships
3. Create a process for media asset management
4. Implement validation to ensure data quality

## Performance Considerations

1. **Database Indexing**
   - Add indexes for frequently queried fields
   - Implement full-text search indexing
   - Optimize join operations

2. **Caching Strategy**
   - Cache category hierarchy
   - Cache featured entries
   - Implement cache invalidation on updates

3. **Image Optimization**
   - Implement responsive images
   - Use image compression
   - Consider a CDN for media assets

4. **API Performance**
   - Implement pagination for list endpoints
   - Use efficient query patterns
   - Monitor and optimize slow queries

## Security Considerations

1. **Input Validation**
   - Validate all user inputs
   - Sanitize HTML content
   - Implement rate limiting for submissions

2. **Authentication and Authorization**
   - Require authentication for interactions
   - Implement proper authorization checks
   - Protect admin functionality

3. **Content Moderation**
   - Implement approval workflow for submissions
   - Add reporting functionality for inappropriate content
   - Create moderation tools for administrators

## Testing Strategy

1. **Unit Testing**
   - Test repository methods
   - Test service layer logic
   - Test validation functions

2. **Integration Testing**
   - Test API endpoints
   - Test database operations
   - Test caching mechanisms

3. **Frontend Testing**
   - Test responsive design
   - Test user interactions
   - Test form submissions

4. **Performance Testing**
   - Test API response times
   - Test page load performance
   - Test search performance with large datasets

## Implementation Phases

### Phase 1: Core Infrastructure
- Database schema implementation
- Basic API endpoints
- Frontend page structure

### Phase 2: Data Population
- Category hierarchy
- Featured entries
- Regular entries
- Media assets

### Phase 3: User Interactions
- Comments functionality
- Voting system
- Submission workflow

### Phase 4: Search and Discovery
- Search API implementation
- Search results page
- Filtering and sorting

### Phase 5: Optimization and Polish
- Performance optimization
- Mobile responsiveness
- UI refinements
- Documentation

## Conclusion

This technical specification provides a comprehensive guide for implementing the Celebrate Nigeria feature. By following this specification, developers can ensure that the implementation is consistent, performant, and meets all requirements.
