# Celebrate Nigeria Voting System Implementation Plan

## Overview

This document outlines the plan for implementing the voting system for the Celebrate Nigeria feature. The voting system will allow users to upvote or downvote entries, helping to surface the most valuable content and provide feedback on entries.

## Current Status

The codebase already includes:

1. **Database Schema**:
   - `entry_votes` table with fields for `celebration_entry_id`, `user_id`, `vote_type`, and `created_at`
   - `EntryVote` model defined in the models package

2. **Repository Layer**:
   - `VoteForSubmission` method that handles voting on submissions

3. **Service Layer**:
   - Corresponding service method for submission voting

4. **Handlers Layer**:
   - Route for voting on submissions

## Implementation Plan

### 1. Repository Layer Extensions

Extend the repository layer to support voting on regular entries:

```go
// VoteForEntry adds or updates a vote for an entry
func (r *CelebrationRepository) VoteForEntry(ctx context.Context, entryID, userID int64, voteType string) error {
    // Check if user already voted
    var existingVote struct {
        ID       int64
        VoteType string
    }
    
    err := r.db.GetContext(ctx, &existingVote, `
        SELECT id, vote_type FROM entry_votes
        WHERE celebration_entry_id = $1 AND user_id = $2
    `, entryID, userID)
    
    if err != nil && err != sql.ErrNoRows {
        return err
    }
    
    // If user already voted with the same vote type, return error
    if err == nil && existingVote.VoteType == voteType {
        return fmt.Errorf("user already voted for this entry with the same vote type")
    }
    
    // If user already voted with a different vote type, update the vote
    if err == nil {
        _, err = r.db.ExecContext(ctx, `
            UPDATE entry_votes
            SET vote_type = $1, created_at = NOW()
            WHERE id = $2
        `, voteType, existingVote.ID)
        return err
    }
    
    // Add new vote
    _, err = r.db.ExecContext(ctx, `
        INSERT INTO entry_votes (celebration_entry_id, user_id, vote_type, created_at)
        VALUES ($1, $2, $3, NOW())
    `, entryID, userID, voteType)
    
    return err
}

// GetEntryVoteCounts gets the upvote and downvote counts for an entry
func (r *CelebrationRepository) GetEntryVoteCounts(ctx context.Context, entryID int64) (upvotes int, downvotes int, err error) {
    query := `
        SELECT 
            COUNT(CASE WHEN vote_type = 'upvote' THEN 1 END) as upvotes,
            COUNT(CASE WHEN vote_type = 'downvote' THEN 1 END) as downvotes
        FROM entry_votes
        WHERE celebration_entry_id = $1
    `
    
    err = r.db.QueryRowContext(ctx, query, entryID).Scan(&upvotes, &downvotes)
    return
}

// GetUserVoteForEntry gets a user's vote for an entry
func (r *CelebrationRepository) GetUserVoteForEntry(ctx context.Context, entryID, userID int64) (string, error) {
    var voteType string
    
    err := r.db.GetContext(ctx, &voteType, `
        SELECT vote_type FROM entry_votes
        WHERE celebration_entry_id = $1 AND user_id = $2
    `, entryID, userID)
    
    if err == sql.ErrNoRows {
        return "", nil // No vote found
    }
    
    return voteType, err
}

// DeleteVoteForEntry removes a user's vote for an entry
func (r *CelebrationRepository) DeleteVoteForEntry(ctx context.Context, entryID, userID int64) error {
    _, err := r.db.ExecContext(ctx, `
        DELETE FROM entry_votes
        WHERE celebration_entry_id = $1 AND user_id = $2
    `, entryID, userID)
    
    return err
}
```

### 2. Service Layer Extensions

Extend the service layer to support voting on entries:

```go
// VoteForEntry adds or updates a vote for an entry
func (s *CelebrationService) VoteForEntry(ctx context.Context, entryID, userID int64, voteType string) error {
    // Validate
    if entryID == 0 {
        return ValidationError{Field: "entryID", Message: "Entry ID is required"}
    }
    if userID == 0 {
        return ValidationError{Field: "userID", Message: "User ID is required"}
    }
    if voteType != "upvote" && voteType != "downvote" {
        return ValidationError{Field: "voteType", Message: "Vote type must be 'upvote' or 'downvote'"}
    }
    
    // Check if entry exists
    entry, err := s.repo.GetEntryByID(ctx, entryID)
    if err != nil {
        if err == sql.ErrNoRows {
            return ValidationError{Field: "entryID", Message: "Entry not found"}
        }
        return err
    }
    
    // Only allow voting on published entries
    if entry.Status != "published" {
        return ValidationError{Field: "entryID", Message: "Cannot vote on unpublished entries"}
    }
    
    // Submit the vote
    return s.repo.VoteForEntry(ctx, entryID, userID, voteType)
}

// GetEntryVoteCounts gets the upvote and downvote counts for an entry
func (s *CelebrationService) GetEntryVoteCounts(ctx context.Context, entryID int64) (upvotes int, downvotes int, err error) {
    // Validate
    if entryID == 0 {
        return 0, 0, ValidationError{Field: "entryID", Message: "Entry ID is required"}
    }
    
    return s.repo.GetEntryVoteCounts(ctx, entryID)
}

// GetUserVoteForEntry gets a user's vote for an entry
func (s *CelebrationService) GetUserVoteForEntry(ctx context.Context, entryID, userID int64) (string, error) {
    // Validate
    if entryID == 0 {
        return "", ValidationError{Field: "entryID", Message: "Entry ID is required"}
    }
    if userID == 0 {
        return "", ValidationError{Field: "userID", Message: "User ID is required"}
    }
    
    return s.repo.GetUserVoteForEntry(ctx, entryID, userID)
}

// DeleteVoteForEntry removes a user's vote for an entry
func (s *CelebrationService) DeleteVoteForEntry(ctx context.Context, entryID, userID int64) error {
    // Validate
    if entryID == 0 {
        return ValidationError{Field: "entryID", Message: "Entry ID is required"}
    }
    if userID == 0 {
        return ValidationError{Field: "userID", Message: "User ID is required"}
    }
    
    return s.repo.DeleteVoteForEntry(ctx, entryID, userID)
}
```

### 3. Handler Layer Extensions

Add new endpoints for voting on entries:

```go
// Define input struct for vote requests
type voteInput struct {
    VoteType string `json:"vote_type" binding:"required,oneof=upvote downvote"`
}

// VoteForEntry handles POST /celebrate/entries/:id/vote
func (h *CelebrationHandlers) VoteForEntry(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
        return
    }
    
    var input voteInput
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get user ID from authentication middleware
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }
    
    if err := h.service.VoteForEntry(c.Request.Context(), id, userID.(int64), input.VoteType); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Vote recorded successfully"})
}

// DeleteVoteForEntry handles DELETE /celebrate/entries/:id/vote
func (h *CelebrationHandlers) DeleteVoteForEntry(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
        return
    }
    
    // Get user ID from authentication middleware
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
        return
    }
    
    if err := h.service.DeleteVoteForEntry(c.Request.Context(), id, userID.(int64)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Vote removed successfully"})
}

// GetEntryVotes handles GET /celebrate/entries/:id/votes
func (h *CelebrationHandlers) GetEntryVotes(c *gin.Context) {
    idStr := c.Param("id")
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
        return
    }
    
    upvotes, downvotes, err := h.service.GetEntryVoteCounts(c.Request.Context(), id)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    // Get user's vote if authenticated
    var userVote string
    if userID, exists := c.Get("user_id"); exists {
        userVote, err = h.service.GetUserVoteForEntry(c.Request.Context(), id, userID.(int64))
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }
    }
    
    c.JSON(http.StatusOK, gin.H{
        "upvotes": upvotes,
        "downvotes": downvotes,
        "total": upvotes - downvotes,
        "user_vote": userVote,
    })
}

// Register the new routes
func (h *CelebrationHandlers) RegisterRoutes(router *gin.RouterGroup) {
    celebrateGroup := router.Group("/celebrate")
    {
        // Existing routes...
        
        // Voting routes
        celebrateGroup.POST("/entries/:id/vote", h.VoteForEntry)
        celebrateGroup.DELETE("/entries/:id/vote", h.DeleteVoteForEntry)
        celebrateGroup.GET("/entries/:id/votes", h.GetEntryVotes)
    }
}
```

### 4. Frontend Integration

Add voting UI components to the frontend:

```html
<!-- Vote buttons component -->
<div class="vote-buttons">
    <button 
        class="vote-button upvote" 
        :class="{ active: userVote === 'upvote' }"
        @click="vote('upvote')">
        <i class="fa fa-arrow-up"></i>
    </button>
    <span class="vote-count">{{ voteTotal }}</span>
    <button 
        class="vote-button downvote" 
        :class="{ active: userVote === 'downvote' }"
        @click="vote('downvote')">
        <i class="fa fa-arrow-down"></i>
    </button>
</div>
```

```javascript
// Vote handling in JavaScript
const entryVoting = {
    data() {
        return {
            upvotes: 0,
            downvotes: 0,
            userVote: '',
            loading: false
        }
    },
    computed: {
        voteTotal() {
            return this.upvotes - this.downvotes;
        }
    },
    methods: {
        async loadVotes() {
            this.loading = true;
            try {
                const response = await fetch(`/api/celebrate/entries/${this.entryId}/votes`);
                const data = await response.json();
                this.upvotes = data.upvotes;
                this.downvotes = data.downvotes;
                this.userVote = data.user_vote;
            } catch (error) {
                console.error('Error loading votes:', error);
            } finally {
                this.loading = false;
            }
        },
        async vote(voteType) {
            if (!this.isAuthenticated) {
                // Redirect to login or show login modal
                this.showLoginPrompt();
                return;
            }
            
            this.loading = true;
            try {
                // If user already voted with this type, remove the vote
                if (this.userVote === voteType) {
                    await fetch(`/api/celebrate/entries/${this.entryId}/vote`, {
                        method: 'DELETE'
                    });
                    this.userVote = '';
                } else {
                    // Otherwise add or change the vote
                    await fetch(`/api/celebrate/entries/${this.entryId}/vote`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ vote_type: voteType })
                    });
                    this.userVote = voteType;
                }
                
                // Reload vote counts
                await this.loadVotes();
            } catch (error) {
                console.error('Error voting:', error);
            } finally {
                this.loading = false;
            }
        }
    },
    mounted() {
        this.loadVotes();
    }
};
```

### 5. Points System Integration (Optional)

Integrate with the points system to award points for receiving upvotes:

```go
// In the VoteForEntry handler
func (h *CelebrationHandlers) VoteForEntry(c *gin.Context) {
    // ... existing code ...
    
    // If this is an upvote and points integration is enabled, award points to the entry creator
    if h.pointsIntegration != nil && input.VoteType == "upvote" {
        // Get the entry to find out who created it
        entry, err := h.service.GetEntryByID(c.Request.Context(), id)
        if err == nil && entry != nil {
            // Award points to the entry creator for receiving an upvote
            h.pointsIntegration.AwardPointsForCelebrateUpvote(entry.UserID, id, 1)
        }
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "Vote recorded successfully"})
}
```

## Testing Plan

1. **Unit Tests**:
   - Test repository methods for voting
   - Test service methods for validation and business logic
   - Test handlers for proper response formatting

2. **Integration Tests**:
   - Test the complete voting flow from API to database
   - Test edge cases like changing votes and removing votes

3. **Manual Testing**:
   - Test the voting UI in different browsers
   - Test with different user accounts
   - Test error handling and edge cases

## Implementation Timeline

1. **Repository Layer**: 1 day
2. **Service Layer**: 1 day
3. **Handler Layer**: 1 day
4. **Frontend Integration**: 2 days
5. **Testing**: 1 day

Total estimated time: 6 days

## Conclusion

By extending the existing voting functionality, we can quickly implement a robust voting system for the Celebrate Nigeria feature. This will enhance user engagement and help surface the most valuable content. The implementation leverages existing patterns in the codebase and integrates with other systems like authentication and points.
