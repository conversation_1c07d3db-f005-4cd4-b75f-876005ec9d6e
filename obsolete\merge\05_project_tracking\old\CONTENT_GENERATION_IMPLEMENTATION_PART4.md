# Book 3 Content Generation Implementation Plan (Part 4)

## Database Integration and Processing

For effective content management and database integration:

```go
// Process all sections within a chapter
func processChapter(db *sql.DB, chapterNumber int, tocData *NewTocData) {
    fmt.Printf("Processing Chapter %d\n", chapterNumber)
    
    // Convert int to string for map lookup
    chapterStr := strconv.Itoa(chapterNumber)
    
    // Check if chapter exists in TOC data
    chapterData, exists := (*tocData)[chapterStr]
    if !exists {
        fmt.Printf("Chapter %d not found in TOC data\n", chapterNumber)
        return
    }
    
    // Get the chapter ID from the database
    var chapterID int
    err := db.QueryRow(`
        SELECT id FROM book_chapters 
        WHERE book_id = 3 AND number = $1
    `, chapterNumber).Scan(&chapterID)
    
    if err != nil {
        fmt.Printf("Error finding chapter %d: %v\n", chapterNumber, err)
        return
    }
    
    fmt.Printf("Found chapter with ID %d\n", chapterID)
    
    // Process each section in the chapter (up to 20 sections)
    sectionsProcessed := 0
    for sectionStr, sectionData := range chapterData {
        sectionNumber, _ := strconv.Atoi(sectionStr)
        
        // Update this section
        updateSection(db, chapterID, chapterNumber, sectionNumber, sectionData)
        
        // Process all subsections for this section
        processSubsections(db, chapterNumber, sectionNumber, sectionData)
        
        sectionsProcessed++
    }
    
    fmt.Printf("Processed %d sections in Chapter %d\n", sectionsProcessed, chapterNumber)
}

// Update a single section with enhanced content
func updateSection(db *sql.DB, chapterID, chapterNumber, sectionNumber int, sectionData SectionData) {
    fmt.Printf("Updating Chapter %d, Section %d: %s\n", chapterNumber, sectionNumber, sectionData.Title)
    
    // Generate enhanced content for this section
    enhancedContent := generateEnhancedSectionContent(chapterNumber, sectionNumber, sectionData.Title)
    
    // Check if section exists in database
    var sectionID int
    var exists bool
    
    err := db.QueryRow(`
        SELECT id FROM book_sections 
        WHERE chapter_id = $1 AND number = $2
    `, chapterID, sectionNumber).Scan(&sectionID)
    
    if err == nil {
        // Section exists, update it
        exists = true
        
        _, err = db.Exec(`
            UPDATE book_sections 
            SET title = $1, content = $2, updated_at = NOW() 
            WHERE id = $3
        `, sectionData.Title, enhancedContent, sectionID)
        
        if err != nil {
            fmt.Printf("Error updating section %d: %v\n", sectionNumber, err)
            return
        }
        
        fmt.Printf("Updated section with ID %d\n", sectionID)
    } else {
        // Section doesn't exist, create it
        exists = false
        
        err = db.QueryRow(`
            INSERT INTO book_sections 
            (chapter_id, number, title, content, created_at, updated_at) 
            VALUES ($1, $2, $3, $4, NOW(), NOW())
            RETURNING id
        `, chapterID, sectionNumber, sectionData.Title, enhancedContent).Scan(&sectionID)
        
        if err != nil {
            fmt.Printf("Error creating section %d: %v\n", sectionNumber, err)
            return
        }
        
        fmt.Printf("Created new section with ID %d\n", sectionID)
    }
    
    // Log the action
    action := "updated"
    if !exists {
        action = "created"
    }
    
    fmt.Printf("Successfully %s Chapter %d, Section %d: %s\n", 
        action, chapterNumber, sectionNumber, sectionData.Title)
}

// Process all subsections for a section
func processSubsections(db *sql.DB, chapterNumber, sectionNumber int, sectionData SectionData) {
    fmt.Printf("Processing subsections for Chapter %d, Section %d\n", chapterNumber, sectionNumber)
    
    // Get section ID from database
    var sectionID int
    err := db.QueryRow(`
        SELECT s.id FROM book_sections s
        JOIN book_chapters c ON s.chapter_id = c.id
        WHERE c.book_id = 3 AND c.number = $1 AND s.number = $2
    `, chapterNumber, sectionNumber).Scan(&sectionID)
    
    if err != nil {
        fmt.Printf("Error finding section ID for Chapter %d, Section %d: %v\n", 
            chapterNumber, sectionNumber, err)
        return
    }
    
    // Process each subsection
    subsectionsProcessed := 0
    for subsectionNumber, subsectionTitle := range sectionData.Subsections {
        // Generate enhanced content for this subsection
        enhancedContent := generateEnhancedSubsectionContent(
            chapterNumber, sectionNumber, subsectionNumber, 
            subsectionTitle, sectionData.Title)
        
        // Check if subsection exists
        var subsectionID int
        var exists bool
        
        err := db.QueryRow(`
            SELECT id FROM book_subsections 
            WHERE section_id = $1 AND number = $2
        `, sectionID, subsectionNumber).Scan(&subsectionID)
        
        if err == nil {
            // Subsection exists, update it
            exists = true
            
            _, err = db.Exec(`
                UPDATE book_subsections 
                SET title = $1, content = $2, updated_at = NOW() 
                WHERE id = $3
            `, subsectionTitle, enhancedContent, subsectionID)
            
            if err != nil {
                fmt.Printf("Error updating subsection %d: %v\n", subsectionNumber, err)
                continue
            }
        } else {
            // Subsection doesn't exist, create it
            exists = false
            
            err = db.QueryRow(`
                INSERT INTO book_subsections 
                (section_id, number, title, content, created_at, updated_at) 
                VALUES ($1, $2, $3, $4, NOW(), NOW())
                RETURNING id
            `, sectionID, subsectionNumber, subsectionTitle, enhancedContent).Scan(&subsectionID)
            
            if err != nil {
                fmt.Printf("Error creating subsection %d: %v\n", subsectionNumber, err)
                continue
            }
        }
        
        // Log the action
        action := "updated"
        if !exists {
            action = "created"
        }
        
        fmt.Printf("Successfully %s subsection %d: %s\n", 
            action, subsectionNumber, subsectionTitle)
        
        subsectionsProcessed++
    }
    
    fmt.Printf("Processed %d subsections for Chapter %d, Section %d\n", 
        subsectionsProcessed, chapterNumber, sectionNumber)
}
```

## Special Handling for Book 3 Epilogue

Book 3 has special templates to handle its unique structure, particularly the integration of the Epilogue within the Appendices section.

```go
// Special handling for Epilogue in Book 3
func processBook3Epilogue(db *sql.DB, ct *CitationTracker) error {
    // Epilogue is treated as part of the Appendices section
    const epilogueTemplate = `
# Epilogue: A Letter to Nigeria from Samuel Chimezie Okechukwu

## Poem: "Nigeria, I See Your Greatness"

{{.PoemContent}}

{{range .Sections}}
## {{.Title}}

{{.Content}}

{{if .Citations}}
### References
{{range .Citations}}
[{{.RefNumber}}] {{.Author}} ({{.Year}}). *{{.Title}}*. {{.Source}}.
{{end}}
{{end}}
{{end}}
`
    
    // Implementation details...
    
    return nil
}
```

In Book 3, the Epilogue is treated as part of the Appendices section rather than as a separate section. The structure is:

```
# Appendices

## Epilogue: A Letter to Nigeria from Samuel Chimezie Okechukwu
   - Poem: "Nigeria, I see your Greatness"
   - E.1 Personal Reflections on the Journey of Writing and Activism
   - E.2 Unwavering Faith in the Resilience and Potential of Nigerians
   - E.3 A Vision of Hope Bequeathed to Future Generations
   - E.4 Invitation to Continue the Dialogue and Action at GreatNigeria.net
   - E.5 A Final Prayer and Blessing for Nigeria's Collective Triumph

## Appendix A: Detailed Visual & Textual Timeline...
## Appendix B: Nigeria By Numbers...
   [Additional appendices continue...]
```

This structure ensures that the Epilogue content is properly included in the TOC rendering while maintaining its special status within the Appendices section.

## Integration with Real-time Communication (WebSockets)

For real-time updates and notifications during the content generation process:

```go
// WebSocket notification for content updates
func notifyContentUpdate(chapterNumber, sectionNumber int, title string) {
    message := map[string]interface{}{
        "type": "content_update",
        "data": map[string]interface{}{
            "book_id": 3,
            "chapter_number": chapterNumber,
            "section_number": sectionNumber,
            "title": title,
            "timestamp": time.Now().Format(time.RFC3339),
        },
    }
    
    // Convert to JSON
    jsonMessage, err := json.Marshal(message)
    if err != nil {
        fmt.Printf("Error creating notification: %v\n", err)
        return
    }
    
    // Send to WebSocket hub
    hub.Broadcast(jsonMessage)
}
```

## Conclusion

This implementation plan provides a comprehensive approach to generating content for Book 3 following the depth-first methodology. The plan ensures that:

1. All content follows the required structural template with 9 mandatory components
2. Content meets the specified word count requirements for each element
3. Specialized elements (VOICES FROM THE FIELD, REFLECTION POINTS, etc.) are properly integrated
4. Nigerian context is maintained throughout all content
5. Database integration is handled efficiently with proper error handling
6. Special cases like the Epilogue are handled appropriately

The modular design of the content generator allows for targeted generation of specific chapters or sections, making it easier to prioritize content creation and manage the overall process. The integration with the database ensures that all generated content is properly stored and accessible through the platform's interface.

By following this implementation plan, we can efficiently generate high-quality, comprehensive content for Book 3 that meets all the specified requirements while maintaining consistency with the overall platform design.
