# CORS Configuration for React Frontend Integration

This document outlines the Cross-Origin Resource Sharing (CORS) configuration needed for the Go backend to work with the React frontend.

## What is CORS?

Cross-Origin Resource Sharing (CORS) is a security feature implemented by browsers that restricts web pages from making requests to a different domain than the one that served the original page. This is a security measure to prevent malicious websites from making unauthorized requests to other websites on behalf of the user.

When the React frontend (running on one domain) makes API requests to the Go backend (running on another domain), the browser will block these requests unless the backend explicitly allows them through CORS headers.

## Required CORS Configuration for Go Backend

The Go backend needs to be configured to allow requests from the React frontend. This is done by adding CORS middleware to the Go server.

### Using Gin CORS Middleware

If you're using the Gin framework (which appears to be the case based on the codebase), you can use the `github.com/gin-contrib/cors` package to add CORS middleware:

```go
package main

import (
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	router := gin.Default()

	// CORS configuration
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "https://your-production-frontend-domain.com"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// ... rest of your code
}
```

### Implementation in the Great Nigeria Library Project

To implement CORS in the Great Nigeria Library project, you need to modify the `cmd/api-gateway/main.go` file to add the CORS middleware:

1. Install the CORS middleware package:
   ```bash
   go get github.com/gin-contrib/cors
   ```

2. Import the package in `cmd/api-gateway/main.go`:
   ```go
   import (
       // ... other imports
       "github.com/gin-contrib/cors"
   )
   ```

3. Add the CORS middleware to the router:
   ```go
   // Add CORS middleware
   router.Use(cors.New(cors.Config{
       AllowOrigins:     []string{"http://localhost:3000", "https://your-production-frontend-domain.com"},
       AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
       AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
       ExposeHeaders:    []string{"Content-Length"},
       AllowCredentials: true,
       MaxAge:           12 * time.Hour,
   }))
   ```

   Add this code before registering any routes.

## Environment-Specific Configuration

It's a good practice to make the CORS configuration environment-specific:

```go
// CORS configuration
var allowedOrigins []string
if os.Getenv("ENV") == "production" {
    allowedOrigins = []string{"https://your-production-frontend-domain.com"}
} else {
    allowedOrigins = []string{"http://localhost:3000"}
}

router.Use(cors.New(cors.Config{
    AllowOrigins:     allowedOrigins,
    AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
    AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
    ExposeHeaders:    []string{"Content-Length"},
    AllowCredentials: true,
    MaxAge:           12 * time.Hour,
}))
```

## Testing CORS Configuration

To test if your CORS configuration is working correctly:

1. Start the Go backend server
2. Start the React frontend development server (typically on port 3000)
3. Make an API request from the React frontend to the Go backend
4. Check the browser's developer console for any CORS-related errors

If there are no CORS errors, your configuration is working correctly.

## Common CORS Issues

1. **Missing Headers**: If you're seeing CORS errors, make sure all required headers are included in the `AllowHeaders` list.

2. **Wrong Origin**: Make sure the origin of your React frontend is included in the `AllowOrigins` list.

3. **Credentials**: If your API requires cookies or authentication headers, make sure `AllowCredentials` is set to `true`.

4. **Preflight Requests**: For non-simple requests (like those with custom headers or using methods other than GET/POST), browsers send a preflight OPTIONS request. Make sure your server handles OPTIONS requests correctly.

## Security Considerations

While CORS is necessary for the React frontend to communicate with the Go backend, it's important to be as restrictive as possible:

1. Only allow origins that you control
2. Only allow the HTTP methods that your API needs
3. Only allow the headers that your API uses
4. Set a reasonable max age for preflight requests

Avoid using `AllowAllOrigins: true` in production as it allows any website to make requests to your API.
