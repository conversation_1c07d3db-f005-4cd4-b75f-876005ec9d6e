# React Frontend Implementation Plan

This document provides a comprehensive plan for implementing the React TypeScript frontend for the Great Nigeria Library project as a separate repository.

## Repository Setup

Create a new repository for the React frontend:

```bash
# Create a new repository
git init great-nigeria-frontend
cd great-nigeria-frontend

# Initialize with React TypeScript
npx create-react-app . --template typescript

# Install core dependencies
npm install react-router-dom @reduxjs/toolkit react-redux axios styled-components
```

## Project Structure

Organize the project with the following structure:

```
src/
├── api/              # API client and services
├── assets/           # Static assets
├── components/       # Reusable UI components
├── features/         # Feature-specific components
│   ├── auth/         # Authentication
│   ├── books/        # Book viewer
│   ├── celebrate/    # Celebrate Nigeria
│   ├── forum/        # Forum
│   ├── profile/      # User profile
│   └── resources/    # Resources
├── hooks/            # Custom React hooks
├── layouts/          # Page layouts
├── pages/            # Page components
├── store/            # Redux store
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

## API Integration

### API Client Setup

```typescript
// src/api/client.ts
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
```

### API Services

Create service modules for different API endpoints:

```typescript
// src/api/bookService.ts
import apiClient from './client';
import { Book, Chapter, Section } from '../types';

const BookService = {
  getBooks: async (): Promise<Book[]> => {
    const response = await apiClient.get('/books');
    return response.data;
  },
  
  getBookById: async (id: string): Promise<Book> => {
    const response = await apiClient.get(`/books/${id}`);
    return response.data;
  },
  
  getBookChapters: async (bookId: string): Promise<Chapter[]> => {
    const response = await apiClient.get(`/books/${bookId}/chapters`);
    return response.data;
  },
  
  getChapterById: async (chapterId: string): Promise<Chapter> => {
    const response = await apiClient.get(`/books/chapters/${chapterId}`);
    return response.data;
  },
  
  getSectionById: async (sectionId: string): Promise<Section> => {
    const response = await apiClient.get(`/books/sections/${sectionId}`);
    return response.data;
  },
  
  saveReadingProgress: async (bookId: string, sectionId: string): Promise<void> => {
    await apiClient.post(`/books/${bookId}/progress`, { sectionId });
  },
};

export default BookService;
```

## State Management

### Store Configuration

```typescript
// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../features/auth/authSlice';
import bookReducer from '../features/books/bookSlice';
import forumReducer from '../features/forum/forumSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    books: bookReducer,
    forum: forumReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### Authentication Slice

```typescript
// src/features/auth/authSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import authService from '../../api/authService';

export const login = createAsyncThunk(
  'auth/login',
  async (credentials: { email: string; password: string }, thunkAPI) => {
    try {
      return await authService.login(credentials);
    } catch (error) {
      return thunkAPI.rejectWithValue('Login failed');
    }
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async (userData: { name: string; email: string; password: string }, thunkAPI) => {
    try {
      return await authService.register(userData);
    } catch (error) {
      return thunkAPI.rejectWithValue('Registration failed');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState: {
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  },
  reducers: {
    logout: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      localStorage.removeItem('token');
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        localStorage.setItem('token', action.payload.token);
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    // Similar cases for register
  },
});

export const { logout } = authSlice.actions;
export default authSlice.reducer;
```

## Routing

```typescript
// src/App.tsx
import React, { useEffect } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import MainLayout from './layouts/MainLayout';
import HomePage from './pages/HomePage';
import BookViewer from './pages/BookViewer';
import UserProfile from './pages/UserProfile';
import Forum from './pages/Forum';
import ForumTopic from './pages/ForumTopic';
import Resources from './pages/Resources';
import Login from './pages/Login';
import Register from './pages/Register';
import ProtectedRoute from './components/ProtectedRoute';
import { checkAuthStatus } from './features/auth/authSlice';

const App: React.FC = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(checkAuthStatus());
  }, [dispatch]);

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<MainLayout />}>
          <Route index element={<HomePage />} />
          <Route path="book-viewer" element={<BookViewer />} />
          <Route path="forum" element={<Forum />} />
          <Route path="forum/topic/:id" element={<ForumTopic />} />
          <Route path="resources" element={<Resources />} />
          <Route path="login" element={<Login />} />
          <Route path="register" element={<Register />} />
          
          {/* Protected routes */}
          <Route path="profile" element={
            <ProtectedRoute>
              <UserProfile />
            </ProtectedRoute>
          } />
        </Route>
      </Routes>
    </BrowserRouter>
  );
};

export default App;
```

## Component Implementation Examples

### Main Layout

```typescript
// src/layouts/MainLayout.tsx
import React from 'react';
import { Outlet } from 'react-router-dom';
import Header from '../components/Header';
import Footer from '../components/Footer';
import styled from 'styled-components';

const MainContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
`;

const Content = styled.main`
  flex: 1;
`;

const MainLayout: React.FC = () => {
  return (
    <MainContainer>
      <Header />
      <Content>
        <Outlet />
      </Content>
      <Footer />
    </MainContainer>
  );
};

export default MainLayout;
```

### Book Viewer Page

```typescript
// src/pages/BookViewer.tsx
import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import BookService from '../api/bookService';
import BookNavigation from '../features/books/BookNavigation';
import BookContent from '../features/books/BookContent';
import BookProgress from '../features/books/BookProgress';
import styled from 'styled-components';

const BookViewerContainer = styled.div`
  display: flex;
  min-height: calc(100vh - 200px);
  background-color: #f8f8f8;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 2rem auto;
  max-width: 1200px;
`;

const BookViewer: React.FC = () => {
  const [searchParams] = useSearchParams();
  const bookId = searchParams.get('book') || '1';
  const [book, setBook] = useState<any>(null);
  const [chapters, setChapters] = useState<any[]>([]);
  const [currentChapter, setCurrentChapter] = useState<any>(null);
  const [currentSection, setCurrentSection] = useState<any>(null);
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    const loadBook = async () => {
      try {
        const bookData = await BookService.getBookById(bookId);
        setBook(bookData);
        
        const chaptersData = await BookService.getBookChapters(bookId);
        setChapters(chaptersData);
        
        if (chaptersData.length > 0) {
          setCurrentChapter(chaptersData[0]);
        }
      } catch (error) {
        console.error('Error loading book:', error);
      }
    };
    
    loadBook();
  }, [bookId]);

  const handleChapterSelect = async (chapterId: string) => {
    try {
      const chapterData = await BookService.getChapterById(chapterId);
      setCurrentChapter(chapterData);
      
      if (chapterData.sections && chapterData.sections.length > 0) {
        setCurrentSection(chapterData.sections[0]);
      }
    } catch (error) {
      console.error('Error loading chapter:', error);
    }
  };

  const handleSectionSelect = async (sectionId: string) => {
    try {
      const sectionData = await BookService.getSectionById(sectionId);
      setCurrentSection(sectionData);
      
      // Save reading progress if authenticated
      if (isAuthenticated) {
        BookService.saveReadingProgress(bookId, sectionId);
      }
    } catch (error) {
      console.error('Error loading section:', error);
    }
  };

  return (
    <BookViewerContainer>
      <BookNavigation 
        book={book}
        chapters={chapters}
        currentChapter={currentChapter}
        onChapterSelect={handleChapterSelect}
        onSectionSelect={handleSectionSelect}
      />
      
      <div className="book-content">
        {currentSection ? (
          <>
            <BookContent section={currentSection} />
            {isAuthenticated && <BookProgress bookId={bookId} sectionId={currentSection.id} />}
          </>
        ) : (
          <div className="select-section-prompt">
            Select a chapter or section to begin reading
          </div>
        )}
      </div>
    </BookViewerContainer>
  );
};

export default BookViewer;
```

## Deployment Configuration

### Environment Variables

Create a `.env` file for development:

```
REACT_APP_API_BASE_URL=http://localhost:5000/api
```

Create a `.env.production` file for production:

```
REACT_APP_API_BASE_URL=https://api.greatnigeria.com/api
```

### Build Configuration

Update `package.json` with build scripts:

```json
{
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "build:prod": "env-cmd -f .env.production react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  }
}
```

## Integration with Go Backend

The React frontend will communicate with the Go backend through API calls. The current API endpoints should work with minimal changes, as they're already designed to return JSON data.

To ensure proper integration:

1. Configure CORS on the Go backend to allow requests from the React frontend
2. Ensure all API endpoints return properly formatted JSON responses
3. Implement consistent error handling across all endpoints
4. Document all API endpoints for frontend developers

## Implementation Timeline

1. **Week 1**: Setup and infrastructure, authentication system
2. **Week 2**: Book viewer and reading pages
3. **Week 3**: User profile and forum pages
4. **Week 4**: Resources pages and testing

## Next Steps

1. Create the React TypeScript project in a separate repository
2. Set up the project structure and core dependencies
3. Implement authentication and routing
4. Begin implementing the priority pages
