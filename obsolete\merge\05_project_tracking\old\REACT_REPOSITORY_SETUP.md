# React Repository Setup Guide

This document provides step-by-step instructions for setting up the React TypeScript frontend repository for the Great Nigeria Library project.

## Prerequisites

- Node.js 16+ and npm
- Git
- Code editor (VS Code recommended)

## Repository Setup

### 1. Create a New Repository

```bash
# Create a new directory for the repository
mkdir great-nigeria-frontend

# Navigate to the directory
cd great-nigeria-frontend

# Initialize Git repository
git init

# Create a .gitignore file
echo "node_modules
build
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.DS_Store" > .gitignore
```

### 2. Initialize React TypeScript Project

```bash
# Initialize a new React TypeScript project
npx create-react-app . --template typescript

# Install dependencies
npm install react-router-dom @reduxjs/toolkit react-redux axios styled-components
```

### 3. Create Project Structure

```bash
# Create directory structure
mkdir -p src/{api,assets,components,features/{auth,books,celebrate,forum,profile,resources},hooks,layouts,pages,store,types,utils}
```

### 4. Copy Implementation Files

Copy the implementation files from the `frontend-implementation` directory to the new repository:

1. Copy `README.md` to the root directory
2. Copy all files from `frontend-implementation/src` to the `src` directory

### 5. Configure Environment Variables

Create a `.env` file in the root directory:

```
REACT_APP_API_BASE_URL=http://localhost:5000/api
```

For production, create a `.env.production` file:

```
REACT_APP_API_BASE_URL=https://api.greatnigeria.com/api
```

### 6. Update package.json

Update the `package.json` file to include the following scripts:

```json
{
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "build:prod": "env-cmd -f .env.production react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject",
    "lint": "eslint src --ext .ts,.tsx",
    "format": "prettier --write \"src/**/*.{ts,tsx}\""
  }
}
```

### 7. Configure ESLint and Prettier

Install ESLint and Prettier:

```bash
npm install --save-dev eslint prettier eslint-config-prettier eslint-plugin-prettier
```

Create an `.eslintrc.js` file:

```javascript
module.exports = {
  parser: '@typescript-eslint/parser',
  extends: [
    'react-app',
    'react-app/jest',
    'plugin:prettier/recommended',
  ],
  plugins: ['prettier'],
  rules: {
    'prettier/prettier': 'error',
    'no-console': 'warn',
  },
};
```

Create a `.prettierrc` file:

```json
{
  "singleQuote": true,
  "trailingComma": "es5",
  "printWidth": 100,
  "tabWidth": 2,
  "semi": true
}
```

## Running the Application

### Development Mode

```bash
# Start the development server
npm start
```

The application will be available at http://localhost:3000.

### Production Build

```bash
# Create a production build
npm run build:prod
```

The build will be available in the `build` directory.

## Configuring CORS on the Go Backend

To allow the React frontend to communicate with the Go backend, you need to configure CORS on the backend. See the [CORS_CONFIGURATION.md](CORS_CONFIGURATION.md) document for detailed instructions.

## Implementing Remaining Pages

After setting up the repository, you should implement the remaining pages:

1. **Profile Page**: User profile, reading statistics, bookmarks
2. **Forum Pages**: Forum categories, topics, replies
3. **Resources Pages**: Resource categories, resources
4. **Celebrate Nigeria Pages**: Featured entries, entry details, search

See the [REACT_IMPLEMENTATION_TASKS.md](REACT_IMPLEMENTATION_TASKS.md) document for a detailed task list.

## Testing

### Unit Tests

Create unit tests for components, Redux slices, and utility functions:

```bash
# Run tests
npm test
```

### Integration Tests

Create integration tests for component interactions, routing, and authentication flow.

### End-to-End Tests

Set up Cypress for end-to-end testing:

```bash
# Install Cypress
npm install --save-dev cypress

# Add Cypress script to package.json
# "cypress:open": "cypress open"

# Run Cypress
npm run cypress:open
```

## Deployment

### Build Configuration

Configure the production build:

```bash
# Install env-cmd for environment variables
npm install --save-dev env-cmd

# Create a production build
npm run build:prod
```

### Static File Serving

The production build can be served from any static file server:

- Nginx
- Apache
- AWS S3 + CloudFront
- Netlify
- Vercel

### CI/CD Pipeline

Set up a CI/CD pipeline using GitHub Actions:

1. Create a `.github/workflows/main.yml` file
2. Configure the workflow to build and deploy the application

## Conclusion

Following these steps will set up a complete React TypeScript frontend repository for the Great Nigeria Library project. The repository will be ready for development, testing, and deployment.
