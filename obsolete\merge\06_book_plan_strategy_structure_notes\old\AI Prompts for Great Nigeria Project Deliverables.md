# AI Prompts for Great Nigeria Project Deliverables

## 1. Unified Website Documentation

### Prompt 1: Website Feature Analysis
```
Analyze all the Great Nigeria website documentation and code files to create a comprehensive inventory of all implemented features. For each feature:
1. Describe its purpose and functionality
2. Identify its implementation status (complete, partial, or planned)
3. List the relevant code files and documentation
4. Note any dependencies or integration points with other features

Focus on organizing features into logical categories such as:
- Core Platform Infrastructure
- User Management
- Content Management
- Community and Discussion
- Points and Rewards
- Marketplace and Economic Features
- Educational Tools
- Administrative Functions

Include code snippets where helpful to illustrate implementation details.
```

### Prompt 2: Website Architecture Documentation
```
Create a comprehensive technical architecture document for the Great Nigeria website platform based on the codebase analysis. Include:

1. High-level architecture overview with diagrams
2. Detailed microservices breakdown
   - Service boundaries and responsibilities
   - Communication patterns between services
   - Database schema and relationships
   - API endpoints and integration points
3. Frontend architecture
   - Component structure
   - State management
   - Routing and navigation
4. Scalability considerations
   - Database sharding strategy
   - Caching mechanisms
   - Load balancing approach
5. Security architecture
   - Authentication and authorization
   - Data protection measures
   - API security

The document should be technical but accessible, with clear explanations of design decisions and their rationales.
```

### Prompt 3: Pending Features Implementation Plan
```
Based on the analysis of the Great Nigeria website codebase and documentation, create a detailed implementation plan for all pending features. For each feature:

1. Provide a clear functional specification
2. Outline technical requirements and dependencies
3. Suggest implementation approach with code examples
4. Estimate complexity and effort required
5. Identify potential challenges and mitigation strategies
6. Recommend priority level (high, medium, low)

Organize features by functional area and implementation phase. Include specific recommendations for enhancing existing features based on best practices and user experience considerations.
```

### Prompt 4: Website Enhancement Recommendations
```
Based on the analysis of the Great Nigeria website platform, provide strategic recommendations for enhancing the platform beyond the currently planned features. Consider:

1. User engagement optimization
2. Performance improvements
3. Accessibility enhancements
4. Mobile experience optimization
5. Analytics and measurement capabilities
6. Integration with external platforms and services
7. Content personalization opportunities
8. Community growth strategies

For each recommendation, provide:
- Clear rationale based on platform goals
- Implementation approach with technical considerations
- Expected impact on user experience and platform adoption
- Relative priority and effort estimation
```

### Prompt 5: Unified Website Documentation Compilation
```
Compile a comprehensive, unified documentation for the Great Nigeria website platform that integrates all previous analyses. The documentation should:

1. Begin with an executive summary of the platform's purpose, status, and key features
2. Include a complete feature inventory with implementation status
3. Provide detailed technical architecture documentation
4. Present a prioritized implementation plan for pending features
5. Offer strategic enhancement recommendations
6. Include deployment and maintenance guidelines

The documentation should be well-structured with clear sections, tables of contents, and cross-references. Use diagrams, tables, and code examples where appropriate to enhance clarity.
```

## 2. Book 1 Manuscript

### Prompt 1: Book 1 Research and Content Planning
```
Conduct comprehensive research for Book 1: "Great Nigeria – Awakening the Giant: A Call to Urgent United Citizen Action" following these guidelines:

1. Research Nigerian news sources (Punch, Vanguard, Guardian Nigeria, ThisDay, Daily Trust, Premium Times. Arise TV news) for contemporary examples of:
   - Resource abundance vs. human development paradoxes
   - Governance failures and their impacts
   - Citizen complicity in systemic problems
   - Emerging resistance and hope (digital activism, community initiatives, youth movements)

2. Identify and document Nigerian YouTube channels and social media accounts with relevant content on:
   - Citizen experiences of systemic failures
   - Community-based solutions
   - Youth-led change initiatives
   - Cross-cultural/ethnic collaboration examples

3. Collect citizen perspectives that illustrate:
   - Personal encounters with everyday corruption
   - Experiences of civic disengagement
   - Moments of awakening to citizen responsibility
   - Successful local action stories

All research must be properly attributed with verifiable sources. For generated content representing citizen perspectives, clearly mark as such with fictional attributions (not using known public figures).

Organize research by chapter and section according to the Book 1 TOC, ensuring balanced representation across regions, ethnic groups, and perspectives.
```

### Prompt 2: Book 1 Manuscript Draft - 
```
Draft the first half of Book 1: "Great Nigeria – Awakening the Giant: A Call to Urgent United Citizen Action" including:



Follow these guidelines:
- Use emotionally resonant, provocative language that validates frustrations while channeling them toward constructive action
- Include all forum topics and actionable steps as specified in the TOC
- Incorporate properly attributed research from Nigerian news sources, social media, and citizen perspectives
- Ensure balanced representation across regions and ethnic groups
- Use the term "Citizen Engagement" or "Citizen Action" instead of "Revolution"
- Include section and subsection numbering throughout
- Maintain a documentary research style with proper citations

The manuscript should be comprehensive, engaging, and action-oriented, 
```

### Prompt 3: Book 1 Manuscript Draft - 

Follow these guidelines:
- Use emotionally resonant, provocative language that inspires hope and action
- Include all forum topics and actionable steps as specified in the TOC
- Incorporate properly attributed research from Nigerian news sources, social media, and citizen perspectives
- Ensure balanced representation across regions and ethnic groups
- Use the term "Citizen Engagement" or "Citizen Action" instead of "Revolution"
- Include section and subsection numbering throughout
- Maintain a documentary research style with proper citations

The manuscript should be comprehensive, engaging, and action-oriented, with approximately 30,000 words for this portion.
```

### Prompt 4: Book 1 Cover Image Generation
```
Generate a cover image for Book 1: "Great Nigeria – Awakening the Giant: A Call to Urgent United Citizen Action" with the following specifications:

1. Title: "GREAT NIGERIA" (prominent)
2. Subtitle: "Awakening the Giant: A Call to Urgent United Citizen Action" (smaller)
3. Author: "Samuel Chimezie Okechukwu" (at bottom)

Visual elements should include:
- A stylized map or outline of Nigeria
- Imagery suggesting awakening or rising (e.g., sunrise, people standing up)
- Green and white color scheme (Nigerian colors) with accent colors for emphasis
- Modern, bold typography
- Clean, professional layout suitable for both print and digital formats

The design should convey:
- Hope and possibility
- Urgency and importance
- Unity and collective action
- Professional credibility

The image should be high resolution (at least 300 dpi) and in portrait orientation with dimensions suitable for a book cover.
```

### Prompt 5: Book 1 Final PDF Compilation
```
Compile the complete Book 1: "Great Nigeria – Awakening the Giant: A Call to Urgent United Citizen Action" into a professionally formatted PDF manuscript with the following specifications:

1. Include the generated cover image as the first page
2. Format all content according to professional publishing standards:
   - Consistent typography with appropriate heading hierarchy
   - Page numbers
   - Running headers/footers
   - Proper paragraph spacing and indentation
   - Balanced page layout
   - Appropriate placement of forum topics and actionable steps
   - Properly formatted citations and bibliography

3. Include all sections from the TOC:
   - Front Matter (title page through introduction)
   - Chapters Content
   - Back Matter (appendices through about the initiative)

4. Ensure all cross-references are functional
5. Verify all citations are complete and properly formatted
6. Check that all section and subsection numbering is consistent

The final PDF should be publication-ready with professional appearance and complete content as specified in the Book 1 TOC.
```

## 3. Book 2 Manuscript

### Prompt 1: Book 2 Research and Content Planning
```
Conduct comprehensive research for Book 2: "Great Nigeria – The Masterplan for Empowered Decentralized Action" following these guidelines:

1. Research Nigerian news sources (Punch, Vanguard, Guardian Nigeria, ThisDay, Daily Trust, Premium Times, Arise TV News) for contemporary examples of:
   - Governance reform initiatives and their outcomes
   - Economic transformation strategies and implementation
   - Community organization and coalition building
   - Accountability systems and their effectiveness
   - Alternative service delivery and infrastructure solutions
   - Policy engagement and advocacy campaigns

2. Identify and document Nigerian YouTube channels and social media accounts with relevant content on:
   - Strategic action planning
   - Community organizing methodologies
   - Accountability and transparency initiatives
   - Alternative development approaches
   - Policy advocacy techniques

3. Research international examples of successful:
   - Governance reforms in comparable contexts
   - Economic diversification strategies
   - Community-based service delivery models
   - Accountability frameworks
   - Policy influence by citizen groups

All research must be properly attributed with verifiable sources. For generated content representing strategic approaches, clearly mark as such with appropriate attribution.

Organize research by chapter and section according to the Book 2 TOC, ensuring practical, actionable content with specific implementation guidance.
```

### Prompt 2: Book 2 Manuscript Draft - 
```
Draft the first part of Book 2: "Great Nigeria – The Masterplan for Empowered Decentralized Action" including:

\

Follow these guidelines:
- Use clear, instructional language that balances inspiration with practical guidance
- Include all forum topics and actionable steps as specified in the TOC
- Incorporate properly attributed research from Nigerian and international sources
- Ensure content is actionable with specific implementation guidance
- Use the term "Citizen Engagement" or "Citizen Action" instead of "Revolution"
- Include section and subsection numbering throughout
- Maintain a documentary research style with proper citations

The manuscript should be comprehensive, practical, and solution-oriented, with approximately 25,000 words for this portion.
```

### Prompt 3: Book 2 Manuscript Draft - Part 2 (Chapters 4-8)
```
Draft the second part of Book 2: "Great Nigeria – The Masterplan for Empowered Decentralized Action" including:



Follow these guidelines:
- Use clear, instructional language that provides specific, actionable guidance
- Include all forum topics and actionable steps as specified in the TOC
- Incorporate properly attributed research from Nigerian and international sources
- Provide concrete examples, tools, and templates for implementation
- Use the term "Citizen Engagement" or "Citizen Action" instead of "Revolution"
- Include section and subsection numbering throughout
- Maintain a documentary research style with proper citations

The manuscript should be comprehensive, practical, and solution-oriented, with approximately 30,000 words for this portion.
```

### Prompt 4: Book 2 Manuscript Draft - Part 3 (Chapters 9-12 and Back Matter)
```
Draft the third part of Book 2: "Great Nigeria – The Masterplan for Empowered Decentralized Action" including:

1. Part III: Implementation and Sustainability
   - Chapter 9: The Strategic Action Plan – Phased Implementation
   - Chapter 10: Ensuring Integrity – Accountability Within the Movement
   - Chapter 11: The Long Game – Sustaining Transformation
   - Chapter 12: Your Commitment – Personal Action Planning

2. Back Matter:
   - Appendix A: Implementation Templates and Worksheets
   - Appendix B: Resource Mobilization Guide
   - Appendix C: Conflict Resolution Toolkit
   - Appendix D: Directory of Partner Organizations
   - Appendix E: Legal Resources for Civic Actors
   - Glossary of Terms
   - Bibliography
   - About the Author
   - About the Great Nigeria Initiative

Follow these guidelines:
- Use clear, instructional language that provides specific, actionable guidance
- Include all forum topics and actionable steps as specified in the TOC
- Incorporate properly attributed research from Nigerian and international sources
- Provide concrete examples, tools, and templates for implementation
- Use the term "Citizen Engagement" or "Citizen Action" instead of "Revolution"
- Include section and subsection numbering throughout
- Maintain a documentary research style with proper citations

The manuscript should be comprehensive, practical, and solution-oriented, with approximately 25,000 words for this portion.
```

### Prompt 5: Book 2 Cover Image Generation
```
Generate a cover image for Book 2: "Great Nigeria – The Masterplan for Empowered Decentralized Action" with the following specifications:

1. Title: "GREAT NIGERIA" (prominent)
2. Subtitle: "The Masterplan for Empowered Decentralized Action" (smaller)
3. Author: "Samuel Chimezie Okechukwu" (at bottom)

Visual elements should include:
- A stylized map or outline of Nigeria
- Imagery suggesting planning, building, or construction (e.g., blueprint, framework, or building blocks)
- Green and white color scheme (Nigerian colors) with accent colors for emphasis
- Modern, bold typography
- Clean, professional layout suitable for both print and digital formats

The design should convey:
- Strategic thinking and planning
- Empowerment and capability
- Structured approach to change
- Professional credibility

The image should be high resolution (at least 300 dpi) and in portrait orientation with dimensions suitable for a book cover.
```

### Prompt 6: Book 2 Final PDF Compilation
```
Compile the complete Book 2: "Great Nigeria – The Masterplan for Empowered Decentralized Action" into a professionally formatted PDF manuscript with the following specifications:

1. Include the generated cover image as the first page
2. Format all content according to professional publishing standards:
   - Consistent typography with appropriate heading hierarchy
   - Page numbers
   - Running headers/footers
   - Proper paragraph spacing and indentation
   - Balanced page layout
   - Appropriate placement of forum topics and actionable steps
   - Properly formatted citations and bibliography

3. Include all sections from the TOC:
   - Front Matter (title page through introduction)
   - Part I: Foundations for Change (Chapters 1-3)
   - Part II: The Five Pillars of Action (Chapters 4-8)
   - Part III: Implementation and Sustainability (Chapters 9-12)
   - Back Matter (appendices through about the initiative)

4. Ensure all cross-references are functional
5. Verify all citations are complete and properly formatted
6. Check that all section and subsection numbering is consistent
7. Include all templates, worksheets, and tools in the appendices

The final PDF should be publication-ready with professional appearance and complete content as specified in the Book 2 TOC.
```

## 4. Book 3 Manuscript

### Prompt 1: Book 3 Research and Content Planning
```
Conduct comprehensive research for Book 3: "Great Nigeria – A Story of Crises, Hope, and Collective Victory (Comprehensive Edition)" following these guidelines:

1. Research Nigerian news sources (Punch, Vanguard, Guardian Nigeria, ThisDay, Daily Trust, Premium Times, Arise TV News) for in-depth analysis of:
   - Historical developments from pre-colonial to present day
   - Contemporary political, economic, and social challenges
   - Sectoral analyses (education, healthcare, infrastructure, etc.)
   - International relations and global positioning
   - Future scenarios and development pathways

2. Research academic sources for:
   - Pre-colonial Nigerian societies and governance systems
   - Colonial impact analysis
   - Post-independence critical periods
   - Comparative post-colonial development
   - Theoretical frameworks for national development

3. Research international organizations' reports on:
   - Nigeria's development indicators
   - Sectoral analyses and recommendations
   - Comparative country studies
   - Future projections and scenarios

4. Identify expert perspectives from:
   - Academic specialists
   - Policy practitioners
   - Civil society leaders
   - Business and technology innovators

All research must be properly attributed with verifiable sources. For generated content representing expert perspectives, clearly mark as such with appropriate attribution.

Organize research by chapter and section according to the Book 3 TOC, filling in all empty placeholders with substantive content based on research findings.
```

### Prompt 2: Book 3 Manuscript Draft - 
```
Draft Part I of Book 3: "Great Nigeria – A Story of Crises, Hope, and Collective Victory (Comprehensive Edition)" including:


2. Part I: Understanding Nigeria's Crisis
   - Chapter 1: The Bleeding Giant – Nigeria's Paradox of Potential and Reality
   - Chapter 2: Ghosts of the Past – Historical Foundations of Present Challenges
   - Chapter 3: Before the Chains – Nigeria's Ancient Foundations
   - Chapter 4: The Colonial Disruption – Artificial Boundaries and Structural Distortions
   - Chapter 5: Independence and Its Discontents – The Unfulfilled Promise of Self-Rule

Follow these guidelines:
- Use scholarly yet accessible language that balances academic rigor with readability
- Include all poems, forum topics, and actionable steps as specified in the TOC
- Fill in all empty placeholders with substantive content based on research
- Incorporate properly attributed research from Nigerian and international sources
- Use the term "Citizen Engagement" or "Citizen Action" instead of "Revolution"
- Include section and subsection numbering throughout
- Maintain a documentary research style with proper citations

The manuscript should be comprehensive, scholarly, and thorough, for this portion.
```

### Prompt 3: Book 3 Manuscript Draft -
```
Draft  Book 3: "Great Nigeria – A Story of Crises, Hope, and Collective Victory (Comprehensive Edition)" including:

Follow these guidelines:
- Use scholarly yet accessible language that balances academic rigor with readability
- Include all poems, forum topics, and actionable steps as specified in the TOC
- Fill in all empty placeholders with substantive content based on research
- Incorporate properly attributed research from Nigerian and international sources
- Use the term "Citizen Engagement" or "Citizen Action" instead of "Revolution"
- Include section and subsection numbering throughout
- Maintain a documentary research style with proper citations

The manuscript should be comprehensive, scholarly, and thorough, with approximately 40,000 words for this portion.
```

### Prompt 4: Book 3 Manuscript Draft -
```
Book 3: "Great Nigeria – A Story of Crises, Hope, and Collective Victory (Comprehensive Edition)" including:



Follow these guidelines:
- Use scholarly yet accessible language that balances academic rigor with readability
- Include all poems, forum topics, and actionable steps as specified in the TOC
- Fill in all empty placeholders with substantive content based on research
- Incorporate properly attributed research from Nigerian and international sources
- Use the term "Citizen Engagement" or "Citizen Action" instead of "Revolution"
- Include section and subsection numbering throughout
- Maintain a documentary research style with proper citations

The manuscript should be comprehensive, scholarly, and thorough, with approximately 40,000 words for this portion.
```

### Prompt 5: Book 3 Manuscript Draft -
```
Draft   Book 3: "Great Nigeria – A Story of Crises, Hope, and Collective Victory (Comprehensive Edition)" including:

Follow these guidelines:
- Use scholarly yet accessible language that balances academic rigor with readability
- Include all poems, forum topics, and actionable steps as specified in the TOC
- Fill in all empty placeholders with substantive content based on research
- Incorporate properly attributed research from Nigerian and international sources
- Use the term "Citizen Engagement" or "Citizen Action" instead of "Revolution"
- Include section and subsection numbering throughout
- Maintain a documentary research style with proper citations

The manuscript should be comprehensive, scholarly, and thorough, with approximately 40,000 words for this portion.
```

### Prompt 6: Book 3 Manuscript Draft - Back Matter
```
Draft the Back Matter for Book 3: "Great Nigeria – A Story of Crises, Hope, and Collective Victory (Comprehensive Edition)" including:

1. Appendices:
   - Appendix A: Historical Timeline of Nigeria
   - Appendix B: Key Development Indicators
   - Appendix C: Comparative Country Analysis
   - Appendix D: Implementation Templates and Tools
   - Appendix E: Directory of Resources and Organizations

2. Reference Material:
   - Glossary of Terms
   - Bibliography (organized by chapter)
   - Index
   - About the Author
   - About the Great Nigeria Initiative

Follow these guidelines:
- Ensure all appendices contain comprehensive, accurate information
- Create a complete bibliography with all sources properly cited
- Develop a detailed index covering key terms, concepts, and names
- Include all cross-references and ensure they are accurate
- Maintain a scholarly, professional tone throughout

The back matter should be thorough and well-organized, providing valuable reference material to support the main text, with approximately 20,000 words.
```

### Prompt 7: Book 3 Cover Image Generation
```
Generate a cover image for Book 3: "Great Nigeria – A Story of Crises, Hope, and Collective Victory (Comprehensive Edition)" with the following specifications:

1. Title: "GREAT NIGERIA" (prominent)
2. Subtitle: "A Story of Crises, Hope, and Collective Victory" (smaller)
3. Additional text: "Comprehensive Edition" (smallest)
4. Author: "Samuel Chimezie Okechukwu" (at bottom)

Visual elements should include:
- A stylized map or outline of Nigeria
- Imagery suggesting transformation, progress, or victory (e.g., rising sun, upward trajectory)
- Green and white color scheme (Nigerian colors) with accent colors for emphasis
- Modern, bold typography
- Clean, professional layout suitable for both print and digital formats

The design should convey:
- Comprehensive, authoritative content
- Hope emerging from challenge
- Collective achievement
- Professional, scholarly credibility

The image should be high resolution (at least 300 dpi) and in portrait orientation with dimensions suitable for a book cover.
```

### Prompt 8: Book 3 Final PDF Compilation
```
Compile the complete Book 3: "Great Nigeria – A Story of Crises, Hope, and Collective Victory (Comprehensive Edition)" into a professionally formatted PDF manuscript with the following specifications:

1. Include the generated cover image as the first page
2. Format all content according to professional publishing standards:
   - Consistent typography with appropriate heading hierarchy
   - Page numbers
   - Running headers/footers
   - Proper paragraph spacing and indentation
   - Balanced page layout
   - Appropriate placement of poems, forum topics, and actionable steps
   - Properly formatted citations and bibliography

3. Include all sections from the TOC:
   - Front Matter (title page through list of acronyms)
   - All Chapters Content
   - Back Matter (appendices through about the initiative)

4. Ensure all cross-references are functional
5. Verify all citations are complete and properly formatted
6. Check that all section and subsection numbering is consistent
7. Include all appendices with complete information

The final PDF should be publication-ready with professional appearance and complete content as specified in the Book 3 TOC, with no empty placeholders.
```

## 5. Website Code Completion

### Prompt 1: Website Code Analysis and Gap Identification
```
Analyze the Great Nigeria website codebase to identify specific code gaps and implementation needs. For each pending feature identified in the REMAINING_FEATURES_IMPLEMENTATION_PLAN.md:

1. Identify existing code components that can be leveraged
2. Determine missing components that need to be implemented
3. Analyze database schema requirements and any needed migrations
4. Identify API endpoints that need to be created or modified
5. Determine frontend components that need to be developed

Organize the analysis by feature category and provide specific file paths and code snippets where relevant. For each feature, provide:
- Current implementation status
- Dependencies on other components
- Specific code files that need to be created or modified
- Database changes required
- API endpoint specifications
```

### Prompt 2: Backend Code Implementation - Priority Services
```
Implement the backend code for the priority services identified in the gap analysis:

1. Marketplace Service:
   - Create service structure following the existing microservice pattern
   - Implement data models, repositories, and services
   - Develop API handlers and endpoints
   - Create database migrations
   - Implement integration with other services

2. Affiliate Service:
   - Create service structure following the existing microservice pattern
   - Implement data models, repositories, and services
   - Develop API handlers and endpoints
   - Create database migrations
   - Implement integration with other services

3. Enhanced Wallet Service:
   - Refactor existing wallet functionality into a dedicated service
   - Implement additional features identified in the gap analysis
   - Create database migrations for new functionality
   - Develop API handlers and endpoints
   - Ensure backward compatibility with existing code

Follow these guidelines:
- Maintain consistent coding style with existing codebase
- Include comprehensive error handling
- Add unit tests for all new functionality
- Document all code with appropriate comments
- Follow the microservices architecture pattern established in the codebase
```

### Prompt 3: Frontend Code Implementation - Priority Features
```
Implement the frontend code for the priority features identified in the gap analysis:

1. Marketplace UI Components:
   - Create React components following the existing pattern
   - Implement state management using Redux
   - Develop API service integration
   - Create responsive layouts for all screen sizes
   - Implement user interaction flows

2. Affiliate UI Components:
   - Create React components following the existing pattern
   - Implement state management using Redux
   - Develop API service integration
   - Create responsive layouts for all screen sizes
   - Implement user interaction flows

3. Enhanced Wallet UI Components:
   - Update existing wallet components
   - Implement new features identified in the gap analysis
   - Ensure responsive design for all screen sizes
   - Implement user interaction flows
   - Integrate with backend API

Follow these guidelines:
- Maintain consistent coding style with existing codebase
- Follow React best practices
- Ensure accessibility compliance
- Implement responsive design for all screen sizes
- Add unit tests for all new components
- Document all code with appropriate comments
```

### Prompt 4: Integration and Testing
```
Implement integration and testing for the newly developed features:

1. API Gateway Integration:
   - Update API gateway configuration to include new services
   - Implement routing rules for new endpoints
   - Configure authentication and authorization for new routes
   - Set up rate limiting and throttling as needed

2. Service-to-Service Communication:
   - Implement message passing between services as needed
   - Configure service discovery for new services
   - Set up health checks and monitoring

3. Testing Suite:
   - Create unit tests for all new components
   - Implement integration tests for service interactions
   - Develop end-to-end tests for user flows
   - Create performance tests for high-traffic scenarios

4. Documentation:
   - Update API documentation with new endpoints
   - Document service interactions and dependencies
   - Create usage examples for frontend components
   - Update database schema documentation

Follow these guidelines:
- Ensure comprehensive test coverage for all new code
- Document all integration points and dependencies
- Verify backward compatibility with existing features
- Implement proper error handling and logging
- Follow established patterns for service communication
```

### Prompt 5: Deployment Setup Guide
```
Create a comprehensive deployment setup guide for the Great Nigeria website platform:

1. Server Requirements:
   - Hardware specifications
   - Operating system requirements
   - Network configuration
   - Storage requirements

2. Database Setup:
   - PostgreSQL installation and configuration
   - Database creation and user setup
   - Migration execution
   - Backup and recovery procedures

3. Backend Deployment:
   - Go environment setup
   - Service compilation and deployment
   - Environment variable configuration
   - Service orchestration (Docker, Kubernetes, or systemd)

4. Frontend Deployment:
   - Node.js environment setup
   - Build process
   - Static file serving
   - CDN configuration (if applicable)

5. API Gateway Configuration:
   - Installation and setup
   - Routing configuration
   - SSL/TLS setup
   - Rate limiting and security settings

6. Monitoring and Maintenance:
   - Logging configuration
   - Monitoring setup
   - Backup procedures
   - Update process

7. Scaling Considerations:
   - Horizontal scaling approach
   - Database scaling strategy
   - Caching implementation
   - Load balancing configuration

The guide should be detailed enough for a system administrator to deploy the platform from scratch, with step-by-step instructions and troubleshooting tips.
```

### Prompt 6: Website Code Package Preparation
```
Prepare the complete website code package for deployment:

1. Code Organization:
   - Ensure all code is properly organized in the repository structure
   - Remove any temporary or development files
   - Verify all dependencies are properly declared
   - Check for and remove any sensitive information

2. Documentation:
   - Include README files for all major components
   - Document environment variables and configuration options
   - Provide setup and deployment instructions
   - Include API documentation

3. Build Scripts:
   - Create scripts for building all services if not exists
   - Implement database migration scripts if not exists
   - Develop deployment automation scripts if not exists
   - Include rollback procedures

4. Configuration Templates:
   - Provide example configuration files
   - Include environment-specific templates (development, staging, production)
   - Document all configuration options
   - Include security best practices

5. Final Package:
   - Create a ZIP archive of the complete codebase
   - Ensure all necessary files are included
   - Verify the package can be extracted and built
   - Include the deployment setup guide

The final package should be complete, well-documented, and ready for deployment on the user's server.
```
