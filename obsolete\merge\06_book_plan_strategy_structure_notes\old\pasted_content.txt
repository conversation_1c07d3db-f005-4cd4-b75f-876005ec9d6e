Project plan is okay, generate list out the full ai prompts needed to fully execute the plan immediately and run them as needed while taking note of additional info and requests below.  Ensure adequate research into the issues and quote from verified international and local sources , include nigerian newspapers and their social media as sources, ,youtube channels with adequate reference, relevant comments from citizens  -  as detailed in dev_plan.txt old content development guidelines plan while adapting it to your current plan
Do not attribute to known individuals what they did not say  to avoid legal battle Be sure every attribution is fully cited and verifiable asides from generated quotes which you attributed to the author 
Remember that real Events or stories from real eyewitness as you researched should be attributed to their owner as stated from the published source you used ---or generated stories attributed to fake owners (as identity Hidden)  without using publicly known persons - More like a Documentary Research work that we interviewed people (even if not real ) Do not attribute stories or events  to the author as he may not be able to defend it if questioned, rather report them as third person telling me interview documentary style  book 1 and book 2 toc that you wrote is great and complete asides from the omitted sections / subsections numbering -  

book 3 toc is incomplete so many empty placeholders, are you waiting to research fully before you complete it? purpose of summaries you wrote for each file was for you was to enable you  capture all the files content into the comprehensive edition so nothing is omitted , revisit the summaries summary_* files  and its linked files so you can grab all the data needed to complete book 3 toc while also researching to fill in the gaps as you detailed in research_streams.md and gap_analysis.md


I have attached the Great Nigeria Website Project files that contains a lot of old and new documentation about the website , all existing features built in and pending features, it also contains all the code files
Website backend was built on go and a react frontend  found in folder great-nigeria-frontend

Extract the attached folder to see all its content, list all its files , folders all levels deep so you can see all files and structure

Analyze all code files so you can  see all current features existing that are completed

Analyze the  website documentation files all that was planned before code files were written, some docs are duplicates and some not yet updated

Output 

1. Website Documentation - Itemize all that has been done and all that is left to do and what you think should be added to enhance it,  in a unified documentation for website with sections grouped accordingly


2. Complete the Full PDF Manuscript for Book 1 which contains cover image you generated and all content for book 1 as a ready to be published book ensure you do not omit content from original while also adding new content you feel is necessary as planned in the gap analysis and research


3. Complete the Full PDF Manuscript for Book 2 which contains cover image you generated and all content for book 2 as a ready to be published book ensure you do not omit content from original while also adding new content you feel is necessary as planned in the gap analysis and research

4. Complete the Full PDF Manuscript for Book 3 which contains cover image you generated and all content for book 3 as a ready to be published book ensure you do not omit content from original while also adding new content you feel is necessary as planned in the gap analysis and research
5. Complete the Website code files as detailed in 1  and output the final zipped  and deployment setup guide i need to host in on my server