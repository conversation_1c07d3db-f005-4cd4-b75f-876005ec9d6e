# Celebrate Nigeria Feature - Code Analysis

## Overview

This document provides a detailed analysis of the code implementation for the Celebrate Nigeria feature, including the data models, repository layer, service layer, and API endpoints.

## Directory Structure

The Celebrate Nigeria feature code is organized as follows:

```
internal/
  celebration/
    migrations/           # Database migrations
      001_create_celebration_tables.sql
    models/               # Data models
      models.go
    repository/           # Data access layer
      repository.go
    service/              # Business logic
      service.go
    handlers/             # API endpoints
      handlers.go
scripts/
  populate_celebrate_nigeria.go  # Data population script
  create_celebrate_image_dirs.sh # Image directory setup script
  run_celebrate_nigeria_population.sh # Script to run data population
web/
  static/
    images/
      celebrate/          # Image assets
        people/           # Images for people entries
        places/           # Images for places entries
        events/           # Images for events entries
    js/
      celebrate.js        # Frontend JavaScript
  templates/
    celebrate.html        # Main template
    celebrate_detail.html # Detail page template
```

## Data Models

The Celebrate Nigeria feature uses a comprehensive set of data models to represent different types of entries and their relationships.

### Core Models

#### Category

```go
type Category struct {
    ID          int64
    ParentID    *int64
    Name        string
    Slug        string
    Description string
    ImageURL    string
    SortOrder   int
    CreatedAt   time.Time
    UpdatedAt   time.Time
}
```

The `Category` model represents a category in the hierarchical category system. Categories can have parent categories, creating a tree structure.

#### CelebrationEntry

```go
type CelebrationEntry struct {
    ID             int64
    EntryType      string // "person", "place", "event"
    Slug           string
    Title          string
    ShortDesc      string
    FullDesc       string
    PrimaryImageURL string
    Location       string
    FeaturedRank   int
    Status         string // "draft", "published", "archived"
    Categories     []Category
    Facts          []EntryFact
    Media          []EntryMedia
    Comments       []EntryComment
    Votes          []EntryVote
    CreatedAt      time.Time
    UpdatedAt      time.Time
}
```

The `CelebrationEntry` model is the base model for all entries in the Celebrate Nigeria feature. It contains common fields shared by all entry types.

#### Type-Specific Models

##### PersonEntry

```go
type PersonEntry struct {
    CelebrationEntryID int64
    BirthDate          *time.Time
    DeathDate          *time.Time
    Profession         string
    Achievements       string
    Contributions      string
    Education          string
    RelatedLinks       string
}
```

The `PersonEntry` model extends the base `CelebrationEntry` with fields specific to people.

##### PlaceEntry

```go
type PlaceEntry struct {
    CelebrationEntryID int64
    PlaceType          string
    Latitude           float64
    Longitude          float64
    Address            string
    VisitingHours      string
    VisitingFees       string
    Accessibility      string
    History            string
}
```

The `PlaceEntry` model extends the base `CelebrationEntry` with fields specific to places.

##### EventEntry

```go
type EventEntry struct {
    CelebrationEntryID  int64
    EventType           string
    StartDate           *time.Time
    EndDate             *time.Time
    IsRecurring         bool
    RecurrencePattern   string
    Organizer           string
    ContactInfo         string
    EventHistory        string
}
```

The `EventEntry` model extends the base `CelebrationEntry` with fields specific to events.

### Supporting Models

#### EntryFact

```go
type EntryFact struct {
    ID                 int64
    CelebrationEntryID int64
    Label              string
    Value              string
    SortOrder          int
    CreatedAt          time.Time
    UpdatedAt          time.Time
}
```

The `EntryFact` model represents a key fact about an entry, displayed as a label-value pair.

#### EntryMedia

```go
type EntryMedia struct {
    ID                 int64
    CelebrationEntryID int64
    MediaType          string // "image", "video", "audio"
    URL                string
    Caption            string
    SortOrder          int
    CreatedAt          time.Time
    UpdatedAt          time.Time
}
```

The `EntryMedia` model represents a media item associated with an entry, such as an image or video.

#### EntryComment

```go
type EntryComment struct {
    ID                 int64
    CelebrationEntryID int64
    UserID             int64
    ParentCommentID    *int64
    Content            string
    Status             string // "pending", "approved", "rejected"
    CreatedAt          time.Time
    UpdatedAt          time.Time
}
```

The `EntryComment` model represents a user comment on an entry. Comments can be nested through the `ParentCommentID` field.

#### EntryVote

```go
type EntryVote struct {
    ID                 int64
    CelebrationEntryID int64
    UserID             int64
    VoteType           string // "upvote", "downvote"
    CreatedAt          time.Time
}
```

The `EntryVote` model represents a user vote on an entry, which can be either an upvote or a downvote. This model is used to track user votes and calculate the popularity of entries. The voting system allows users to express their opinion on entries and helps surface the most valuable content.

The voting system has been fully implemented with the following components:

1. **Repository Methods**:
   - `VoteForEntry`: Adds or updates a vote for an entry
   - `GetEntryVoteCounts`: Gets the upvote and downvote counts for an entry
   - `GetUserVoteForEntry`: Gets a user's vote for an entry
   - `DeleteVoteForEntry`: Removes a user's vote for an entry
   - `updateEntryLikeCount`: Updates the like count field in the entry table

2. **Service Methods**:
   - `VoteForEntry`: Validates and processes a vote for an entry
   - `GetEntryVoteCounts`: Gets the upvote and downvote counts for an entry
   - `GetUserVoteForEntry`: Gets a user's vote for an entry
   - `DeleteVoteForEntry`: Removes a user's vote for an entry

3. **Handler Methods**:
   - `VoteForEntry`: Handles POST requests to vote on an entry
   - `DeleteVoteForEntry`: Handles DELETE requests to remove a vote
   - `GetEntryVotes`: Handles GET requests to retrieve vote counts

4. **Frontend Components**:
   - `celebrate-voting.js`: JavaScript for handling vote actions
   - `celebrate-voting.css`: Styles for the voting UI
   - `celebrate-voting.html`: HTML template for the voting component

#### EntrySubmission

```go
type EntrySubmission struct {
    ID            int64        `json:"id" db:"id"`
    UserID        int64        `json:"user_id" db:"user_id"`
    EntryType     string       `json:"entry_type" db:"entry_type"`
    TargetEntryID *int64       `json:"target_entry_id,omitempty" db:"target_entry_id"`
    Title         string       `json:"title" db:"title"`
    Content       string       `json:"content" db:"content"`
    Status        string       `json:"status" db:"status"`
    AdminNotes    *string      `json:"admin_notes,omitempty" db:"admin_notes"`
    ReviewedBy    *int64       `json:"reviewed_by,omitempty" db:"reviewed_by"`
    ReviewedAt    sql.NullTime `json:"reviewed_at,omitempty" db:"reviewed_at"`
    VoteCount     int          `json:"vote_count" db:"vote_count"`
    CreatedAt     time.Time    `json:"created_at" db:"created_at"`
    UpdatedAt     time.Time    `json:"updated_at" db:"updated_at"`
}
```

The `EntrySubmission` model represents a user-submitted entry that is pending review and approval. The submission workflow has been fully implemented with the following components:

1. **Repository Methods**:
   - `CreateSubmission`: Creates a new entry submission
   - `UpdateSubmission`: Updates a submission's status and review information
   - `ListPendingSubmissions`: Retrieves pending submissions for review
   - `VoteForSubmission`: Adds a vote to a submission

2. **Service Methods**:
   - `CreateEntrySubmission`: Validates and creates a new submission
   - `ReviewSubmission`: Updates the status of a submission
   - `ListPendingSubmissions`: Retrieves pending submissions for review
   - `VoteForSubmission`: Adds a vote to a submission

3. **Handler Methods**:
   - `CreateEntrySubmission`: Handles POST requests to create a new submission
   - `ReviewSubmission`: Handles PUT requests to review a submission
   - `ListPendingSubmissions`: Handles GET requests to retrieve pending submissions
   - `VoteForSubmission`: Handles POST requests to vote on a submission
   - `RenderSubmissionForm`: Renders the submission form template
   - `RenderAdminSubmissions`: Renders the admin review page template

4. **Frontend Components**:
   - `celebrate-submission.html`: HTML template for the submission form
   - `celebrate-submission.css`: Styles for the submission form
   - `celebrate-submission.js`: JavaScript for handling form submission
   - `celebrate-admin-submissions.html`: HTML template for the admin review page
   - `celebrate-admin.css`: Styles for the admin review page
   - `celebrate-admin.js`: JavaScript for handling submission review

## Database Schema

The database schema for the Celebrate Nigeria feature is defined in `internal/celebration/migrations/001_create_celebration_tables.sql`. The schema includes the following tables:

- `categories`: Stores category information
- `celebration_entries`: Base table for all entries
- `person_entries`: Person-specific data
- `place_entries`: Place-specific data
- `event_entries`: Event-specific data
- `entry_categories`: Many-to-many relationship between entries and categories
- `entry_facts`: Facts about entries
- `entry_media`: Media items for entries
- `entry_comments`: User comments on entries
- `entry_votes`: User votes on entries
- `entry_submissions`: User-submitted entries pending review

## Repository Layer

The repository layer provides data access methods for the Celebrate Nigeria feature. It includes methods for:

- Retrieving entries by ID, slug, or category
- Creating and updating entries
- Managing entry relationships (categories, facts, media)
- Handling user interactions (comments, votes, submissions)

## Service Layer

The service layer implements the business logic for the Celebrate Nigeria feature. It includes methods for:

- Entry management (creation, retrieval, update)
- Category management
- User interaction processing (comments, votes, submissions)
- Search functionality

## API Endpoints

The API endpoints for the Celebrate Nigeria feature include:

### Category Endpoints
- `GET /api/celebrate/categories`: Get all categories
- `GET /api/celebrate/categories/:slug`: Get category by slug
- `GET /api/celebrate/categories/:slug/entries`: Get entries by category

### Entry Endpoints
- `GET /api/celebrate/entries`: Get all entries with optional filtering
- `GET /api/celebrate/entries/:slug`: Get entry by slug
- `GET /api/celebrate/entries/featured`: Get featured entries

### Comment Endpoints
- `POST /api/celebrate/entries/:id/comments`: Add a comment to an entry
- `GET /api/celebrate/entries/:id/comments`: Get comments for an entry

### Voting Endpoints
- `POST /api/celebrate/entries/:id/vote`: Vote on an entry
- `DELETE /api/celebrate/entries/:id/vote`: Remove a vote from an entry
- `GET /api/celebrate/entries/:id/votes`: Get vote counts for an entry

### Submission Endpoints
- `POST /api/celebrate/submissions`: Submit a new entry
- `GET /api/celebrate/submissions`: Get pending submissions
- `PUT /api/celebrate/submissions/:id`: Update a submission
- `POST /api/celebrate/submissions/:id/approve`: Approve a submission

## Data Population Script

The data population script (`scripts/populate_celebrate_nigeria.go`) is used to populate the database with initial entries for the Celebrate Nigeria feature. The script includes:

### Data Structures

```go
// Entry types
const (
    PersonEntryType = "person"
    PlaceEntryType  = "place"
    EventEntryType  = "event"
)

// Entry represents the base data for an entry
type Entry struct {
    Title           string
    Slug            string
    ShortDesc       string
    FullDesc        string
    PrimaryImageURL string
    Location        string
    FeaturedRank    int
    CategorySlugs   []string
    Facts           []Fact
    Media           []Media
}

// PersonData represents person-specific data
type PersonData struct {
    Entry
    BirthDate     *time.Time
    DeathDate     *time.Time
    Profession    string
    Achievements  string
    Contributions string
    Education     string
    RelatedLinks  string
}

// PlaceData represents place-specific data
type PlaceData struct {
    Entry
    PlaceType     string
    Latitude      float64
    Longitude     float64
    Address       string
    VisitingHours string
    VisitingFees  string
    Accessibility string
    History       string
}

// EventData represents event-specific data
type EventData struct {
    Entry
    EventType         string
    StartDate         *time.Time
    EndDate           *time.Time
    IsRecurring       bool
    RecurrencePattern string
    Organizer         string
    ContactInfo       string
    EventHistory      string
}

// Fact represents a key fact about an entry
type Fact struct {
    Label     string
    Value     string
    SortOrder int
}

// Media represents a media item for an entry
type Media struct {
    MediaType string
    URL       string
    Caption   string
    SortOrder int
}
```

### Database Operations

The script includes functions for:

- Connecting to the database
- Checking if required tables exist
- Inserting entries into the database
- Creating relationships between entries and categories
- Adding facts and media to entries

### Sample Data

The script includes sample data for:

- People (e.g., Chinua Achebe, Wole Soyinka, Ngozi Okonjo-Iweala)
- Places (e.g., Zuma Rock, Osun-Osogbo Sacred Grove, Eko Atlantic City)
- Events (e.g., Eyo Festival, Nigeria Independence Day, Lagos International Jazz Festival)

## Frontend Implementation

The frontend implementation for the Celebrate Nigeria feature includes:

- Main page template (`web/templates/celebrate-home.html`)
- Detail page template (`web/templates/celebrate-detail.html`)
- Search page template (`web/templates/celebrate-search.html`)
- Submission form template (`web/templates/celebrate-submission.html`)
- Moderation dashboard template (`web/templates/celebrate-moderation-dashboard.html`)
- JavaScript for dynamic content loading and user interactions (`web/static/js/celebrate.js`)

## Moderation System

The moderation system for the Celebrate Nigeria feature has been fully implemented. It includes:

1. **Content Flagging**: Allows users to flag inappropriate content
   - `EntryFlag` model for storing flags
   - Repository methods for creating and retrieving flags
   - Service methods for flagging entries and reviewing flags
   - Handler methods for flagging entries and reviewing flags
   - Frontend components for flagging content and reviewing flags

2. **Moderation Queue**: A queue for moderators to review content
   - `EntryModerationQueue` model for storing queue items
   - Repository methods for creating and retrieving queue items
   - Service methods for adding to the queue and processing queue items
   - Handler methods for managing the queue
   - Frontend components for viewing and processing the queue

3. **Moderation Dashboard**: An interface for moderators to manage content
   - Dashboard with statistics on pending flags and queue items
   - Tabs for flagged content, moderation queue, and history
   - Detailed views for reviewing flags and queue items
   - Actions for approving, rejecting, or hiding content

4. **Content Filtering**: Automatic filtering of content based on predefined rules
   - `EntryFilterResult` model for storing filter results
   - Repository methods for creating and retrieving filter results
   - Service methods for filtering entry content
   - Integration with the existing moderation system

## Conclusion

The Celebrate Nigeria feature has a comprehensive code implementation that follows best practices for separation of concerns and modularity. The data models are well-defined, and the database schema supports all required functionality. The data population script provides a solid foundation of initial content for the feature.

All user interaction features have been fully implemented, including comments, voting, submissions, and moderation. The frontend components provide a complete user experience for interacting with the content.

The next steps in the implementation should focus on enhancing the frontend experience, optimizing performance, and refining the search functionality with advanced filtering.
