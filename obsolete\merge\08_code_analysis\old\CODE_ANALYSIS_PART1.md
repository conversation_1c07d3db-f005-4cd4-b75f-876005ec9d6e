# Great Nigeria Project - Code Analysis (Part 1)

This document provides a comprehensive analysis of the Great Nigeria project codebase, detailing the architecture, key components, and functionality of the system.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Core Architecture](#core-architecture)
3. [API Gateway](#api-gateway)
4. [Microservices](#microservices)
5. [Content Management System](#content-management-system)
6. [Book Structure and Content](#book-structure-and-content)
7. [Discussion and Community Features](#discussion-and-community-features)
8. [Points and Rewards System](#points-and-rewards-system)
9. [Citation and Bibliography System](#citation-and-bibliography-system)
10. [Additional Features](#additional-features)
11. [Backup and Data Integrity](#backup-and-data-integrity)
12. [Frontend Components](#frontend-components)

## Project Overview

The Great Nigeria project is a comprehensive digital platform built using a microservices architecture with Go (Golang) as the primary programming language. The system serves as an engagement hub for a three-book series about Nigeria's socio-economic transformation, offering features such as:

- Book content delivery with interactive elements
- Community discussion forums
- Points-based reward system
- User authentication and profile management
- Nigerian payment processing integration
- Celebration of Nigerian culture through specialized directories
- Project management and implementation reporting tools
- Enhanced accessibility features including Voice Navigation

The project is transitioning from a Node.js/Express architecture to a more scalable Go-based microservices approach, providing better performance and maintainability.

## Core Architecture

The Great Nigeria project follows a modern microservices architecture pattern with a clear separation of concerns:

1. **Entry Points (`cmd/`)**: Main applications that serve as entry points for various services
2. **Internal Packages (`internal/`)**: Business domain-specific packages that form the core of the application
3. **Common Packages (`pkg/`)**: Shared utilities and models used across multiple services
4. **Standalone Utilities (root directory)**: Specialized tools for content management and updates

Each service follows a layered architecture pattern:
- **Handlers**: Handle HTTP requests and responses, delegating business logic to services
- **Services**: Contain business logic and orchestrate operations across repositories
- **Repositories**: Handle data access and persistence operations
- **Models**: Define data structures used throughout the application

### `cmd/api-gateway/main.go`

This file serves as the entry point for the API Gateway, which is the central access point for all client requests. Key responsibilities include:

- Initializing the Gin web framework
- Registering all route handlers from various services
- Setting up middleware for authentication, logging, and CORS
- Connecting to the PostgreSQL database
- Starting the HTTP server on port 5000

The API Gateway implements a microservices architecture pattern, routing requests to appropriate internal services based on their endpoints.

```go
func main() {
    // Initialize router
    router := gin.Default()
    
    // Set up middleware
    router.Use(middleware.Auth())
    router.Use(middleware.Logging())
    router.Use(middleware.CORS())
    
    // Initialize database
    db := database.InitDatabase()
    
    // Initialize services
    authService := auth.NewAuthService(db)
    contentService := content.NewContentService(db)
    discussionService := discussion.NewDiscussionService(db)
    pointsService := points.NewPointsService(db)
    
    // Initialize handlers
    authHandler := handlers.NewAuthHandler(authService)
    contentHandler := handlers.NewContentHandler(contentService)
    discussionHandler := handlers.NewDiscussionHandler(discussionService, pointsService)
    
    // Register routes
    authHandler.RegisterRoutes(router.Group("/api/auth"))
    contentHandler.RegisterRoutes(router.Group("/api/content"))
    discussionHandler.RegisterRoutes(router.Group("/api/discussions"))
    
    // Start server
    port := os.Getenv("PORT")
    if port == "" {
        port = "5000"
    }
    
    logger.Info(fmt.Sprintf("Server running on port %s", port))
    router.Run(fmt.Sprintf("0.0.0.0:%s", port))
}
```

### `pkg/common` Package Files

These files provide shared utilities used across the application:

- `config/` - Configuration management for services
- `database/` - Database connection and transaction utilities
- `logger/` - Centralized logging functionality
- `middleware/` - Shared HTTP middleware components

## API Gateway

The API Gateway serves as the central entry point for all client requests, providing:

- Routing to appropriate microservices
- Authentication and authorization
- Request/response transformation
- Rate limiting and throttling
- API documentation (Swagger)

### Route Configuration

```go
// Content routes
contentRoutes := router.Group("/api/content")
{
    contentRoutes.GET("/books", contentHandler.GetBooks)
    contentRoutes.GET("/books/:id", contentHandler.GetBookByID)
    contentRoutes.GET("/chapters/:id", contentHandler.GetChapterByID)
    contentRoutes.GET("/sections/:id", contentHandler.GetSectionByID)
}

// Discussion routes
discussionRoutes := router.Group("/api/discussions")
{
    discussionRoutes.GET("/topics", discussionHandler.GetTopics)
    discussionRoutes.POST("/topics", discussionHandler.CreateTopic)
    discussionRoutes.GET("/topics/:id", discussionHandler.GetTopicByID)
    discussionRoutes.POST("/topics/:id/comments", discussionHandler.CreateComment)
}

// Auth routes
authRoutes := router.Group("/api/auth")
{
    authRoutes.POST("/register", authHandler.Register)
    authRoutes.POST("/login", authHandler.Login)
    authRoutes.POST("/refresh", authHandler.RefreshToken)
    authRoutes.POST("/logout", authHandler.Logout)
}
```

## Microservices

The application is divided into the following microservices:

### Auth Service (`cmd/auth-service/main.go`)

**Purpose**: Provides authentication and authorization functionality.

**Key Responsibilities**:
- User registration and login
- Session management
- JWT token generation and validation
- OAuth provider integration
- Two-factor authentication

### Content Service (`cmd/content-service/main.go`)

**Purpose**: Manages all book content and related features.

**Key Responsibilities**:
- Book content retrieval
- Content rendering and formatting
- Reading progress tracking
- Bookmarks and notes
- Interactive elements processing

### Discussion Service (`cmd/discussion-service/main.go`)

**Purpose**: Handles all forum and community-related features.

**Key Responsibilities**:
- Topic and comment management
- Moderation and flagging
- Category and tag systems
- Content linking to book sections
- Subscription management

### Payment Service (`cmd/payment-service/main.go`)

**Purpose**: Manages payments and subscriptions.

**Key Responsibilities**:
- Integration with Nigerian payment processors
- Subscription plan management
- Transaction history
- Receipt generation
- Refund processing

### Points Service (`cmd/points-service/main.go`)

**Purpose**: Handles the points-based reward system.

**Key Responsibilities**:
- Points awarding for various activities
- Leaderboard functionality
- Points history tracking
- Tier determination based on points
- Points redemption for rewards
