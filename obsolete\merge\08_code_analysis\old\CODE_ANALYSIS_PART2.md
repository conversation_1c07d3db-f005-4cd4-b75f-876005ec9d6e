# Great Nigeria Project - Code Analysis (Part 2)

## Content Management System

### Book Structure and Models (`internal/content/models/book.go`)

**Purpose**: Defines the data structures for book content.

**Key Models**:
- `Book`: Represents a complete book with metadata
   ```go
   type Book struct {
       ID          uint       `gorm:"primarykey" json:"id"`
       Title       string     `json:"title"`
       Subtitle    string     `json:"subtitle"`
       Author      string     `json:"author"`
       Description string     `json:"description"`
       CoverImage  string     `json:"cover_image"`
       Published   bool       `json:"published"`
       CreatedAt   time.Time  `json:"created_at"`
       UpdatedAt   time.Time  `json:"updated_at"`
       Chapters    []Chapter  `gorm:"foreignKey:BookID" json:"chapters,omitempty"`
   }
   ```

- `Chapter`: Represents a chapter within a book
   ```go
   type Chapter struct {
       ID          uint       `gorm:"primarykey" json:"id"`
       BookID      uint       `json:"book_id"`
       Title       string     `json:"title"`
       Number      int        `json:"number"`
       CreatedAt   time.Time  `json:"created_at"`
       UpdatedAt   time.Time  `json:"updated_at"`
       Sections    []Section  `gorm:"foreignKey:ChapterID" json:"sections,omitempty"`
   }
   ```

- `Section`: Represents a section within a chapter
   ```go
   type Section struct {
       ID          uint       `gorm:"primarykey" json:"id"`
       ChapterID   uint       `json:"chapter_id"`
       Title       string     `json:"title"`
       Number      int        `json:"number"`
       Content     string     `json:"content"`
       Format      string     `json:"format"`
       TimeToRead  int        `json:"time_to_read"`
       Published   bool       `json:"published"`
       CreatedAt   time.Time  `json:"created_at"`
       UpdatedAt   time.Time  `json:"updated_at"`
   }
   ```

### Book Repository (`internal/content/repository/book_repository.go`)

**Purpose**: Handles database operations for book content.

**Key Functions**:
- `GetBookByID`: Retrieves a specific book with chapters and sections
- `GetPublishedBooks`: Retrieves all published books
- `GetChapterByID`: Retrieves a specific chapter with its sections
- `GetSectionByID`: Retrieves a specific section content
- `UpdateSectionContent`: Updates content for a specific section

**Implementation Pattern**:
```go
func (r *BookRepository) GetBookByID(id uint) (*models.Book, error) {
    var book models.Book
    result := r.db.Preload("Chapters", func(db *gorm.DB) *gorm.DB {
        return db.Order("number ASC")
    }).Preload("Chapters.Sections", func(db *gorm.DB) *gorm.DB {
        return db.Order("number ASC")
    }).First(&book, id)
    
    if result.Error != nil {
        if errors.Is(result.Error, gorm.ErrRecordNotFound) {
            return nil, models.ErrBookNotFound
        }
        return nil, result.Error
    }
    
    return &book, nil
}
```

### Book Service (`internal/content/service/book_service.go`)

**Purpose**: Implements business logic for book-related operations.

**Key Functions**:
- `GetBookByID`: Retrieves and processes a book for client consumption
- `GetChapterByID`: Retrieves and processes a chapter
- `GetSectionByID`: Retrieves section content with processing
- `UserHasBookAccess`: Checks if a user has access to a specific book

**Implementation Pattern**:
```go
func (s *BookService) GetSectionByID(sectionID uint, userID uint) (*models.SectionResponse, error) {
    section, err := s.repo.GetSectionByID(sectionID)
    if err != nil {
        return nil, err
    }
    
    // Get chapter to check book access
    chapter, err := s.repo.GetChapterByID(section.ChapterID)
    if err != nil {
        return nil, err
    }
    
    // Check if user has access to this content
    hasAccess, err := s.UserHasBookAccess(userID, chapter.BookID)
    if err != nil || !hasAccess {
        return nil, models.ErrAccessDenied
    }
    
    // Process content
    renderContent := s.renderer.RenderMarkdown(section.Content)
    
    // Track reading progress if user is authenticated
    if userID > 0 {
        go s.progressRepo.TrackSectionRead(userID, sectionID)
    }
    
    return &models.SectionResponse{
        ID: section.ID,
        Title: section.Title,
        Content: renderContent,
        TimeToRead: section.TimeToRead,
    }, nil
}
```

### Content Renderer (`internal/content/service/content_renderer.go`)

**Purpose**: Transforms raw content into displayable format with interactive elements.

**Key Functions**:
- `RenderMarkdown`: Converts markdown to HTML
- `ProcessInteractiveElements`: Handles interactive components
- `ProcessMediaEmbeds`: Processes embedded media
- `ProcessTopicLinks`: Links content with discussion topics

**Implementation Pattern**:
```go
func (r *ContentRenderer) RenderMarkdown(content string) string {
    // Convert markdown to HTML
    htmlContent := blackfriday.Run([]byte(content))
    
    // Process interactive elements
    processed := r.processInteractiveElements(string(htmlContent))
    
    // Process media embeds
    processed = r.processMediaEmbeds(processed)
    
    // Process topic links
    processed = r.processTopicLinks(processed)
    
    return processed
}

func (r *ContentRenderer) processInteractiveElements(content string) string {
    // Find interactive element placeholders with regex
    re := regexp.MustCompile(`\{\{interactive:([a-z-]+):([0-9]+)\}\}`)
    matches := re.FindAllStringSubmatch(content, -1)
    
    for _, match := range matches {
        elementType := match[1]
        elementID, _ := strconv.Atoi(match[2])
        
        // Retrieve element from database
        element, err := r.interactiveRepo.GetElementByID(uint(elementID))
        if err != nil {
            continue
        }
        
        // Generate appropriate HTML based on element type
        htmlElement := ""
        switch elementType {
        case "quiz":
            htmlElement = r.generateQuizHTML(element)
        case "reflection":
            htmlElement = r.generateReflectionHTML(element)
        case "call-to-action":
            htmlElement = r.generateCTAHTML(element)
        }
        
        // Replace placeholder with actual element
        content = strings.Replace(content, match[0], htmlElement, 1)
    }
    
    return content
}
```

## Book Structure and Content

### Interactive Elements (`internal/content/models/interactive_element.go`)

**Purpose**: Defines and processes interactive components within book content.

**Key Components**:
- `Quiz`: Interactive quiz elements with questions and answers
- `Reflection`: Guided reflection exercises
- `CallToAction`: Prompts for user engagement and action
- `SurveyForm`: Data collection forms within content

**Implementation Pattern**:
```go
type InteractiveElement struct {
    ID           uint      `gorm:"primarykey" json:"id"`
    Type         string    `json:"type"` // quiz, reflection, cta, survey
    Title        string    `json:"title"`
    Description  string    `json:"description"`
    Content      string    `json:"content"` // JSON string with element-specific content
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}

func (s *InteractiveElementService) GenerateElementHTML(element *models.InteractiveElement) string {
    switch element.Type {
    case "quiz":
        return s.generateQuizHTML(element)
    case "reflection":
        return s.generateReflectionHTML(element)
    case "cta":
        return s.generateCTAHTML(element)
    case "survey":
        return s.generateSurveyHTML(element)
    default:
        return ""
    }
}
```

### Section Template Files (`section_template.go` & `section_template_ghosts.go`)

**Purpose**: Define templates for specific book sections and update them in the database.

**Key Features**:
- Structured content templates with standard sections
- Database connectivity
- Error handling and logging
- Content update functionality

**Implementation Pattern**:
```go
func main() {
    // Connect to database
    dbURL := os.Getenv("DATABASE_URL")
    if dbURL == "" {
        log.Fatal("DATABASE_URL environment variable not set")
    }

    db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    })
    if err != nil {
        log.Fatalf("Failed to connect to database: %v", err)
    }

    // Update section 49 (The Bleeding Giant)
    err = updateSection(db, 49, bleedingGiantUpdated)
    if err != nil {
        log.Fatalf("Failed to update section 49: %v", err)
    }

    fmt.Println("Content updated successfully!")
}

func updateSection(db *gorm.DB, sectionID uint, content string) error {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()

    result := db.WithContext(ctx).Model(&BookSection{}).Where("id = ?", sectionID).Update("content", content)
    if result.Error != nil {
        return result.Error
    }

    if result.RowsAffected == 0 {
        return fmt.Errorf("no rows updated for section ID %d", sectionID)
    }

    return nil
}
```

### Content Structure

The content follows a consistent structure:

```markdown
# 1.1 The Bleeding Giant

![Nigeria's Contrasts: A bustling Lagos skyline against the backdrop of struggling neighborhoods]

## Introduction
Nigeria stands as a land of extraordinary potential facing profound challenges...

## Chapter Quotes
> "A nation that neglects its youth, infrastructure, and institutions does not bleed money alone—it hemorrhages hope, talent, and its very future."
> — Samuel Chimezie Okechukwu, author and concerned citizen

## Poem: The Giant's Wound
*By Samuel Chimezie Okechukwu*

...

### Research Findings: Nigeria's Paradox
...

### VOICES FROM THE FIELD: The Weight of Frustration
...

### The Evidence of Decline
...

### REFLECTION POINT: The Weight of Witnessing Decline
...

### Beyond Data: The Human Dimension
...

### A CALL TO AWAKENING: Beyond Analysis to Action
...
```
