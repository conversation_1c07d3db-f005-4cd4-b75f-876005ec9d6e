# Great Nigeria Project - Code Analysis (Part 3)

## Discussion and Community Features

### `internal/discussion/handlers`

HTTP handlers for forum and discussion features:

#### `topic_handler.go`
- Creates, updates, lists, and deletes discussion topics
- Handles topic categorization and tagging
- Manages pinned/featured topics and view counts

```go
// GetTopics returns a list of discussion topics
func (h *TopicHandler) GetTopics(c *gin.Context) {
    categoryID := c.Query("category_id")
    page := c.<PERSON><PERSON>("page", "1")
    pageSize := c.<PERSON><PERSON><PERSON><PERSON>("page_size", "20")
    
    pageInt, _ := strconv.Atoi(page)
    pageSizeInt, _ := strconv.Atoi(pageSize)
    
    var categoryIDUint uint
    if categoryID != "" {
        categoryIDInt, _ := strconv.Atoi(categoryID)
        categoryIDUint = uint(categoryIDInt)
    }
    
    topics, total, err := h.topicService.GetTopics(categoryIDUint, pageInt, pageSizeInt)
    if err != nil {
        c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve topics"})
        return
    }
    
    c.<PERSON>(http.StatusOK, gin.H{
        "topics": topics,
        "total": total,
        "page": pageInt,
        "page_size": pageSizeInt,
    })
}
```

#### `comment_handler.go`
- Manages creation and retrieval of comments
- Implements threading and sorting logic
- Handles upvoting and reporting of comments

#### `flag_handler.go`
- Implements content moderation flagging system
- Processes user reports of inappropriate content
- Assigns moderation tasks and tracks status

#### `category_handler.go`
- Manages discussion categories and subcategories
- Controls category permissions and visibility
- Configures posting rules per category

### `internal/discussion/service`

Business logic for discussion features:

#### `topic_service.go`
- Implements business rules for topic creation and management
- Handles validation and filtering
- Integrates with the points system for rewards

```go
func (s *TopicService) CreateTopic(userID uint, input models.TopicInput) (*models.Topic, error) {
    // Validate input
    if input.Title == "" || input.Content == "" {
        return nil, errors.New("title and content are required")
    }
    
    // Check if category exists
    if input.CategoryID > 0 {
        exists, err := s.categoryRepo.CategoryExists(input.CategoryID)
        if err != nil {
            return nil, err
        }
        if !exists {
            return nil, errors.New("category not found")
        }
    }
    
    // Create topic
    topic := &models.Topic{
        UserID:      userID,
        CategoryID:  input.CategoryID,
        Title:       input.Title,
        Content:     input.Content,
        Tags:        input.Tags,
        CreatedAt:   time.Now(),
        UpdatedAt:   time.Now(),
    }
    
    // Save to database
    err := s.topicRepo.CreateTopic(topic)
    if err != nil {
        return nil, err
    }
    
    // Award points for creating a topic
    go s.pointsService.AwardPointsForTopicCreation(userID, topic.ID)
    
    return topic, nil
}
```

#### `moderation_service.go`
- Processes content flags and reports
- Implements automated moderation rules
- Manages user penalties and restrictions

## Points and Rewards System

### `internal/points/handlers`

HTTP handlers for the points system:

#### `discussion_points_handler.go`
- Awards points for creating topics
- Awards points for replies
- Grants points for receiving upvotes
- Distributes bonus points for featured content
- Provides configuration endpoints for point values

```go
// AwardPointsForComment awards points for creating a comment
func (h *DiscussionPointsHandler) AwardPointsForComment(c *gin.Context) {
    var input struct {
        UserID    uint `json:"user_id" binding:"required"`
        CommentID uint `json:"comment_id" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    points, err := h.pointsService.AwardPointsForComment(input.UserID, input.CommentID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award points"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "points_awarded": points,
        "user_id": input.UserID,
        "comment_id": input.CommentID,
    })
}
```

### `internal/points/service`

Business logic for points and rewards:

#### `points_service.go`
- Calculates points for various user actions
- Tracks point history and ledger
- Implements point thresholds for features
- Provides points-based content access control

```go
func (s *PointsService) AwardPointsForTopicCreation(userID uint, topicID uint) (int, error) {
    // Get point value for topic creation
    pointValue, err := s.configRepo.GetPointValue("topic_creation")
    if err != nil {
        return 0, err
    }
    
    // Check if points were already awarded for this topic
    exists, err := s.pointsRepo.PointsExistForAction(userID, "topic_creation", topicID)
    if err != nil {
        return 0, err
    }
    if exists {
        return 0, errors.New("points already awarded for this topic")
    }
    
    // Create points record
    pointsRecord := &models.PointsRecord{
        UserID:     userID,
        Points:     pointValue,
        ActionType: "topic_creation",
        ReferenceID: topicID,
        CreatedAt:  time.Now(),
    }
    
    // Save to database
    err = s.pointsRepo.CreatePointsRecord(pointsRecord)
    if err != nil {
        return 0, err
    }
    
    // Update user's total points
    err = s.userRepo.IncrementUserPoints(userID, pointValue)
    if err != nil {
        return 0, err
    }
    
    // Check if user has reached a new tier
    go s.checkAndUpdateUserTier(userID)
    
    return pointValue, nil
}
```

## Citation and Bibliography System

### `citation_tracker.go`

**Purpose**: Tracks, validates, and manages citations across all book content.

**Key Components**:

1. **CitationTracker Structure**:
   ```go
   type CitationTracker struct {
       db              *gorm.DB
       sourceMap       map[uint]*Source
       citationsByBook map[uint][]Citation
       logger          *log.Logger
   }
   ```
   - Maintains an in-memory cache of sources and citations organized by book

2. **Initialization**:
   ```go
   func NewCitationTracker(db *gorm.DB) (*CitationTracker, error) {
       tracker := &CitationTracker{
           db:              db,
           sourceMap:       make(map[uint]*Source),
           citationsByBook: make(map[uint][]Citation),
           logger:          log.New(os.Stdout, "[CitationTracker] ", log.LstdFlags),
       }
       
       // Load all sources and citations from database
       if err := tracker.LoadSources(); err != nil {
           return nil, err
       }
       
       if err := tracker.LoadAllCitations(); err != nil {
           return nil, err
       }
       
       return tracker, nil
   }
   ```
   - Loads all sources and citations into memory at startup

3. **Citation Verification**:
   ```go
   func (t *CitationTracker) VerifyCitationConsistency(bookID uint) ([]string, error) {
       var issues []string
       
       citations, ok := t.citationsByBook[bookID]
       if !ok {
           return nil, fmt.Errorf("no citations found for book ID %d", bookID)
       }
       
       // Check for missing sequential numbers
       numbers := make([]int, len(citations))
       for i, c := range citations {
           numbers[i] = c.Number
       }
       
       sort.Ints(numbers)
       
       for i := 1; i < len(numbers); i++ {
           if numbers[i] != numbers[i-1]+1 {
               issues = append(issues, fmt.Sprintf("Missing citation numbers between %d and %d", 
                   numbers[i-1], numbers[i]))
           }
       }
       
       // Check for duplicate numbers
       numCounts := make(map[int]int)
       for _, c := range citations {
           numCounts[c.Number]++
       }
       
       for num, count := range numCounts {
           if count > 1 {
               issues = append(issues, fmt.Sprintf("Citation number %d appears %d times", num, count))
           }
       }
       
       return issues, nil
   }
   ```
   - Ensures citations are numbered sequentially and consistently

4. **Bibliography Generation**:
   ```go
   func (t *CitationTracker) GenerateBibliography(bookID uint) (string, error) {
       citations, ok := t.citationsByBook[bookID]
       if !ok {
           return "", fmt.Errorf("no citations found for book ID %d", bookID)
       }
       
       // Group citations by source to avoid duplicates
       sourceIDs := make(map[uint]bool)
       for _, c := range citations {
           sourceIDs[c.SourceID] = true
       }
       
       var bibliography strings.Builder
       bibliography.WriteString("# Bibliography\n\n")
       
       // Generate formatted bibliography entries
       for sourceID := range sourceIDs {
           source, ok := t.sourceMap[sourceID]
           if !ok {
               continue
           }
           
           entry := t.formatBibliographyEntry(source)
           bibliography.WriteString(entry)
           bibliography.WriteString("\n\n")
       }
       
       return bibliography.String(), nil
   }
   ```
   - Generates a formatted bibliography from citation sources

### `generate_bibliography.go`

**Purpose**: Command-line utility to generate bibliography sections for book appendices.

**Key Components**:

1. **Main Function**:
   ```go
   func main() {
       // Connect to database
       dbURL := os.Getenv("DATABASE_URL")
       if dbURL == "" {
           log.Fatal("DATABASE_URL environment variable not set")
       }
       
       db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{
           Logger: logger.Default.LogMode(logger.Info),
       })
       if err != nil {
           log.Fatalf("Failed to connect to database: %v", err)
       }
       
       // Initialize citation tracker
       tracker, err := NewCitationTracker(db)
       if err != nil {
           log.Fatalf("Failed to initialize citation tracker: %v", err)
       }
       
       // Generate bibliography for each book
       for bookID := uint(1); bookID <= 3; bookID++ {
           bibliography, err := tracker.GenerateBibliography(bookID)
           if err != nil {
               log.Printf("Warning: %v", err)
               continue
           }
           
           // Update bibliography in database
           if err := updateBibliography(db, bookID, bibliography); err != nil {
               log.Printf("Failed to update bibliography for book %d: %v", bookID, err)
           } else {
               log.Printf("Bibliography updated successfully for book %d", bookID)
           }
       }
   }
   ```
   - Generates and updates bibliography sections for all three books
