# Great Nigeria Project - Code Analysis (Part 5)

This document extends the code analysis to cover additional modules and components that were not thoroughly documented in the previous parts.

## Table of Contents

1. [Celebration Module](#celebration-module)
2. [Gifts Module](#gifts-module)
3. [Project Module](#project-module)
4. [Report Module](#report-module)
5. [Resource Module](#resource-module)
6. [Template Module](#template-module)
7. [Web Components](#web-components)
8. [Frontend-Backend Integration](#frontend-backend-integration)

## Celebration Module

The Celebration module (`internal/celebration`) implements the "Celebrate Nigeria" feature, which showcases Nigerian excellence across various domains.

### Database Structure (`internal/celebration/database/driver.go`)

```go
// Driver provides database connectivity for the celebration module
type Driver struct {
    DB *gorm.DB
}

// NewDriver creates a new database driver instance
func NewDriver(db *gorm.DB) *Driver {
    return &Driver{
        DB: db,
    }
}

// RunMigrations executes all database migrations for the celebration module
func (d *Driver) RunMigrations() error {
    migrator := migrations.NewMigrator(d.DB)
    return migrator.RunMigrations()
}
```

This component:
- Provides a dedicated database connection for the celebration module
- Manages database migrations specific to celebration features
- Isolates database operations from business logic

### Migrations (`internal/celebration/migrations/`)

The migrations directory contains database schema definitions:

```go
// 001_create_celebration_tables.go
func CreateCelebrationTables() *gorm.DB {
    return func(tx *gorm.DB) error {
        // Create categories table
        if err := tx.AutoMigrate(&models.Category{}); err != nil {
            return err
        }
        
        // Create entries table
        if err := tx.AutoMigrate(&models.Entry{}); err != nil {
            return err
        }
        
        // Create nominations table
        if err := tx.AutoMigrate(&models.Nomination{}); err != nil {
            return err
        }
        
        return nil
    }
}
```

The migration system:
- Creates tables for categories, entries, and nominations
- Establishes relationships between tables
- Provides versioned schema changes

### Models (`internal/celebration/models/models.go`)

```go
// Category represents a classification for celebration entries
type Category struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Slug        string    `json:"slug"`
    ParentID    *uint     `json:"parent_id"`
    ImageURL    string    `json:"image_url"`
    DisplayOrder int       `json:"display_order"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Parent      *Category  `gorm:"foreignKey:ParentID" json:"parent,omitempty"`
    Children    []Category `gorm:"foreignKey:ParentID" json:"children,omitempty"`
    Entries     []Entry    `gorm:"foreignKey:CategoryID" json:"entries,omitempty"`
}

// Entry represents a celebration item showcasing Nigerian excellence
type Entry struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    Content     string    `json:"content"`
    CategoryID  uint      `json:"category_id"`
    Year        int       `json:"year"`
    Location    string    `json:"location"`
    ImageURL    string    `json:"image_url"`
    SourceURL   string    `json:"source_url"`
    Featured    bool      `json:"featured"`
    Verified    bool      `json:"verified"`
    SubmitterID uint      `json:"submitter_id"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Category    Category  `gorm:"foreignKey:CategoryID" json:"category,omitempty"`
}

// Nomination represents a user-submitted celebration entry pending verification
type Nomination struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    Content     string    `json:"content"`
    CategoryID  uint      `json:"category_id"`
    Year        int       `json:"year"`
    Location    string    `json:"location"`
    ImageURL    string    `json:"image_url"`
    SourceURL   string    `json:"source_url"`
    UserID      uint      `json:"user_id"`
    Status      string    `json:"status"` // pending, approved, rejected
    ReviewerID  *uint     `json:"reviewer_id"`
    ReviewNotes string    `json:"review_notes"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

The model structure:
- Defines hierarchical categories with parent-child relationships
- Supports verified entries and user-submitted nominations
- Includes metadata like location, year, and sources
- Implements moderation workflow for community contributions

### Repository (`internal/celebration/repository/repository.go`)

```go
// Repository handles data access for the celebration module
type Repository struct {
    db *gorm.DB
}

// NewRepository creates a new repository instance
func NewRepository(db *gorm.DB) *Repository {
    return &Repository{
        db: db,
    }
}

// GetCategories retrieves categories with optional parent filtering
func (r *Repository) GetCategories(parentID *uint) ([]models.Category, error) {
    var categories []models.Category
    query := r.db.Order("display_order ASC")
    
    if parentID != nil {
        query = query.Where("parent_id = ?", parentID)
    } else {
        query = query.Where("parent_id IS NULL")
    }
    
    if err := query.Find(&categories).Error; err != nil {
        return nil, err
    }
    
    return categories, nil
}

// GetEntries retrieves celebration entries with filtering options
func (r *Repository) GetEntries(categoryID *uint, featured bool, page, pageSize int) ([]models.Entry, int64, error) {
    var entries []models.Entry
    var total int64
    
    query := r.db.Model(&models.Entry{}).Where("verified = ?", true)
    
    if categoryID != nil {
        query = query.Where("category_id = ?", categoryID)
    }
    
    if featured {
        query = query.Where("featured = ?", true)
    }
    
    // Count total entries
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&entries).Error; err != nil {
        return nil, 0, err
    }
    
    return entries, total, nil
}

// Additional repository methods for nominations, verification, etc.
```

The repository layer:
- Implements data access patterns for categories and entries
- Supports filtering, pagination, and sorting
- Handles verification status and featured content
- Manages the nomination workflow

### Service (`internal/celebration/service/service.go`)

```go
// Service implements business logic for the celebration module
type Service struct {
    repo *repository.Repository
}

// NewService creates a new service instance
func NewService(repo *repository.Repository) *Service {
    return &Service{
        repo: repo,
    }
}

// GetCategoryTree retrieves the full category hierarchy
func (s *Service) GetCategoryTree() ([]models.Category, error) {
    // Get root categories
    rootCategories, err := s.repo.GetCategories(nil)
    if err != nil {
        return nil, err
    }
    
    // Recursively load children for each category
    for i := range rootCategories {
        if err := s.loadCategoryChildren(&rootCategories[i]); err != nil {
            return nil, err
        }
    }
    
    return rootCategories, nil
}

// LoadCategoryChildren recursively loads child categories
func (s *Service) loadCategoryChildren(category *models.Category) error {
    children, err := s.repo.GetCategories(&category.ID)
    if err != nil {
        return err
    }
    
    category.Children = children
    
    for i := range category.Children {
        if err := s.loadCategoryChildren(&category.Children[i]); err != nil {
            return err
        }
    }
    
    return nil
}

// GetFeaturedEntries retrieves featured entries across categories
func (s *Service) GetFeaturedEntries(limit int) ([]models.Entry, error) {
    return s.repo.GetFeaturedEntries(limit)
}

// SubmitNomination processes a user-submitted nomination
func (s *Service) SubmitNomination(nomination *models.Nomination) error {
    // Validate nomination
    if nomination.Title == "" || nomination.Description == "" {
        return errors.New("title and description are required")
    }
    
    // Set initial status
    nomination.Status = "pending"
    
    // Save to database
    return s.repo.CreateNomination(nomination)
}

// Additional service methods for moderation, approval, etc.
```

The service layer:
- Implements business logic for category hierarchy
- Manages featured content selection
- Processes user nominations with validation
- Handles moderation workflow

### Handlers (`internal/celebration/handlers/handlers.go`)

```go
// Handler processes HTTP requests for the celebration module
type Handler struct {
    service *service.Service
}

// NewHandler creates a new handler instance
func NewHandler(service *service.Service) *Handler {
    return &Handler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the celebration module
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
    celebrateGroup := router.Group("/celebrate")
    {
        celebrateGroup.GET("/categories", h.GetCategories)
        celebrateGroup.GET("/categories/:id", h.GetCategoryByID)
        celebrateGroup.GET("/entries", h.GetEntries)
        celebrateGroup.GET("/entries/:id", h.GetEntryByID)
        celebrateGroup.GET("/featured", h.GetFeaturedEntries)
        
        // Protected routes
        authorized := celebrateGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.POST("/nominations", h.SubmitNomination)
            authorized.GET("/nominations", h.GetUserNominations)
        }
        
        // Admin routes
        admin := celebrateGroup.Group("/admin")
        admin.Use(middleware.AdminRequired())
        {
            admin.GET("/nominations", h.GetPendingNominations)
            admin.PUT("/nominations/:id/approve", h.ApproveNomination)
            admin.PUT("/nominations/:id/reject", h.RejectNomination)
            admin.POST("/categories", h.CreateCategory)
            admin.PUT("/categories/:id", h.UpdateCategory)
            admin.POST("/entries", h.CreateEntry)
            admin.PUT("/entries/:id", h.UpdateEntry)
            admin.DELETE("/entries/:id", h.DeleteEntry)
        }
    }
}

// GetCategories returns all categories
func (h *Handler) GetCategories(c *gin.Context) {
    hierarchical := c.DefaultQuery("hierarchical", "false") == "true"
    
    var categories []models.Category
    var err error
    
    if hierarchical {
        categories, err = h.service.GetCategoryTree()
    } else {
        parentID := c.Query("parent_id")
        var parentIDPtr *uint
        
        if parentID != "" {
            id, err := strconv.ParseUint(parentID, 10, 32)
            if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid parent_id"})
                return
            }
            idUint := uint(id)
            parentIDPtr = &idUint
        }
        
        categories, err = h.service.GetCategories(parentIDPtr)
    }
    
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve categories"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"categories": categories})
}

// Additional handler methods for entries, nominations, etc.
```

The handler layer:
- Defines API routes for the celebration module
- Implements request processing and response formatting
- Manages authentication and authorization
- Provides admin functionality for content management
