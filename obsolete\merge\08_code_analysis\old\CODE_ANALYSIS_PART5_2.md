# Great Nigeria Project - Code Analysis (Part 5.2)

## Gifts Module

The Gifts module (`internal/gifts`) implements the Nigerian Virtual Gifts system, allowing users to send culturally authentic virtual gifts to each other.

### Models (`internal/gifts/models`)

```go
// GiftCategory represents a classification for virtual gifts
type GiftCategory struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    DisplayOrder int      `json:"display_order"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Gifts       []Gift    `gorm:"foreignKey:CategoryID" json:"gifts,omitempty"`
}

// Gift represents a virtual gift item
type Gift struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    CategoryID  uint      `json:"category_id"`
    ImageURL    string    `json:"image_url"`
    AnimationURL string   `json:"animation_url"`
    SoundURL    string    `json:"sound_url"`
    Price       int       `json:"price"` // In points
    CoinPrice   int       `json:"coin_price"` // In virtual coins
    IsActive    bool      `json:"is_active"`
    IsPremium   bool      `json:"is_premium"`
    DisplayOrder int      `json:"display_order"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Category    GiftCategory `gorm:"foreignKey:CategoryID" json:"category,omitempty"`
}

// GiftTransaction records gift exchanges between users
type GiftTransaction struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    SenderID    uint      `json:"sender_id"`
    RecipientID uint      `json:"recipient_id"`
    GiftID      uint      `json:"gift_id"`
    Message     string    `json:"message"`
    PointsCost  int       `json:"points_cost"`
    CoinsCost   int       `json:"coins_cost"`
    PaymentType string    `json:"payment_type"` // "points" or "coins"
    IsAnonymous bool      `json:"is_anonymous"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    Gift        Gift      `gorm:"foreignKey:GiftID" json:"gift,omitempty"`
    Sender      User      `gorm:"foreignKey:SenderID" json:"sender,omitempty"`
    Recipient   User      `gorm:"foreignKey:RecipientID" json:"recipient,omitempty"`
}

// GiftRevenue tracks revenue sharing for gift transactions
type GiftRevenue struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    TransactionID   uint      `json:"transaction_id"`
    RecipientAmount int       `json:"recipient_amount"` // 50% to recipient
    PlatformAmount  int       `json:"platform_amount"`  // 50% to platform
    ProcessedAt     time.Time `json:"processed_at"`
    
    // Relationships
    Transaction     GiftTransaction `gorm:"foreignKey:TransactionID" json:"transaction,omitempty"`
}

// GiftNotificationSettings stores user preferences for gift notifications
type GiftNotificationSettings struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    UserID          uint      `json:"user_id"`
    ReceiveNotifications bool  `json:"receive_notifications"`
    ShowAnimation   bool      `json:"show_animation"`
    PlaySound       bool      `json:"play_sound"`
    EmailNotification bool    `json:"email_notification"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
}
```

The model structure:
- Defines gift categories and individual gift items
- Tracks gift transactions between users
- Implements revenue sharing between recipients and platform
- Manages notification preferences for gift recipients

### Repository (`internal/gifts/repository`)

```go
// GiftRepository handles data access for gifts
type GiftRepository struct {
    db *gorm.DB
}

// NewGiftRepository creates a new repository instance
func NewGiftRepository(db *gorm.DB) *GiftRepository {
    return &GiftRepository{
        db: db,
    }
}

// GetGiftCategories retrieves all gift categories
func (r *GiftRepository) GetGiftCategories() ([]models.GiftCategory, error) {
    var categories []models.GiftCategory
    
    if err := r.db.Order("display_order ASC").Find(&categories).Error; err != nil {
        return nil, err
    }
    
    return categories, nil
}

// GetGiftsByCategory retrieves gifts for a specific category
func (r *GiftRepository) GetGiftsByCategory(categoryID uint) ([]models.Gift, error) {
    var gifts []models.Gift
    
    if err := r.db.Where("category_id = ? AND is_active = ?", categoryID, true).
        Order("display_order ASC").Find(&gifts).Error; err != nil {
        return nil, err
    }
    
    return gifts, nil
}

// CreateGiftTransaction records a new gift transaction
func (r *GiftRepository) CreateGiftTransaction(transaction *models.GiftTransaction) error {
    return r.db.Create(transaction).Error
}

// GetUserReceivedGifts retrieves gifts received by a user
func (r *GiftRepository) GetUserReceivedGifts(userID uint, page, pageSize int) ([]models.GiftTransaction, int64, error) {
    var transactions []models.GiftTransaction
    var total int64
    
    query := r.db.Model(&models.GiftTransaction{}).
        Where("recipient_id = ?", userID).
        Preload("Gift").
        Preload("Sender", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, profile_image")
        })
    
    // Count total
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).
        Order("created_at DESC").Find(&transactions).Error; err != nil {
        return nil, 0, err
    }
    
    return transactions, total, nil
}

// Additional repository methods for leaderboards, revenue tracking, etc.
```

The repository layer:
- Implements data access for gift categories and items
- Manages gift transactions and revenue sharing
- Supports user gift history and leaderboards
- Handles notification preferences

### Service (`internal/gifts/service`)

```go
// GiftService implements business logic for the gifts module
type GiftService struct {
    repo        *repository.GiftRepository
    userRepo    *repository.UserRepository
    pointsClient points.PointsServiceClient
}

// NewGiftService creates a new service instance
func NewGiftService(repo *repository.GiftRepository, userRepo *repository.UserRepository, pointsClient points.PointsServiceClient) *GiftService {
    return &GiftService{
        repo:        repo,
        userRepo:    userRepo,
        pointsClient: pointsClient,
    }
}

// GetGiftCatalog retrieves the full gift catalog with categories
func (s *GiftService) GetGiftCatalog() ([]models.GiftCategoryWithGifts, error) {
    // Get all categories
    categories, err := s.repo.GetGiftCategories()
    if err != nil {
        return nil, err
    }
    
    // Build response with gifts for each category
    result := make([]models.GiftCategoryWithGifts, len(categories))
    for i, category := range categories {
        gifts, err := s.repo.GetGiftsByCategory(category.ID)
        if err != nil {
            return nil, err
        }
        
        result[i] = models.GiftCategoryWithGifts{
            ID:          category.ID,
            Name:        category.Name,
            Description: category.Description,
            Gifts:       gifts,
        }
    }
    
    return result, nil
}

// SendGift processes a gift transaction
func (s *GiftService) SendGift(senderID, recipientID, giftID uint, message string, isAnonymous bool, paymentType string) (*models.GiftTransaction, error) {
    // Validate recipient exists
    recipient, err := s.userRepo.GetUserByID(recipientID)
    if err != nil {
        return nil, err
    }
    if recipient == nil {
        return nil, errors.New("recipient not found")
    }
    
    // Get gift details
    gift, err := s.repo.GetGiftByID(giftID)
    if err != nil {
        return nil, err
    }
    if gift == nil {
        return nil, errors.New("gift not found")
    }
    if !gift.IsActive {
        return nil, errors.New("gift is not available")
    }
    
    // Determine cost based on payment type
    var pointsCost, coinsCost int
    if paymentType == "points" {
        pointsCost = gift.Price
        coinsCost = 0
        
        // Verify sender has enough points
        hasPoints, err := s.pointsClient.CheckUserHasPoints(context.Background(), &points.CheckPointsRequest{
            UserId: senderID,
            Points: int32(pointsCost),
        })
        if err != nil {
            return nil, err
        }
        if !hasPoints.HasEnough {
            return nil, errors.New("insufficient points")
        }
        
        // Deduct points
        _, err = s.pointsClient.DeductPoints(context.Background(), &points.DeductPointsRequest{
            UserId: senderID,
            Points: int32(pointsCost),
            Reason: fmt.Sprintf("Gift: %s", gift.Name),
        })
        if err != nil {
            return nil, err
        }
    } else if paymentType == "coins" {
        pointsCost = 0
        coinsCost = gift.CoinPrice
        
        // Verify sender has enough coins
        hasCoins, err := s.userRepo.CheckUserHasCoins(senderID, coinsCost)
        if err != nil {
            return nil, err
        }
        if !hasCoins {
            return nil, errors.New("insufficient coins")
        }
        
        // Deduct coins
        err = s.userRepo.DeductUserCoins(senderID, coinsCost)
        if err != nil {
            return nil, err
        }
    } else {
        return nil, errors.New("invalid payment type")
    }
    
    // Create transaction
    transaction := &models.GiftTransaction{
        SenderID:    senderID,
        RecipientID: recipientID,
        GiftID:      giftID,
        Message:     message,
        PointsCost:  pointsCost,
        CoinsCost:   coinsCost,
        PaymentType: paymentType,
        IsAnonymous: isAnonymous,
        CreatedAt:   time.Now(),
    }
    
    // Save transaction
    if err := s.repo.CreateGiftTransaction(transaction); err != nil {
        return nil, err
    }
    
    // Process revenue sharing (50% to recipient)
    go s.processRevenueSharing(transaction)
    
    // Send notification to recipient
    go s.sendGiftNotification(transaction)
    
    return transaction, nil
}

// Additional service methods for analytics, leaderboards, etc.
```

The service layer:
- Implements business logic for gift catalog management
- Processes gift transactions with payment validation
- Handles revenue sharing between recipients and platform
- Manages notifications and analytics

### Handlers (`internal/gifts/handlers`)

```go
// GiftHandler processes HTTP requests for the gifts module
type GiftHandler struct {
    service *service.GiftService
}

// NewGiftHandler creates a new handler instance
func NewGiftHandler(service *service.GiftService) *GiftHandler {
    return &GiftHandler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the gifts module
func (h *GiftHandler) RegisterRoutes(router *gin.RouterGroup) {
    giftsGroup := router.Group("/gifts")
    {
        giftsGroup.GET("/catalog", h.GetGiftCatalog)
        giftsGroup.GET("/categories", h.GetGiftCategories)
        giftsGroup.GET("/categories/:id", h.GetGiftsByCategory)
        
        // Protected routes
        authorized := giftsGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.POST("/send", h.SendGift)
            authorized.GET("/received", h.GetReceivedGifts)
            authorized.GET("/sent", h.GetSentGifts)
            authorized.GET("/leaderboard", h.GetGiftLeaderboard)
            authorized.GET("/settings", h.GetNotificationSettings)
            authorized.PUT("/settings", h.UpdateNotificationSettings)
        }
        
        // Admin routes
        admin := giftsGroup.Group("/admin")
        admin.Use(middleware.AdminRequired())
        {
            admin.POST("/categories", h.CreateGiftCategory)
            admin.PUT("/categories/:id", h.UpdateGiftCategory)
            admin.POST("/gifts", h.CreateGift)
            admin.PUT("/gifts/:id", h.UpdateGift)
            admin.GET("/analytics", h.GetGiftAnalytics)
            admin.GET("/revenue", h.GetRevenueReport)
        }
    }
}

// GetGiftCatalog returns the full gift catalog
func (h *GiftHandler) GetGiftCatalog(c *gin.Context) {
    catalog, err := h.service.GetGiftCatalog()
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve gift catalog"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"catalog": catalog})
}

// SendGift processes a gift transaction
func (h *GiftHandler) SendGift(c *gin.Context) {
    var input struct {
        RecipientID uint   `json:"recipient_id" binding:"required"`
        GiftID      uint   `json:"gift_id" binding:"required"`
        Message     string `json:"message"`
        IsAnonymous bool   `json:"is_anonymous"`
        PaymentType string `json:"payment_type" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get sender ID from authenticated user
    userID := middleware.GetUserID(c)
    
    transaction, err := h.service.SendGift(userID, input.RecipientID, input.GiftID, 
        input.Message, input.IsAnonymous, input.PaymentType)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Gift sent successfully",
        "transaction": transaction,
    })
}

// Additional handler methods for gift management, analytics, etc.
```

The handler layer:
- Defines API routes for the gifts module
- Processes gift transactions and catalog requests
- Manages authentication and authorization
- Provides admin functionality for gift management and analytics
