# Great Nigeria Project - Code Analysis (Part 5.3)

## Project Module

The Project module (`internal/project`) enables users to create and collaborate on community implementation projects based on book initiatives.

### Models (`internal/project/models`)

```go
// Project represents a community implementation initiative
type Project struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    BookID      *uint     `json:"book_id"`
    ChapterID   *uint     `json:"chapter_id"`
    SectionID   *uint     `json:"section_id"`
    CreatorID   uint      `json:"creator_id"`
    Status      string    `json:"status"` // planning, active, completed, archived
    StartDate   time.Time `json:"start_date"`
    EndDate     *time.Time `json:"end_date"`
    ImageURL    string    `json:"image_url"`
    Location    string    `json:"location"`
    IsVirtual   bool      `json:"is_virtual"`
    IsPublic    bool      `json:"is_public"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Creator     User      `gorm:"foreignKey:CreatorID" json:"creator,omitempty"`
    Members     []ProjectMember `gorm:"foreignKey:ProjectID" json:"members,omitempty"`
    Tasks       []ProjectTask `gorm:"foreignKey:ProjectID" json:"tasks,omitempty"`
    Updates     []ProjectUpdate `gorm:"foreignKey:ProjectID" json:"updates,omitempty"`
    Resources   []ProjectResource `gorm:"foreignKey:ProjectID" json:"resources,omitempty"`
}

// ProjectMember represents a user participating in a project
type ProjectMember struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ProjectID   uint      `json:"project_id"`
    UserID      uint      `json:"user_id"`
    Role        string    `json:"role"` // owner, admin, member
    JoinedAt    time.Time `json:"joined_at"`
    
    // Relationships
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// ProjectTask represents a task within a project
type ProjectTask struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ProjectID   uint      `json:"project_id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    Status      string    `json:"status"` // pending, in_progress, completed
    Priority    string    `json:"priority"` // low, medium, high
    AssigneeID  *uint     `json:"assignee_id"`
    DueDate     *time.Time `json:"due_date"`
    CompletedAt *time.Time `json:"completed_at"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Assignee    *User     `gorm:"foreignKey:AssigneeID" json:"assignee,omitempty"`
}

// ProjectUpdate represents a progress update for a project
type ProjectUpdate struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ProjectID   uint      `json:"project_id"`
    UserID      uint      `json:"user_id"`
    Content     string    `json:"content"`
    MediaURLs   pq.StringArray `gorm:"type:text[]" json:"media_urls"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// ProjectResource represents a resource shared within a project
type ProjectResource struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ProjectID   uint      `json:"project_id"`
    UserID      uint      `json:"user_id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    Type        string    `json:"type"` // link, document, image, video
    URL         string    `json:"url"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}
```

The model structure:
- Defines projects with metadata and book references
- Tracks project membership with role-based permissions
- Manages tasks with assignment and status tracking
- Supports progress updates and resource sharing

### Repository (`internal/project/repository`)

```go
// ProjectRepository handles data access for projects
type ProjectRepository struct {
    db *gorm.DB
}

// NewProjectRepository creates a new repository instance
func NewProjectRepository(db *gorm.DB) *ProjectRepository {
    return &ProjectRepository{
        db: db,
    }
}

// GetProjects retrieves projects with filtering options
func (r *ProjectRepository) GetProjects(filters map[string]interface{}, page, pageSize int) ([]models.Project, int64, error) {
    var projects []models.Project
    var total int64
    
    query := r.db.Model(&models.Project{})
    
    // Apply filters
    for key, value := range filters {
        query = query.Where(key, value)
    }
    
    // Count total
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).
        Preload("Creator", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Order("created_at DESC").Find(&projects).Error; err != nil {
        return nil, 0, err
    }
    
    return projects, total, nil
}

// GetProjectByID retrieves a project by ID with preloaded relationships
func (r *ProjectRepository) GetProjectByID(id uint) (*models.Project, error) {
    var project models.Project
    
    err := r.db.Preload("Creator").
        Preload("Members.User", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Tasks", func(db *gorm.DB) *gorm.DB {
            return db.Order("priority DESC, due_date ASC")
        }).
        Preload("Tasks.Assignee", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Updates", func(db *gorm.DB) *gorm.DB {
            return db.Order("created_at DESC")
        }).
        Preload("Updates.User").
        Preload("Resources").
        First(&project, id).Error
    
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil
        }
        return nil, err
    }
    
    return &project, nil
}

// CreateProject creates a new project
func (r *ProjectRepository) CreateProject(project *models.Project) error {
    return r.db.Transaction(func(tx *gorm.DB) error {
        // Create project
        if err := tx.Create(project).Error; err != nil {
            return err
        }
        
        // Add creator as owner
        member := models.ProjectMember{
            ProjectID: project.ID,
            UserID:    project.CreatorID,
            Role:      "owner",
            JoinedAt:  time.Now(),
        }
        
        return tx.Create(&member).Error
    })
}

// Additional repository methods for tasks, members, updates, etc.
```

The repository layer:
- Implements data access for projects and related entities
- Supports filtering and pagination for project listings
- Manages relationships between projects, members, tasks, and resources
- Uses transactions for data consistency

### Service (`internal/project/service`)

```go
// ProjectService implements business logic for the project module
type ProjectService struct {
    repo        *repository.ProjectRepository
    userRepo    *repository.UserRepository
    pointsClient points.PointsServiceClient
}

// NewProjectService creates a new service instance
func NewProjectService(repo *repository.ProjectRepository, userRepo *repository.UserRepository, pointsClient points.PointsServiceClient) *ProjectService {
    return &ProjectService{
        repo:        repo,
        userRepo:    userRepo,
        pointsClient: pointsClient,
    }
}

// GetPublicProjects retrieves public projects with pagination
func (s *ProjectService) GetPublicProjects(page, pageSize int) ([]models.Project, int64, error) {
    filters := map[string]interface{}{
        "is_public": true,
    }
    return s.repo.GetProjects(filters, page, pageSize)
}

// GetUserProjects retrieves projects for a specific user
func (s *ProjectService) GetUserProjects(userID uint, page, pageSize int) ([]models.Project, int64, error) {
    // Get projects where user is a member
    memberProjects, err := s.repo.GetProjectsByMember(userID)
    if err != nil {
        return nil, 0, err
    }
    
    // Extract project IDs
    var projectIDs []uint
    for _, project := range memberProjects {
        projectIDs = append(projectIDs, project.ID)
    }
    
    // If user is not a member of any projects, return empty result
    if len(projectIDs) == 0 {
        return []models.Project{}, 0, nil
    }
    
    // Get full project details with pagination
    filters := map[string]interface{}{
        "id": projectIDs,
    }
    return s.repo.GetProjects(filters, page, pageSize)
}

// CreateProject creates a new project and awards points
func (s *ProjectService) CreateProject(project *models.Project) (*models.Project, error) {
    // Validate project data
    if project.Title == "" {
        return nil, errors.New("project title is required")
    }
    
    // Set default values
    project.Status = "planning"
    project.CreatedAt = time.Now()
    project.UpdatedAt = time.Now()
    
    // Create project
    if err := s.repo.CreateProject(project); err != nil {
        return nil, err
    }
    
    // Award points for creating a project
    go func() {
        _, err := s.pointsClient.AwardPoints(context.Background(), &points.AwardPointsRequest{
            UserId: project.CreatorID,
            Points: 50,
            Reason: "Created a new community project",
        })
        if err != nil {
            log.Printf("Failed to award points for project creation: %v", err)
        }
    }()
    
    return project, nil
}

// JoinProject adds a user to a project
func (s *ProjectService) JoinProject(projectID, userID uint) error {
    // Check if project exists
    project, err := s.repo.GetProjectByID(projectID)
    if err != nil {
        return err
    }
    if project == nil {
        return errors.New("project not found")
    }
    
    // Check if user is already a member
    isMember, err := s.repo.IsUserProjectMember(projectID, userID)
    if err != nil {
        return err
    }
    if isMember {
        return errors.New("user is already a member of this project")
    }
    
    // Add user as member
    member := models.ProjectMember{
        ProjectID: projectID,
        UserID:    userID,
        Role:      "member",
        JoinedAt:  time.Now(),
    }
    
    if err := s.repo.CreateProjectMember(&member); err != nil {
        return err
    }
    
    // Award points for joining a project
    go func() {
        _, err := s.pointsClient.AwardPoints(context.Background(), &points.AwardPointsRequest{
            UserId: userID,
            Points: 10,
            Reason: "Joined a community project",
        })
        if err != nil {
            log.Printf("Failed to award points for joining project: %v", err)
        }
    }()
    
    return nil
}

// Additional service methods for tasks, updates, resources, etc.
```

The service layer:
- Implements business logic for project management
- Integrates with the points system for rewards
- Manages project membership and permissions
- Handles task assignment and progress tracking

### Handlers (`internal/project/handlers`)

```go
// ProjectHandler processes HTTP requests for the project module
type ProjectHandler struct {
    service *service.ProjectService
}

// NewProjectHandler creates a new handler instance
func NewProjectHandler(service *service.ProjectService) *ProjectHandler {
    return &ProjectHandler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the project module
func (h *ProjectHandler) RegisterRoutes(router *gin.RouterGroup) {
    projectsGroup := router.Group("/projects")
    {
        projectsGroup.GET("/public", h.GetPublicProjects)
        projectsGroup.GET("/:id", h.GetProjectByID)
        
        // Protected routes
        authorized := projectsGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.GET("/", h.GetUserProjects)
            authorized.POST("/", h.CreateProject)
            authorized.PUT("/:id", h.UpdateProject)
            authorized.POST("/:id/join", h.JoinProject)
            authorized.POST("/:id/leave", h.LeaveProject)
            authorized.POST("/:id/tasks", h.CreateTask)
            authorized.PUT("/:id/tasks/:taskId", h.UpdateTask)
            authorized.POST("/:id/updates", h.AddUpdate)
            authorized.POST("/:id/resources", h.AddResource)
            authorized.GET("/:id/members", h.GetProjectMembers)
            authorized.PUT("/:id/members/:userId", h.UpdateMemberRole)
        }
    }
}

// GetPublicProjects returns public projects with pagination
func (h *ProjectHandler) GetPublicProjects(c *gin.Context) {
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
    
    projects, total, err := h.service.GetPublicProjects(page, pageSize)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve projects"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "projects": projects,
        "pagination": gin.H{
            "total":      total,
            "page":       page,
            "page_size":  pageSize,
            "total_pages": int(math.Ceil(float64(total) / float64(pageSize))),
        },
    })
}

// CreateProject handles project creation
func (h *ProjectHandler) CreateProject(c *gin.Context) {
    var input models.Project
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Set creator ID from authenticated user
    userID := middleware.GetUserID(c)
    input.CreatorID = userID
    
    project, err := h.service.CreateProject(&input)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{
        "message": "Project created successfully",
        "project": project,
    })
}

// Additional handler methods for project management, tasks, updates, etc.
```

The handler layer:
- Defines API routes for the project module
- Processes project creation and management requests
- Handles task assignment and progress updates
- Manages project membership and permissions
