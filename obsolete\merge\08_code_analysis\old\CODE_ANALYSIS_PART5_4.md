# Great Nigeria Project - Code Analysis (Part 5.4)

## Report Module

The Report module (`internal/report`) handles content reporting and moderation for the platform.

### Models (`internal/report/models`)

```go
// ReportType defines the type of content being reported
type ReportType string

const (
    ReportTypeDiscussion ReportType = "discussion"
    ReportTypeComment    ReportType = "comment"
    ReportTypeUser       ReportType = "user"
    ReportTypeProject    ReportType = "project"
    ReportTypeResource   ReportType = "resource"
)

// ReportReason defines the reason for reporting content
type ReportReason string

const (
    ReportReasonSpam             ReportReason = "spam"
    ReportReasonHarassment       ReportReason = "harassment"
    ReportReasonInappropriate    ReportReason = "inappropriate"
    ReportReasonViolation        ReportReason = "violation"
    ReportReasonOther            ReportReason = "other"
)

// ReportStatus defines the status of a report
type ReportStatus string

const (
    ReportStatusPending   ReportStatus = "pending"
    ReportStatusReviewed  ReportStatus = "reviewed"
    ReportStatusResolved  ReportStatus = "resolved"
    ReportStatusRejected  ReportStatus = "rejected"
)

// Report represents a user-submitted content report
type Report struct {
    ID          uint        `gorm:"primarykey" json:"id"`
    ReporterID  uint        `json:"reporter_id"`
    EntityType  ReportType  `json:"entity_type"`
    EntityID    uint        `json:"entity_id"`
    Reason      ReportReason `json:"reason"`
    Description string      `json:"description"`
    Status      ReportStatus `json:"status"`
    ReviewerID  *uint       `json:"reviewer_id"`
    ReviewNotes string      `json:"review_notes"`
    ActionTaken string      `json:"action_taken"`
    CreatedAt   time.Time   `json:"created_at"`
    UpdatedAt   time.Time   `json:"updated_at"`
    
    // Relationships
    Reporter    User        `gorm:"foreignKey:ReporterID" json:"reporter,omitempty"`
    Reviewer    *User       `gorm:"foreignKey:ReviewerID" json:"reviewer,omitempty"`
}

// ReportEvidence represents additional evidence for a report
type ReportEvidence struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ReportID    uint      `json:"report_id"`
    Type        string    `json:"type"` // screenshot, link, text
    Content     string    `json:"content"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    Report      Report    `gorm:"foreignKey:ReportID" json:"report,omitempty"`
}
```

The model structure:
- Defines report types for different content entities
- Categorizes report reasons for better moderation
- Tracks report status through the moderation workflow
- Supports evidence submission for better context

### Repository (`internal/report/repository`)

```go
// ReportRepository handles data access for reports
type ReportRepository struct {
    db *gorm.DB
}

// NewReportRepository creates a new repository instance
func NewReportRepository(db *gorm.DB) *ReportRepository {
    return &ReportRepository{
        db: db,
    }
}

// CreateReport creates a new content report
func (r *ReportRepository) CreateReport(report *models.Report) error {
    return r.db.Create(report).Error
}

// AddReportEvidence adds evidence to a report
func (r *ReportRepository) AddReportEvidence(evidence *models.ReportEvidence) error {
    return r.db.Create(evidence).Error
}

// GetReportByID retrieves a report by ID with preloaded relationships
func (r *ReportRepository) GetReportByID(id uint) (*models.Report, error) {
    var report models.Report
    
    err := r.db.Preload("Reporter", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Reviewer", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        First(&report, id).Error
    
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil
        }
        return nil, err
    }
    
    return &report, nil
}

// GetReportEvidence retrieves evidence for a report
func (r *ReportRepository) GetReportEvidence(reportID uint) ([]models.ReportEvidence, error) {
    var evidence []models.ReportEvidence
    
    if err := r.db.Where("report_id = ?", reportID).Find(&evidence).Error; err != nil {
        return nil, err
    }
    
    return evidence, nil
}

// GetReports retrieves reports with filtering and pagination
func (r *ReportRepository) GetReports(filters map[string]interface{}, page, pageSize int) ([]models.Report, int64, error) {
    var reports []models.Report
    var total int64
    
    query := r.db.Model(&models.Report{})
    
    // Apply filters
    for key, value := range filters {
        query = query.Where(key, value)
    }
    
    // Count total
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).
        Preload("Reporter", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Reviewer", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Order("created_at DESC").Find(&reports).Error; err != nil {
        return nil, 0, err
    }
    
    return reports, total, nil
}

// UpdateReportStatus updates the status of a report
func (r *ReportRepository) UpdateReportStatus(id uint, status models.ReportStatus, reviewerID uint, notes, action string) error {
    return r.db.Model(&models.Report{}).
        Where("id = ?", id).
        Updates(map[string]interface{}{
            "status":       status,
            "reviewer_id":  reviewerID,
            "review_notes": notes,
            "action_taken": action,
            "updated_at":   time.Now(),
        }).Error
}
```

The repository layer:
- Implements data access for reports and evidence
- Supports filtering and pagination for report listings
- Manages report status updates and moderation actions
- Preloads relationships for efficient data access

### Service (`internal/report/service`)

```go
// ReportService implements business logic for the report module
type ReportService struct {
    repo            *repository.ReportRepository
    userRepo        *repository.UserRepository
    discussionRepo  *repository.DiscussionRepository
    commentRepo     *repository.CommentRepository
    projectRepo     *repository.ProjectRepository
}

// NewReportService creates a new service instance
func NewReportService(
    repo *repository.ReportRepository,
    userRepo *repository.UserRepository,
    discussionRepo *repository.DiscussionRepository,
    commentRepo *repository.CommentRepository,
    projectRepo *repository.ProjectRepository,
) *ReportService {
    return &ReportService{
        repo:           repo,
        userRepo:       userRepo,
        discussionRepo: discussionRepo,
        commentRepo:    commentRepo,
        projectRepo:    projectRepo,
    }
}

// CreateReport creates a new content report
func (s *ReportService) CreateReport(report *models.Report) (*models.Report, error) {
    // Validate report data
    if report.EntityType == "" || report.EntityID == 0 || report.Reason == "" {
        return nil, errors.New("entity type, entity ID, and reason are required")
    }
    
    // Verify entity exists
    exists, err := s.verifyEntityExists(report.EntityType, report.EntityID)
    if err != nil {
        return nil, err
    }
    if !exists {
        return nil, errors.New("reported entity does not exist")
    }
    
    // Set initial status
    report.Status = models.ReportStatusPending
    report.CreatedAt = time.Now()
    report.UpdatedAt = time.Now()
    
    // Create report
    if err := s.repo.CreateReport(report); err != nil {
        return nil, err
    }
    
    return report, nil
}

// verifyEntityExists checks if the reported entity exists
func (s *ReportService) verifyEntityExists(entityType models.ReportType, entityID uint) (bool, error) {
    switch entityType {
    case models.ReportTypeDiscussion:
        discussion, err := s.discussionRepo.GetDiscussionByID(entityID)
        return discussion != nil, err
    case models.ReportTypeComment:
        comment, err := s.commentRepo.GetCommentByID(entityID)
        return comment != nil, err
    case models.ReportTypeUser:
        user, err := s.userRepo.GetUserByID(entityID)
        return user != nil, err
    case models.ReportTypeProject:
        project, err := s.projectRepo.GetProjectByID(entityID)
        return project != nil, err
    default:
        return false, errors.New("unsupported entity type")
    }
}

// AddReportEvidence adds evidence to a report
func (s *ReportService) AddReportEvidence(evidence *models.ReportEvidence) error {
    // Validate evidence
    if evidence.ReportID == 0 || evidence.Type == "" || evidence.Content == "" {
        return errors.New("report ID, type, and content are required")
    }
    
    // Verify report exists
    report, err := s.repo.GetReportByID(evidence.ReportID)
    if err != nil {
        return err
    }
    if report == nil {
        return errors.New("report not found")
    }
    
    // Add evidence
    evidence.CreatedAt = time.Now()
    return s.repo.AddReportEvidence(evidence)
}

// GetPendingReports retrieves pending reports with pagination
func (s *ReportService) GetPendingReports(page, pageSize int) ([]models.Report, int64, error) {
    filters := map[string]interface{}{
        "status": models.ReportStatusPending,
    }
    return s.repo.GetReports(filters, page, pageSize)
}

// ReviewReport processes a report review
func (s *ReportService) ReviewReport(id uint, reviewerID uint, status models.ReportStatus, notes, action string) error {
    // Validate status
    if status != models.ReportStatusReviewed && 
       status != models.ReportStatusResolved && 
       status != models.ReportStatusRejected {
        return errors.New("invalid report status")
    }
    
    // Verify report exists
    report, err := s.repo.GetReportByID(id)
    if err != nil {
        return err
    }
    if report == nil {
        return errors.New("report not found")
    }
    
    // Update report status
    return s.repo.UpdateReportStatus(id, status, reviewerID, notes, action)
}

// TakeActionOnEntity performs moderation actions on reported content
func (s *ReportService) TakeActionOnEntity(reportID uint, action string) error {
    // Get report details
    report, err := s.repo.GetReportByID(reportID)
    if err != nil {
        return err
    }
    if report == nil {
        return errors.New("report not found")
    }
    
    // Take action based on entity type
    switch report.EntityType {
    case models.ReportTypeDiscussion:
        return s.takeActionOnDiscussion(report.EntityID, action)
    case models.ReportTypeComment:
        return s.takeActionOnComment(report.EntityID, action)
    case models.ReportTypeUser:
        return s.takeActionOnUser(report.EntityID, action)
    case models.ReportTypeProject:
        return s.takeActionOnProject(report.EntityID, action)
    default:
        return errors.New("unsupported entity type")
    }
}

// Additional service methods for specific entity actions
```

The service layer:
- Implements business logic for report creation and processing
- Verifies entity existence before accepting reports
- Manages evidence submission and retrieval
- Handles moderation actions on reported content

### Handlers (`internal/report/handlers`)

```go
// ReportHandler processes HTTP requests for the report module
type ReportHandler struct {
    service *service.ReportService
}

// NewReportHandler creates a new handler instance
func NewReportHandler(service *service.ReportService) *ReportHandler {
    return &ReportHandler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the report module
func (h *ReportHandler) RegisterRoutes(router *gin.RouterGroup) {
    reportsGroup := router.Group("/reports")
    {
        // Protected routes
        authorized := reportsGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.POST("/", h.CreateReport)
            authorized.POST("/:id/evidence", h.AddReportEvidence)
            authorized.GET("/user", h.GetUserReports)
        }
        
        // Admin/moderator routes
        moderation := reportsGroup.Group("/moderation")
        moderation.Use(middleware.ModeratorRequired())
        {
            moderation.GET("/pending", h.GetPendingReports)
            moderation.GET("/:id", h.GetReportByID)
            moderation.PUT("/:id/review", h.ReviewReport)
            moderation.POST("/:id/action", h.TakeAction)
            moderation.GET("/stats", h.GetModerationStats)
        }
    }
}

// CreateReport handles report creation
func (h *ReportHandler) CreateReport(c *gin.Context) {
    var input models.Report
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Set reporter ID from authenticated user
    userID := middleware.GetUserID(c)
    input.ReporterID = userID
    
    report, err := h.service.CreateReport(&input)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{
        "message": "Report submitted successfully",
        "report": report,
    })
}

// AddReportEvidence handles evidence submission
func (h *ReportHandler) AddReportEvidence(c *gin.Context) {
    reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
        return
    }
    
    var input models.ReportEvidence
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    input.ReportID = uint(reportID)
    
    if err := h.service.AddReportEvidence(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Evidence added successfully",
    })
}

// GetPendingReports returns pending reports for moderators
func (h *ReportHandler) GetPendingReports(c *gin.Context) {
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
    
    reports, total, err := h.service.GetPendingReports(page, pageSize)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve reports"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "reports": reports,
        "pagination": gin.H{
            "total":      total,
            "page":       page,
            "page_size":  pageSize,
            "total_pages": int(math.Ceil(float64(total) / float64(pageSize))),
        },
    })
}

// ReviewReport handles report review by moderators
func (h *ReportHandler) ReviewReport(c *gin.Context) {
    reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
        return
    }
    
    var input struct {
        Status      models.ReportStatus `json:"status" binding:"required"`
        Notes       string              `json:"notes"`
        Action      string              `json:"action"`
    }
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get reviewer ID from authenticated user
    reviewerID := middleware.GetUserID(c)
    
    if err := h.service.ReviewReport(uint(reportID), reviewerID, input.Status, input.Notes, input.Action); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Report reviewed successfully",
    })
}

// Additional handler methods for moderation actions, statistics, etc.
```

The handler layer:
- Defines API routes for the report module
- Processes report creation and evidence submission
- Handles moderation review and actions
- Provides statistics and reporting for moderators
