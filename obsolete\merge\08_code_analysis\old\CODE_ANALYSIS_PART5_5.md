# Great Nigeria Project - Code Analysis (Part 5.5)

## Resource Module

The Resource module (`internal/resource`) manages educational resources and materials for the platform.

### Models (`internal/resource/models`)

```go
// ResourceType defines the type of resource
type ResourceType string

const (
    ResourceTypeDocument ResourceType = "document"
    ResourceTypeVideo    ResourceType = "video"
    ResourceTypeAudio    ResourceType = "audio"
    ResourceTypeLink     ResourceType = "link"
    ResourceTypeImage    ResourceType = "image"
    ResourceTypeOther    ResourceType = "other"
)

// Resource represents an educational resource
type Resource struct {
    ID          uint        `gorm:"primarykey" json:"id"`
    Title       string      `json:"title"`
    Description string      `json:"description"`
    Type        ResourceType `json:"type"`
    URL         string      `json:"url"`
    FileSize    int64       `json:"file_size"`
    MimeType    string      `json:"mime_type"`
    BookID      *uint       `json:"book_id"`
    ChapterID   *uint       `json:"chapter_id"`
    SectionID   *uint       `json:"section_id"`
    CreatorID   uint        `json:"creator_id"`
    IsPublic    bool        `json:"is_public"`
    AccessLevel int         `json:"access_level"` // 1=Free, 2=Points, 3=Premium
    PointsCost  int         `json:"points_cost"`
    Downloads   int         `json:"downloads"`
    CreatedAt   time.Time   `json:"created_at"`
    UpdatedAt   time.Time   `json:"updated_at"`
    
    // Relationships
    Creator     User        `gorm:"foreignKey:CreatorID" json:"creator,omitempty"`
    Tags        []ResourceTag `gorm:"many2many:resource_tag_map;" json:"tags,omitempty"`
}

// ResourceTag represents a tag for categorizing resources
type ResourceTag struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `json:"name"`
    Slug        string    `json:"slug"`
    Description string    `json:"description"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    Resources   []Resource `gorm:"many2many:resource_tag_map;" json:"resources,omitempty"`
}

// ResourceDownload tracks resource downloads by users
type ResourceDownload struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ResourceID  uint      `json:"resource_id"`
    UserID      uint      `json:"user_id"`
    DownloadedAt time.Time `json:"downloaded_at"`
    
    // Relationships
    Resource    Resource  `gorm:"foreignKey:ResourceID" json:"resource,omitempty"`
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// ResourceRating stores user ratings for resources
type ResourceRating struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ResourceID  uint      `json:"resource_id"`
    UserID      uint      `json:"user_id"`
    Rating      int       `json:"rating"` // 1-5 stars
    Comment     string    `json:"comment"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Resource    Resource  `gorm:"foreignKey:ResourceID" json:"resource,omitempty"`
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}
```

The model structure:
- Defines various resource types (documents, videos, links, etc.)
- Supports tagging for better organization
- Tracks downloads and user ratings
- Implements access control based on membership level

### Repository (`internal/resource/repository`)

```go
// ResourceRepository handles data access for resources
type ResourceRepository struct {
    db *gorm.DB
}

// NewResourceRepository creates a new repository instance
func NewResourceRepository(db *gorm.DB) *ResourceRepository {
    return &ResourceRepository{
        db: db,
    }
}

// GetResources retrieves resources with filtering and pagination
func (r *ResourceRepository) GetResources(filters map[string]interface{}, page, pageSize int) ([]models.Resource, int64, error) {
    var resources []models.Resource
    var total int64
    
    query := r.db.Model(&models.Resource{})
    
    // Apply filters
    for key, value := range filters {
        query = query.Where(key, value)
    }
    
    // Count total
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).
        Preload("Creator", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Tags").
        Order("created_at DESC").Find(&resources).Error; err != nil {
        return nil, 0, err
    }
    
    return resources, total, nil
}

// GetResourceByID retrieves a resource by ID with preloaded relationships
func (r *ResourceRepository) GetResourceByID(id uint) (*models.Resource, error) {
    var resource models.Resource
    
    err := r.db.Preload("Creator", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Tags").
        First(&resource, id).Error
    
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil
        }
        return nil, err
    }
    
    return &resource, nil
}

// CreateResource creates a new resource
func (r *ResourceRepository) CreateResource(resource *models.Resource, tagIDs []uint) error {
    return r.db.Transaction(func(tx *gorm.DB) error {
        // Create resource
        if err := tx.Create(resource).Error; err != nil {
            return err
        }
        
        // Add tags if provided
        if len(tagIDs) > 0 {
            for _, tagID := range tagIDs {
                if err := tx.Exec("INSERT INTO resource_tag_map (resource_id, resource_tag_id) VALUES (?, ?)", 
                    resource.ID, tagID).Error; err != nil {
                    return err
                }
            }
        }
        
        return nil
    })
}

// RecordResourceDownload records a resource download
func (r *ResourceRepository) RecordResourceDownload(resourceID, userID uint) error {
    // Update download count
    if err := r.db.Model(&models.Resource{}).
        Where("id = ?", resourceID).
        UpdateColumn("downloads", gorm.Expr("downloads + ?", 1)).Error; err != nil {
        return err
    }
    
    // Record download
    download := models.ResourceDownload{
        ResourceID:   resourceID,
        UserID:       userID,
        DownloadedAt: time.Now(),
    }
    
    return r.db.Create(&download).Error
}

// GetResourceTags retrieves all resource tags
func (r *ResourceRepository) GetResourceTags() ([]models.ResourceTag, error) {
    var tags []models.ResourceTag
    
    if err := r.db.Order("name ASC").Find(&tags).Error; err != nil {
        return nil, err
    }
    
    return tags, nil
}

// CreateResourceTag creates a new resource tag
func (r *ResourceRepository) CreateResourceTag(tag *models.ResourceTag) error {
    return r.db.Create(tag).Error
}

// Additional repository methods for ratings, user access, etc.
```

The repository layer:
- Implements data access for resources and related entities
- Supports filtering and pagination for resource listings
- Manages resource tags and download tracking
- Uses transactions for data consistency

### Service (`internal/resource/service`)

```go
// ResourceService implements business logic for the resource module
type ResourceService struct {
    repo        *repository.ResourceRepository
    userRepo    *repository.UserRepository
    pointsClient points.PointsServiceClient
    storageClient storage.StorageServiceClient
}

// NewResourceService creates a new service instance
func NewResourceService(
    repo *repository.ResourceRepository,
    userRepo *repository.UserRepository,
    pointsClient points.PointsServiceClient,
    storageClient storage.StorageServiceClient,
) *ResourceService {
    return &ResourceService{
        repo:         repo,
        userRepo:     userRepo,
        pointsClient: pointsClient,
        storageClient: storageClient,
    }
}

// GetPublicResources retrieves public resources with pagination
func (s *ResourceService) GetPublicResources(page, pageSize int) ([]models.Resource, int64, error) {
    filters := map[string]interface{}{
        "is_public": true,
    }
    return s.repo.GetResources(filters, page, pageSize)
}

// GetResourcesByBook retrieves resources for a specific book
func (s *ResourceService) GetResourcesByBook(bookID uint, page, pageSize int) ([]models.Resource, int64, error) {
    filters := map[string]interface{}{
        "book_id": bookID,
        "is_public": true,
    }
    return s.repo.GetResources(filters, page, pageSize)
}

// GetResourcesByTag retrieves resources with a specific tag
func (s *ResourceService) GetResourcesByTag(tagID uint, page, pageSize int) ([]models.Resource, int64, error) {
    return s.repo.GetResourcesByTag(tagID, page, pageSize)
}

// CreateResource creates a new resource
func (s *ResourceService) CreateResource(resource *models.Resource, tagIDs []uint, file *multipart.FileHeader) (*models.Resource, error) {
    // Validate resource data
    if resource.Title == "" {
        return nil, errors.New("resource title is required")
    }
    
    // Handle file upload if provided
    if file != nil {
        // Get file details
        resource.FileSize = file.Size
        resource.MimeType = file.Header.Get("Content-Type")
        
        // Upload file to storage
        uploadReq := &storage.UploadRequest{
            Filename:  file.Filename,
            Size:      file.Size,
            MimeType:  resource.MimeType,
            CreatorID: resource.CreatorID,
        }
        
        uploadResp, err := s.storageClient.UploadFile(context.Background(), uploadReq)
        if err != nil {
            return nil, fmt.Errorf("failed to upload file: %w", err)
        }
        
        // Set resource URL to uploaded file URL
        resource.URL = uploadResp.URL
    } else if resource.URL == "" {
        return nil, errors.New("either file or URL is required")
    }
    
    // Set default values
    resource.Downloads = 0
    resource.CreatedAt = time.Now()
    resource.UpdatedAt = time.Now()
    
    // Create resource
    if err := s.repo.CreateResource(resource, tagIDs); err != nil {
        return nil, err
    }
    
    // Award points for creating a resource
    go func() {
        _, err := s.pointsClient.AwardPoints(context.Background(), &points.AwardPointsRequest{
            UserId: resource.CreatorID,
            Points: 20,
            Reason: "Created an educational resource",
        })
        if err != nil {
            log.Printf("Failed to award points for resource creation: %v", err)
        }
    }()
    
    return resource, nil
}

// DownloadResource processes a resource download
func (s *ResourceService) DownloadResource(resourceID, userID uint) (string, error) {
    // Get resource details
    resource, err := s.repo.GetResourceByID(resourceID)
    if err != nil {
        return "", err
    }
    if resource == nil {
        return "", errors.New("resource not found")
    }
    
    // Check access permissions
    if !resource.IsPublic {
        // Check if user has access based on membership level
        user, err := s.userRepo.GetUserByID(userID)
        if err != nil {
            return "", err
        }
        if user == nil {
            return "", errors.New("user not found")
        }
        
        if user.MembershipLevel < resource.AccessLevel {
            return "", errors.New("insufficient membership level to access this resource")
        }
    }
    
    // Handle points-based resources
    if resource.PointsCost > 0 {
        // Check if user has already downloaded this resource
        hasDownloaded, err := s.repo.HasUserDownloadedResource(resourceID, userID)
        if err != nil {
            return "", err
        }
        
        // If not previously downloaded, check and deduct points
        if !hasDownloaded {
            // Verify user has enough points
            hasPoints, err := s.pointsClient.CheckUserHasPoints(context.Background(), &points.CheckPointsRequest{
                UserId: userID,
                Points: int32(resource.PointsCost),
            })
            if err != nil {
                return "", err
            }
            if !hasPoints.HasEnough {
                return "", errors.New("insufficient points to download this resource")
            }
            
            // Deduct points
            _, err = s.pointsClient.DeductPoints(context.Background(), &points.DeductPointsRequest{
                UserId: userID,
                Points: int32(resource.PointsCost),
                Reason: fmt.Sprintf("Downloaded resource: %s", resource.Title),
            })
            if err != nil {
                return "", err
            }
        }
    }
    
    // Record download
    if err := s.repo.RecordResourceDownload(resourceID, userID); err != nil {
        return "", err
    }
    
    // For external URLs, return the URL directly
    if resource.Type == models.ResourceTypeLink {
        return resource.URL, nil
    }
    
    // For stored files, generate a signed URL
    signedURL, err := s.storageClient.GetSignedURL(context.Background(), &storage.SignedURLRequest{
        URL:       resource.URL,
        ExpiresIn: 3600, // 1 hour
    })
    if err != nil {
        return "", err
    }
    
    return signedURL.URL, nil
}

// Additional service methods for ratings, tags, etc.
```

The service layer:
- Implements business logic for resource management
- Integrates with storage service for file uploads
- Manages access control based on membership level
- Handles points-based downloads and tracking

### Handlers (`internal/resource/handlers`)

```go
// ResourceHandler processes HTTP requests for the resource module
type ResourceHandler struct {
    service *service.ResourceService
}

// NewResourceHandler creates a new handler instance
func NewResourceHandler(service *service.ResourceService) *ResourceHandler {
    return &ResourceHandler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the resource module
func (h *ResourceHandler) RegisterRoutes(router *gin.RouterGroup) {
    resourcesGroup := router.Group("/resources")
    {
        resourcesGroup.GET("/public", h.GetPublicResources)
        resourcesGroup.GET("/tags", h.GetResourceTags)
        resourcesGroup.GET("/tags/:id", h.GetResourcesByTag)
        resourcesGroup.GET("/book/:id", h.GetResourcesByBook)
        resourcesGroup.GET("/:id", h.GetResourceByID)
        
        // Protected routes
        authorized := resourcesGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.POST("/", h.CreateResource)
            authorized.GET("/download/:id", h.DownloadResource)
            authorized.POST("/:id/rating", h.RateResource)
            authorized.GET("/user", h.GetUserResources)
        }
        
        // Admin routes
        admin := resourcesGroup.Group("/admin")
        admin.Use(middleware.AdminRequired())
        {
            admin.POST("/tags", h.CreateResourceTag)
            admin.PUT("/:id", h.UpdateResource)
            admin.DELETE("/:id", h.DeleteResource)
            admin.GET("/stats", h.GetResourceStats)
        }
    }
}

// GetPublicResources returns public resources with pagination
func (h *ResourceHandler) GetPublicResources(c *gin.Context) {
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
    
    resources, total, err := h.service.GetPublicResources(page, pageSize)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve resources"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "resources": resources,
        "pagination": gin.H{
            "total":      total,
            "page":       page,
            "page_size":  pageSize,
            "total_pages": int(math.Ceil(float64(total) / float64(pageSize))),
        },
    })
}

// CreateResource handles resource creation with file upload
func (h *ResourceHandler) CreateResource(c *gin.Context) {
    // Parse form data
    if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
        c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form data"})
        return
    }
    
    // Get resource data
    var resource models.Resource
    if err := c.ShouldBind(&resource); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get tag IDs
    var tagIDs []uint
    tagIDsStr := c.PostForm("tag_ids")
    if tagIDsStr != "" {
        for _, idStr := range strings.Split(tagIDsStr, ",") {
            id, err := strconv.ParseUint(idStr, 10, 32)
            if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tag ID format"})
                return
            }
            tagIDs = append(tagIDs, uint(id))
        }
    }
    
    // Get file if uploaded
    var file *multipart.FileHeader
    uploadedFile, _ := c.FormFile("file")
    if uploadedFile != nil {
        file = uploadedFile
    }
    
    // Set creator ID from authenticated user
    userID := middleware.GetUserID(c)
    resource.CreatorID = userID
    
    // Create resource
    createdResource, err := h.service.CreateResource(&resource, tagIDs, file)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{
        "message": "Resource created successfully",
        "resource": createdResource,
    })
}

// DownloadResource handles resource download requests
func (h *ResourceHandler) DownloadResource(c *gin.Context) {
    resourceID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID"})
        return
    }
    
    // Get user ID from authenticated user
    userID := middleware.GetUserID(c)
    
    // Process download
    downloadURL, err := h.service.DownloadResource(uint(resourceID), userID)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Resource download processed successfully",
        "download_url": downloadURL,
    })
}

// Additional handler methods for resource management, ratings, tags, etc.
```

The handler layer:
- Defines API routes for the resource module
- Processes resource creation with file uploads
- Handles resource downloads and access control
- Manages resource tags and ratings
