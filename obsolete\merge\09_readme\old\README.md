# Great Nigeria Library

## Project Overview

The Great Nigeria Library is a comprehensive digital platform built using a microservices architecture with Go (Golang) as the primary programming language. The system serves as an engagement hub for a three-book series about Nigeria's socio-economic transformation.

## Repository Structure

- **cmd/**: Entry points for all microservices
- **internal/**: Core business logic for each service
- **pkg/**: Shared utilities and common code
- **web/**: Frontend assets and templates
- **docs/**: Project documentation
  - **architecture/**: System architecture documentation
  - **code/**: Code documentation and analysis
  - **content/**: Book content structure and guidelines
  - **development/**: Development guides and standards
  - **features/**: Feature specifications
  - **project/**: Project management documentation
- **scripts/**: Utility scripts for development and deployment
- **obsolete/**: Deprecated code and documentation

## Services

- **API Gateway**: Central entry point for all client requests
- **Auth Service**: User authentication and management
- **Content Service**: Book content delivery
- **Discussion Service**: Community forums
- **Points Service**: Rewards system
- **Payment Service**: Payment processing

## Books

- **Book 1**: Great Nigeria – Awakening the Giant (free access)
- **Book 2**: Great Nigeria – The Masterplan (points-based access)
- **Book 3**: Great Nigeria – Comprehensive Edition (premium access)

## Development Setup

See [Development Guide](docs/development/DEVELOPMENT_GUIDE.md) for setup instructions.

## Project Status

See [Project Status](docs/project/PROJECT_STATUS.md) for current development status.

## Organization Standards

This project follows strict organization standards. All AI agents and developers working on this project must adhere to these standards:

1. **Directory Structure**: Follow the established directory structure
2. **Documentation Updates**: Update documentation when implementing new features
3. **Code Organization**: Place new code in appropriate directories
4. **Obsolete Files**: Move obsolete files to the obsolete directory instead of deleting
5. **Script Usage**: Use scripts in the scripts directory for common tasks

See [AI Agent Instructions](AI_AGENT_INSTRUCTIONS.md) for detailed instructions.

## Task List

See [Task List](docs/project/TASK_LIST.md) for a comprehensive list of completed and pending tasks, with file locations for implemented features.
