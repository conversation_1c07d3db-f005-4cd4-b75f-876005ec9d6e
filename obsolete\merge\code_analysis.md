

## CELEBRATE_NIGERIA_CODE_ANALYSIS.md

# Celebrate Nigeria Feature - Code Analysis

## Overview

This document provides a detailed analysis of the code implementation for the Celebrate Nigeria feature, including the data models, repository layer, service layer, and API endpoints.

## Directory Structure

The Celebrate Nigeria feature code is organized as follows:

```
internal/
  celebration/
    migrations/           # Database migrations
      001_create_celebration_tables.sql
    models/               # Data models
      models.go
    repository/           # Data access layer
      repository.go
    service/              # Business logic
      service.go
    handlers/             # API endpoints
      handlers.go
scripts/
  populate_celebrate_nigeria.go  # Data population script
  create_celebrate_image_dirs.sh # Image directory setup script
  run_celebrate_nigeria_population.sh # Script to run data population
web/
  static/
    images/
      celebrate/          # Image assets
        people/           # Images for people entries
        places/           # Images for places entries
        events/           # Images for events entries
    js/
      celebrate.js        # Frontend JavaScript
  templates/
    celebrate.html        # Main template
    celebrate_detail.html # Detail page template
```

## Data Models

The Celebrate Nigeria feature uses a comprehensive set of data models to represent different types of entries and their relationships.

### Core Models

#### Category

```go
type Category struct {
    ID          int64
    ParentID    *int64
    Name        string
    Slug        string
    Description string
    ImageURL    string
    SortOrder   int
    CreatedAt   time.Time
    UpdatedAt   time.Time
}
```

The `Category` model represents a category in the hierarchical category system. Categories can have parent categories, creating a tree structure.

#### CelebrationEntry

```go
type CelebrationEntry struct {
    ID             int64
    EntryType      string // "person", "place", "event"
    Slug           string
    Title          string
    ShortDesc      string
    FullDesc       string
    PrimaryImageURL string
    Location       string
    FeaturedRank   int
    Status         string // "draft", "published", "archived"
    Categories     []Category
    Facts          []EntryFact
    Media          []EntryMedia
    Comments       []EntryComment
    Votes          []EntryVote
    CreatedAt      time.Time
    UpdatedAt      time.Time
}
```

The `CelebrationEntry` model is the base model for all entries in the Celebrate Nigeria feature. It contains common fields shared by all entry types.

#### Type-Specific Models

##### PersonEntry

```go
type PersonEntry struct {
    CelebrationEntryID int64
    BirthDate          *time.Time
    DeathDate          *time.Time
    Profession         string
    Achievements       string
    Contributions      string
    Education          string
    RelatedLinks       string
}
```

The `PersonEntry` model extends the base `CelebrationEntry` with fields specific to people.

##### PlaceEntry

```go
type PlaceEntry struct {
    CelebrationEntryID int64
    PlaceType          string
    Latitude           float64
    Longitude          float64
    Address            string
    VisitingHours      string
    VisitingFees       string
    Accessibility      string
    History            string
}
```

The `PlaceEntry` model extends the base `CelebrationEntry` with fields specific to places.

##### EventEntry

```go
type EventEntry struct {
    CelebrationEntryID  int64
    EventType           string
    StartDate           *time.Time
    EndDate             *time.Time
    IsRecurring         bool
    RecurrencePattern   string
    Organizer           string
    ContactInfo         string
    EventHistory        string
}
```

The `EventEntry` model extends the base `CelebrationEntry` with fields specific to events.

### Supporting Models

#### EntryFact

```go
type EntryFact struct {
    ID                 int64
    CelebrationEntryID int64
    Label              string
    Value              string
    SortOrder          int
    CreatedAt          time.Time
    UpdatedAt          time.Time
}
```

The `EntryFact` model represents a key fact about an entry, displayed as a label-value pair.

#### EntryMedia

```go
type EntryMedia struct {
    ID                 int64
    CelebrationEntryID int64
    MediaType          string // "image", "video", "audio"
    URL                string
    Caption            string
    SortOrder          int
    CreatedAt          time.Time
    UpdatedAt          time.Time
}
```

The `EntryMedia` model represents a media item associated with an entry, such as an image or video.

#### EntryComment

```go
type EntryComment struct {
    ID                 int64
    CelebrationEntryID int64
    UserID             int64
    ParentCommentID    *int64
    Content            string
    Status             string // "pending", "approved", "rejected"
    CreatedAt          time.Time
    UpdatedAt          time.Time
}
```

The `EntryComment` model represents a user comment on an entry. Comments can be nested through the `ParentCommentID` field.

#### EntryVote

```go
type EntryVote struct {
    ID                 int64
    CelebrationEntryID int64
    UserID             int64
    VoteType           string // "upvote", "downvote"
    CreatedAt          time.Time
}
```

The `EntryVote` model represents a user vote on an entry, which can be either an upvote or a downvote. This model is used to track user votes and calculate the popularity of entries. The voting system allows users to express their opinion on entries and helps surface the most valuable content.

The voting system has been fully implemented with the following components:

1. **Repository Methods**:
   - `VoteForEntry`: Adds or updates a vote for an entry
   - `GetEntryVoteCounts`: Gets the upvote and downvote counts for an entry
   - `GetUserVoteForEntry`: Gets a user's vote for an entry
   - `DeleteVoteForEntry`: Removes a user's vote for an entry
   - `updateEntryLikeCount`: Updates the like count field in the entry table

2. **Service Methods**:
   - `VoteForEntry`: Validates and processes a vote for an entry
   - `GetEntryVoteCounts`: Gets the upvote and downvote counts for an entry
   - `GetUserVoteForEntry`: Gets a user's vote for an entry
   - `DeleteVoteForEntry`: Removes a user's vote for an entry

3. **Handler Methods**:
   - `VoteForEntry`: Handles POST requests to vote on an entry
   - `DeleteVoteForEntry`: Handles DELETE requests to remove a vote
   - `GetEntryVotes`: Handles GET requests to retrieve vote counts

4. **Frontend Components**:
   - `celebrate-voting.js`: JavaScript for handling vote actions
   - `celebrate-voting.css`: Styles for the voting UI
   - `celebrate-voting.html`: HTML template for the voting component

#### EntrySubmission

```go
type EntrySubmission struct {
    ID            int64        `json:"id" db:"id"`
    UserID        int64        `json:"user_id" db:"user_id"`
    EntryType     string       `json:"entry_type" db:"entry_type"`
    TargetEntryID *int64       `json:"target_entry_id,omitempty" db:"target_entry_id"`
    Title         string       `json:"title" db:"title"`
    Content       string       `json:"content" db:"content"`
    Status        string       `json:"status" db:"status"`
    AdminNotes    *string      `json:"admin_notes,omitempty" db:"admin_notes"`
    ReviewedBy    *int64       `json:"reviewed_by,omitempty" db:"reviewed_by"`
    ReviewedAt    sql.NullTime `json:"reviewed_at,omitempty" db:"reviewed_at"`
    VoteCount     int          `json:"vote_count" db:"vote_count"`
    CreatedAt     time.Time    `json:"created_at" db:"created_at"`
    UpdatedAt     time.Time    `json:"updated_at" db:"updated_at"`
}
```

The `EntrySubmission` model represents a user-submitted entry that is pending review and approval. The submission workflow has been fully implemented with the following components:

1. **Repository Methods**:
   - `CreateSubmission`: Creates a new entry submission
   - `UpdateSubmission`: Updates a submission's status and review information
   - `ListPendingSubmissions`: Retrieves pending submissions for review
   - `VoteForSubmission`: Adds a vote to a submission

2. **Service Methods**:
   - `CreateEntrySubmission`: Validates and creates a new submission
   - `ReviewSubmission`: Updates the status of a submission
   - `ListPendingSubmissions`: Retrieves pending submissions for review
   - `VoteForSubmission`: Adds a vote to a submission

3. **Handler Methods**:
   - `CreateEntrySubmission`: Handles POST requests to create a new submission
   - `ReviewSubmission`: Handles PUT requests to review a submission
   - `ListPendingSubmissions`: Handles GET requests to retrieve pending submissions
   - `VoteForSubmission`: Handles POST requests to vote on a submission
   - `RenderSubmissionForm`: Renders the submission form template
   - `RenderAdminSubmissions`: Renders the admin review page template

4. **Frontend Components**:
   - `celebrate-submission.html`: HTML template for the submission form
   - `celebrate-submission.css`: Styles for the submission form
   - `celebrate-submission.js`: JavaScript for handling form submission
   - `celebrate-admin-submissions.html`: HTML template for the admin review page
   - `celebrate-admin.css`: Styles for the admin review page
   - `celebrate-admin.js`: JavaScript for handling submission review

## Database Schema

The database schema for the Celebrate Nigeria feature is defined in `internal/celebration/migrations/001_create_celebration_tables.sql`. The schema includes the following tables:

- `categories`: Stores category information
- `celebration_entries`: Base table for all entries
- `person_entries`: Person-specific data
- `place_entries`: Place-specific data
- `event_entries`: Event-specific data
- `entry_categories`: Many-to-many relationship between entries and categories
- `entry_facts`: Facts about entries
- `entry_media`: Media items for entries
- `entry_comments`: User comments on entries
- `entry_votes`: User votes on entries
- `entry_submissions`: User-submitted entries pending review

## Repository Layer

The repository layer provides data access methods for the Celebrate Nigeria feature. It includes methods for:

- Retrieving entries by ID, slug, or category
- Creating and updating entries
- Managing entry relationships (categories, facts, media)
- Handling user interactions (comments, votes, submissions)

## Service Layer

The service layer implements the business logic for the Celebrate Nigeria feature. It includes methods for:

- Entry management (creation, retrieval, update)
- Category management
- User interaction processing (comments, votes, submissions)
- Search functionality

## API Endpoints

The API endpoints for the Celebrate Nigeria feature include:

### Category Endpoints
- `GET /api/celebrate/categories`: Get all categories
- `GET /api/celebrate/categories/:slug`: Get category by slug
- `GET /api/celebrate/categories/:slug/entries`: Get entries by category

### Entry Endpoints
- `GET /api/celebrate/entries`: Get all entries with optional filtering
- `GET /api/celebrate/entries/:slug`: Get entry by slug
- `GET /api/celebrate/entries/featured`: Get featured entries

### Comment Endpoints
- `POST /api/celebrate/entries/:id/comments`: Add a comment to an entry
- `GET /api/celebrate/entries/:id/comments`: Get comments for an entry

### Voting Endpoints
- `POST /api/celebrate/entries/:id/vote`: Vote on an entry
- `DELETE /api/celebrate/entries/:id/vote`: Remove a vote from an entry
- `GET /api/celebrate/entries/:id/votes`: Get vote counts for an entry

### Submission Endpoints
- `POST /api/celebrate/submissions`: Submit a new entry
- `GET /api/celebrate/submissions`: Get pending submissions
- `PUT /api/celebrate/submissions/:id`: Update a submission
- `POST /api/celebrate/submissions/:id/approve`: Approve a submission

## Data Population Script

The data population script (`scripts/populate_celebrate_nigeria.go`) is used to populate the database with initial entries for the Celebrate Nigeria feature. The script includes:

### Data Structures

```go
// Entry types
const (
    PersonEntryType = "person"
    PlaceEntryType  = "place"
    EventEntryType  = "event"
)

// Entry represents the base data for an entry
type Entry struct {
    Title           string
    Slug            string
    ShortDesc       string
    FullDesc        string
    PrimaryImageURL string
    Location        string
    FeaturedRank    int
    CategorySlugs   []string
    Facts           []Fact
    Media           []Media
}

// PersonData represents person-specific data
type PersonData struct {
    Entry
    BirthDate     *time.Time
    DeathDate     *time.Time
    Profession    string
    Achievements  string
    Contributions string
    Education     string
    RelatedLinks  string
}

// PlaceData represents place-specific data
type PlaceData struct {
    Entry
    PlaceType     string
    Latitude      float64
    Longitude     float64
    Address       string
    VisitingHours string
    VisitingFees  string
    Accessibility string
    History       string
}

// EventData represents event-specific data
type EventData struct {
    Entry
    EventType         string
    StartDate         *time.Time
    EndDate           *time.Time
    IsRecurring       bool
    RecurrencePattern string
    Organizer         string
    ContactInfo       string
    EventHistory      string
}

// Fact represents a key fact about an entry
type Fact struct {
    Label     string
    Value     string
    SortOrder int
}

// Media represents a media item for an entry
type Media struct {
    MediaType string
    URL       string
    Caption   string
    SortOrder int
}
```

### Database Operations

The script includes functions for:

- Connecting to the database
- Checking if required tables exist
- Inserting entries into the database
- Creating relationships between entries and categories
- Adding facts and media to entries

### Sample Data

The script includes sample data for:

- People (e.g., Chinua Achebe, Wole Soyinka, Ngozi Okonjo-Iweala)
- Places (e.g., Zuma Rock, Osun-Osogbo Sacred Grove, Eko Atlantic City)
- Events (e.g., Eyo Festival, Nigeria Independence Day, Lagos International Jazz Festival)

## Frontend Implementation

The frontend implementation for the Celebrate Nigeria feature includes:

- Main page template (`web/templates/celebrate-home.html`)
- Detail page template (`web/templates/celebrate-detail.html`)
- Search page template (`web/templates/celebrate-search.html`)
- Submission form template (`web/templates/celebrate-submission.html`)
- Moderation dashboard template (`web/templates/celebrate-moderation-dashboard.html`)
- JavaScript for dynamic content loading and user interactions (`web/static/js/celebrate.js`)

## Moderation System

The moderation system for the Celebrate Nigeria feature has been fully implemented. It includes:

1. **Content Flagging**: Allows users to flag inappropriate content
   - `EntryFlag` model for storing flags
   - Repository methods for creating and retrieving flags
   - Service methods for flagging entries and reviewing flags
   - Handler methods for flagging entries and reviewing flags
   - Frontend components for flagging content and reviewing flags

2. **Moderation Queue**: A queue for moderators to review content
   - `EntryModerationQueue` model for storing queue items
   - Repository methods for creating and retrieving queue items
   - Service methods for adding to the queue and processing queue items
   - Handler methods for managing the queue
   - Frontend components for viewing and processing the queue

3. **Moderation Dashboard**: An interface for moderators to manage content
   - Dashboard with statistics on pending flags and queue items
   - Tabs for flagged content, moderation queue, and history
   - Detailed views for reviewing flags and queue items
   - Actions for approving, rejecting, or hiding content

4. **Content Filtering**: Automatic filtering of content based on predefined rules
   - `EntryFilterResult` model for storing filter results
   - Repository methods for creating and retrieving filter results
   - Service methods for filtering entry content
   - Integration with the existing moderation system

## Conclusion

The Celebrate Nigeria feature has a comprehensive code implementation that follows best practices for separation of concerns and modularity. The data models are well-defined, and the database schema supports all required functionality. The data population script provides a solid foundation of initial content for the feature.

All user interaction features have been fully implemented, including comments, voting, submissions, and moderation. The frontend components provide a complete user experience for interacting with the content.

The next steps in the implementation should focus on enhancing the frontend experience, optimizing performance, and refining the search functionality with advanced filtering.


## CODE_ANALYSIS_PART1.md

# Great Nigeria Project - Code Analysis (Part 1)

This document provides a comprehensive analysis of the Great Nigeria project codebase, detailing the architecture, key components, and functionality of the system.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Core Architecture](#core-architecture)
3. [API Gateway](#api-gateway)
4. [Microservices](#microservices)
5. [Content Management System](#content-management-system)
6. [Book Structure and Content](#book-structure-and-content)
7. [Discussion and Community Features](#discussion-and-community-features)
8. [Points and Rewards System](#points-and-rewards-system)
9. [Citation and Bibliography System](#citation-and-bibliography-system)
10. [Additional Features](#additional-features)
11. [Backup and Data Integrity](#backup-and-data-integrity)
12. [Frontend Components](#frontend-components)

## Project Overview

The Great Nigeria project is a comprehensive digital platform built using a microservices architecture with Go (Golang) as the primary programming language. The system serves as an engagement hub for a three-book series about Nigeria's socio-economic transformation, offering features such as:

- Book content delivery with interactive elements
- Community discussion forums
- Points-based reward system
- User authentication and profile management
- Nigerian payment processing integration
- Celebration of Nigerian culture through specialized directories
- Project management and implementation reporting tools
- Enhanced accessibility features including Voice Navigation

The project is transitioning from a Node.js/Express architecture to a more scalable Go-based microservices approach, providing better performance and maintainability.

## Core Architecture

The Great Nigeria project follows a modern microservices architecture pattern with a clear separation of concerns:

1. **Entry Points (`cmd/`)**: Main applications that serve as entry points for various services
2. **Internal Packages (`internal/`)**: Business domain-specific packages that form the core of the application
3. **Common Packages (`pkg/`)**: Shared utilities and models used across multiple services
4. **Standalone Utilities (root directory)**: Specialized tools for content management and updates

Each service follows a layered architecture pattern:
- **Handlers**: Handle HTTP requests and responses, delegating business logic to services
- **Services**: Contain business logic and orchestrate operations across repositories
- **Repositories**: Handle data access and persistence operations
- **Models**: Define data structures used throughout the application

### `cmd/api-gateway/main.go`

This file serves as the entry point for the API Gateway, which is the central access point for all client requests. Key responsibilities include:

- Initializing the Gin web framework
- Registering all route handlers from various services
- Setting up middleware for authentication, logging, and CORS
- Connecting to the PostgreSQL database
- Starting the HTTP server on port 5000

The API Gateway implements a microservices architecture pattern, routing requests to appropriate internal services based on their endpoints.

```go
func main() {
    // Initialize router
    router := gin.Default()
    
    // Set up middleware
    router.Use(middleware.Auth())
    router.Use(middleware.Logging())
    router.Use(middleware.CORS())
    
    // Initialize database
    db := database.InitDatabase()
    
    // Initialize services
    authService := auth.NewAuthService(db)
    contentService := content.NewContentService(db)
    discussionService := discussion.NewDiscussionService(db)
    pointsService := points.NewPointsService(db)
    
    // Initialize handlers
    authHandler := handlers.NewAuthHandler(authService)
    contentHandler := handlers.NewContentHandler(contentService)
    discussionHandler := handlers.NewDiscussionHandler(discussionService, pointsService)
    
    // Register routes
    authHandler.RegisterRoutes(router.Group("/api/auth"))
    contentHandler.RegisterRoutes(router.Group("/api/content"))
    discussionHandler.RegisterRoutes(router.Group("/api/discussions"))
    
    // Start server
    port := os.Getenv("PORT")
    if port == "" {
        port = "5000"
    }
    
    logger.Info(fmt.Sprintf("Server running on port %s", port))
    router.Run(fmt.Sprintf("0.0.0.0:%s", port))
}
```

### `pkg/common` Package Files

These files provide shared utilities used across the application:

- `config/` - Configuration management for services
- `database/` - Database connection and transaction utilities
- `logger/` - Centralized logging functionality
- `middleware/` - Shared HTTP middleware components

## API Gateway

The API Gateway serves as the central entry point for all client requests, providing:

- Routing to appropriate microservices
- Authentication and authorization
- Request/response transformation
- Rate limiting and throttling
- API documentation (Swagger)

### Route Configuration

```go
// Content routes
contentRoutes := router.Group("/api/content")
{
    contentRoutes.GET("/books", contentHandler.GetBooks)
    contentRoutes.GET("/books/:id", contentHandler.GetBookByID)
    contentRoutes.GET("/chapters/:id", contentHandler.GetChapterByID)
    contentRoutes.GET("/sections/:id", contentHandler.GetSectionByID)
}

// Discussion routes
discussionRoutes := router.Group("/api/discussions")
{
    discussionRoutes.GET("/topics", discussionHandler.GetTopics)
    discussionRoutes.POST("/topics", discussionHandler.CreateTopic)
    discussionRoutes.GET("/topics/:id", discussionHandler.GetTopicByID)
    discussionRoutes.POST("/topics/:id/comments", discussionHandler.CreateComment)
}

// Auth routes
authRoutes := router.Group("/api/auth")
{
    authRoutes.POST("/register", authHandler.Register)
    authRoutes.POST("/login", authHandler.Login)
    authRoutes.POST("/refresh", authHandler.RefreshToken)
    authRoutes.POST("/logout", authHandler.Logout)
}
```

## Microservices

The application is divided into the following microservices:

### Auth Service (`cmd/auth-service/main.go`)

**Purpose**: Provides authentication and authorization functionality.

**Key Responsibilities**:
- User registration and login
- Session management
- JWT token generation and validation
- OAuth provider integration
- Two-factor authentication

### Content Service (`cmd/content-service/main.go`)

**Purpose**: Manages all book content and related features.

**Key Responsibilities**:
- Book content retrieval
- Content rendering and formatting
- Reading progress tracking
- Bookmarks and notes
- Interactive elements processing

### Discussion Service (`cmd/discussion-service/main.go`)

**Purpose**: Handles all forum and community-related features.

**Key Responsibilities**:
- Topic and comment management
- Moderation and flagging
- Category and tag systems
- Content linking to book sections
- Subscription management

### Payment Service (`cmd/payment-service/main.go`)

**Purpose**: Manages payments and subscriptions.

**Key Responsibilities**:
- Integration with Nigerian payment processors
- Subscription plan management
- Transaction history
- Receipt generation
- Refund processing

### Points Service (`cmd/points-service/main.go`)

**Purpose**: Handles the points-based reward system.

**Key Responsibilities**:
- Points awarding for various activities
- Leaderboard functionality
- Points history tracking
- Tier determination based on points
- Points redemption for rewards


## CODE_ANALYSIS_PART2.md

# Great Nigeria Project - Code Analysis (Part 2)

## Content Management System

### Book Structure and Models (`internal/content/models/book.go`)

**Purpose**: Defines the data structures for book content.

**Key Models**:
- `Book`: Represents a complete book with metadata
   ```go
   type Book struct {
       ID          uint       `gorm:"primarykey" json:"id"`
       Title       string     `json:"title"`
       Subtitle    string     `json:"subtitle"`
       Author      string     `json:"author"`
       Description string     `json:"description"`
       CoverImage  string     `json:"cover_image"`
       Published   bool       `json:"published"`
       CreatedAt   time.Time  `json:"created_at"`
       UpdatedAt   time.Time  `json:"updated_at"`
       Chapters    []Chapter  `gorm:"foreignKey:BookID" json:"chapters,omitempty"`
   }
   ```

- `Chapter`: Represents a chapter within a book
   ```go
   type Chapter struct {
       ID          uint       `gorm:"primarykey" json:"id"`
       BookID      uint       `json:"book_id"`
       Title       string     `json:"title"`
       Number      int        `json:"number"`
       CreatedAt   time.Time  `json:"created_at"`
       UpdatedAt   time.Time  `json:"updated_at"`
       Sections    []Section  `gorm:"foreignKey:ChapterID" json:"sections,omitempty"`
   }
   ```

- `Section`: Represents a section within a chapter
   ```go
   type Section struct {
       ID          uint       `gorm:"primarykey" json:"id"`
       ChapterID   uint       `json:"chapter_id"`
       Title       string     `json:"title"`
       Number      int        `json:"number"`
       Content     string     `json:"content"`
       Format      string     `json:"format"`
       TimeToRead  int        `json:"time_to_read"`
       Published   bool       `json:"published"`
       CreatedAt   time.Time  `json:"created_at"`
       UpdatedAt   time.Time  `json:"updated_at"`
   }
   ```

### Book Repository (`internal/content/repository/book_repository.go`)

**Purpose**: Handles database operations for book content.

**Key Functions**:
- `GetBookByID`: Retrieves a specific book with chapters and sections
- `GetPublishedBooks`: Retrieves all published books
- `GetChapterByID`: Retrieves a specific chapter with its sections
- `GetSectionByID`: Retrieves a specific section content
- `UpdateSectionContent`: Updates content for a specific section

**Implementation Pattern**:
```go
func (r *BookRepository) GetBookByID(id uint) (*models.Book, error) {
    var book models.Book
    result := r.db.Preload("Chapters", func(db *gorm.DB) *gorm.DB {
        return db.Order("number ASC")
    }).Preload("Chapters.Sections", func(db *gorm.DB) *gorm.DB {
        return db.Order("number ASC")
    }).First(&book, id)
    
    if result.Error != nil {
        if errors.Is(result.Error, gorm.ErrRecordNotFound) {
            return nil, models.ErrBookNotFound
        }
        return nil, result.Error
    }
    
    return &book, nil
}
```

### Book Service (`internal/content/service/book_service.go`)

**Purpose**: Implements business logic for book-related operations.

**Key Functions**:
- `GetBookByID`: Retrieves and processes a book for client consumption
- `GetChapterByID`: Retrieves and processes a chapter
- `GetSectionByID`: Retrieves section content with processing
- `UserHasBookAccess`: Checks if a user has access to a specific book

**Implementation Pattern**:
```go
func (s *BookService) GetSectionByID(sectionID uint, userID uint) (*models.SectionResponse, error) {
    section, err := s.repo.GetSectionByID(sectionID)
    if err != nil {
        return nil, err
    }
    
    // Get chapter to check book access
    chapter, err := s.repo.GetChapterByID(section.ChapterID)
    if err != nil {
        return nil, err
    }
    
    // Check if user has access to this content
    hasAccess, err := s.UserHasBookAccess(userID, chapter.BookID)
    if err != nil || !hasAccess {
        return nil, models.ErrAccessDenied
    }
    
    // Process content
    renderContent := s.renderer.RenderMarkdown(section.Content)
    
    // Track reading progress if user is authenticated
    if userID > 0 {
        go s.progressRepo.TrackSectionRead(userID, sectionID)
    }
    
    return &models.SectionResponse{
        ID: section.ID,
        Title: section.Title,
        Content: renderContent,
        TimeToRead: section.TimeToRead,
    }, nil
}
```

### Content Renderer (`internal/content/service/content_renderer.go`)

**Purpose**: Transforms raw content into displayable format with interactive elements.

**Key Functions**:
- `RenderMarkdown`: Converts markdown to HTML
- `ProcessInteractiveElements`: Handles interactive components
- `ProcessMediaEmbeds`: Processes embedded media
- `ProcessTopicLinks`: Links content with discussion topics

**Implementation Pattern**:
```go
func (r *ContentRenderer) RenderMarkdown(content string) string {
    // Convert markdown to HTML
    htmlContent := blackfriday.Run([]byte(content))
    
    // Process interactive elements
    processed := r.processInteractiveElements(string(htmlContent))
    
    // Process media embeds
    processed = r.processMediaEmbeds(processed)
    
    // Process topic links
    processed = r.processTopicLinks(processed)
    
    return processed
}

func (r *ContentRenderer) processInteractiveElements(content string) string {
    // Find interactive element placeholders with regex
    re := regexp.MustCompile(`\{\{interactive:([a-z-]+):([0-9]+)\}\}`)
    matches := re.FindAllStringSubmatch(content, -1)
    
    for _, match := range matches {
        elementType := match[1]
        elementID, _ := strconv.Atoi(match[2])
        
        // Retrieve element from database
        element, err := r.interactiveRepo.GetElementByID(uint(elementID))
        if err != nil {
            continue
        }
        
        // Generate appropriate HTML based on element type
        htmlElement := ""
        switch elementType {
        case "quiz":
            htmlElement = r.generateQuizHTML(element)
        case "reflection":
            htmlElement = r.generateReflectionHTML(element)
        case "call-to-action":
            htmlElement = r.generateCTAHTML(element)
        }
        
        // Replace placeholder with actual element
        content = strings.Replace(content, match[0], htmlElement, 1)
    }
    
    return content
}
```

## Book Structure and Content

### Interactive Elements (`internal/content/models/interactive_element.go`)

**Purpose**: Defines and processes interactive components within book content.

**Key Components**:
- `Quiz`: Interactive quiz elements with questions and answers
- `Reflection`: Guided reflection exercises
- `CallToAction`: Prompts for user engagement and action
- `SurveyForm`: Data collection forms within content

**Implementation Pattern**:
```go
type InteractiveElement struct {
    ID           uint      `gorm:"primarykey" json:"id"`
    Type         string    `json:"type"` // quiz, reflection, cta, survey
    Title        string    `json:"title"`
    Description  string    `json:"description"`
    Content      string    `json:"content"` // JSON string with element-specific content
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}

func (s *InteractiveElementService) GenerateElementHTML(element *models.InteractiveElement) string {
    switch element.Type {
    case "quiz":
        return s.generateQuizHTML(element)
    case "reflection":
        return s.generateReflectionHTML(element)
    case "cta":
        return s.generateCTAHTML(element)
    case "survey":
        return s.generateSurveyHTML(element)
    default:
        return ""
    }
}
```

### Section Template Files (`section_template.go` & `section_template_ghosts.go`)

**Purpose**: Define templates for specific book sections and update them in the database.

**Key Features**:
- Structured content templates with standard sections
- Database connectivity
- Error handling and logging
- Content update functionality

**Implementation Pattern**:
```go
func main() {
    // Connect to database
    dbURL := os.Getenv("DATABASE_URL")
    if dbURL == "" {
        log.Fatal("DATABASE_URL environment variable not set")
    }

    db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    })
    if err != nil {
        log.Fatalf("Failed to connect to database: %v", err)
    }

    // Update section 49 (The Bleeding Giant)
    err = updateSection(db, 49, bleedingGiantUpdated)
    if err != nil {
        log.Fatalf("Failed to update section 49: %v", err)
    }

    fmt.Println("Content updated successfully!")
}

func updateSection(db *gorm.DB, sectionID uint, content string) error {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()

    result := db.WithContext(ctx).Model(&BookSection{}).Where("id = ?", sectionID).Update("content", content)
    if result.Error != nil {
        return result.Error
    }

    if result.RowsAffected == 0 {
        return fmt.Errorf("no rows updated for section ID %d", sectionID)
    }

    return nil
}
```

### Content Structure

The content follows a consistent structure:

```markdown
# 1.1 The Bleeding Giant

![Nigeria's Contrasts: A bustling Lagos skyline against the backdrop of struggling neighborhoods]

## Introduction
Nigeria stands as a land of extraordinary potential facing profound challenges...

## Chapter Quotes
> "A nation that neglects its youth, infrastructure, and institutions does not bleed money alone—it hemorrhages hope, talent, and its very future."
> — Samuel Chimezie Okechukwu, author and concerned citizen

## Poem: The Giant's Wound
*By Samuel Chimezie Okechukwu*

...

### Research Findings: Nigeria's Paradox
...

### VOICES FROM THE FIELD: The Weight of Frustration
...

### The Evidence of Decline
...

### REFLECTION POINT: The Weight of Witnessing Decline
...

### Beyond Data: The Human Dimension
...

### A CALL TO AWAKENING: Beyond Analysis to Action
...
```


## CODE_ANALYSIS_PART3.md

# Great Nigeria Project - Code Analysis (Part 3)

## Discussion and Community Features

### `internal/discussion/handlers`

HTTP handlers for forum and discussion features:

#### `topic_handler.go`
- Creates, updates, lists, and deletes discussion topics
- Handles topic categorization and tagging
- Manages pinned/featured topics and view counts

```go
// GetTopics returns a list of discussion topics
func (h *TopicHandler) GetTopics(c *gin.Context) {
    categoryID := c.Query("category_id")
    page := c.DefaultQuery("page", "1")
    pageSize := c.DefaultQuery("page_size", "20")
    
    pageInt, _ := strconv.Atoi(page)
    pageSizeInt, _ := strconv.Atoi(pageSize)
    
    var categoryIDUint uint
    if categoryID != "" {
        categoryIDInt, _ := strconv.Atoi(categoryID)
        categoryIDUint = uint(categoryIDInt)
    }
    
    topics, total, err := h.topicService.GetTopics(categoryIDUint, pageInt, pageSizeInt)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve topics"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "topics": topics,
        "total": total,
        "page": pageInt,
        "page_size": pageSizeInt,
    })
}
```

#### `comment_handler.go`
- Manages creation and retrieval of comments
- Implements threading and sorting logic
- Handles upvoting and reporting of comments

#### `flag_handler.go`
- Implements content moderation flagging system
- Processes user reports of inappropriate content
- Assigns moderation tasks and tracks status

#### `category_handler.go`
- Manages discussion categories and subcategories
- Controls category permissions and visibility
- Configures posting rules per category

### `internal/discussion/service`

Business logic for discussion features:

#### `topic_service.go`
- Implements business rules for topic creation and management
- Handles validation and filtering
- Integrates with the points system for rewards

```go
func (s *TopicService) CreateTopic(userID uint, input models.TopicInput) (*models.Topic, error) {
    // Validate input
    if input.Title == "" || input.Content == "" {
        return nil, errors.New("title and content are required")
    }
    
    // Check if category exists
    if input.CategoryID > 0 {
        exists, err := s.categoryRepo.CategoryExists(input.CategoryID)
        if err != nil {
            return nil, err
        }
        if !exists {
            return nil, errors.New("category not found")
        }
    }
    
    // Create topic
    topic := &models.Topic{
        UserID:      userID,
        CategoryID:  input.CategoryID,
        Title:       input.Title,
        Content:     input.Content,
        Tags:        input.Tags,
        CreatedAt:   time.Now(),
        UpdatedAt:   time.Now(),
    }
    
    // Save to database
    err := s.topicRepo.CreateTopic(topic)
    if err != nil {
        return nil, err
    }
    
    // Award points for creating a topic
    go s.pointsService.AwardPointsForTopicCreation(userID, topic.ID)
    
    return topic, nil
}
```

#### `moderation_service.go`
- Processes content flags and reports
- Implements automated moderation rules
- Manages user penalties and restrictions

## Points and Rewards System

### `internal/points/handlers`

HTTP handlers for the points system:

#### `discussion_points_handler.go`
- Awards points for creating topics
- Awards points for replies
- Grants points for receiving upvotes
- Distributes bonus points for featured content
- Provides configuration endpoints for point values

```go
// AwardPointsForComment awards points for creating a comment
func (h *DiscussionPointsHandler) AwardPointsForComment(c *gin.Context) {
    var input struct {
        UserID    uint `json:"user_id" binding:"required"`
        CommentID uint `json:"comment_id" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    points, err := h.pointsService.AwardPointsForComment(input.UserID, input.CommentID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to award points"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "points_awarded": points,
        "user_id": input.UserID,
        "comment_id": input.CommentID,
    })
}
```

### `internal/points/service`

Business logic for points and rewards:

#### `points_service.go`
- Calculates points for various user actions
- Tracks point history and ledger
- Implements point thresholds for features
- Provides points-based content access control

```go
func (s *PointsService) AwardPointsForTopicCreation(userID uint, topicID uint) (int, error) {
    // Get point value for topic creation
    pointValue, err := s.configRepo.GetPointValue("topic_creation")
    if err != nil {
        return 0, err
    }
    
    // Check if points were already awarded for this topic
    exists, err := s.pointsRepo.PointsExistForAction(userID, "topic_creation", topicID)
    if err != nil {
        return 0, err
    }
    if exists {
        return 0, errors.New("points already awarded for this topic")
    }
    
    // Create points record
    pointsRecord := &models.PointsRecord{
        UserID:     userID,
        Points:     pointValue,
        ActionType: "topic_creation",
        ReferenceID: topicID,
        CreatedAt:  time.Now(),
    }
    
    // Save to database
    err = s.pointsRepo.CreatePointsRecord(pointsRecord)
    if err != nil {
        return 0, err
    }
    
    // Update user's total points
    err = s.userRepo.IncrementUserPoints(userID, pointValue)
    if err != nil {
        return 0, err
    }
    
    // Check if user has reached a new tier
    go s.checkAndUpdateUserTier(userID)
    
    return pointValue, nil
}
```

## Citation and Bibliography System

### `citation_tracker.go`

**Purpose**: Tracks, validates, and manages citations across all book content.

**Key Components**:

1. **CitationTracker Structure**:
   ```go
   type CitationTracker struct {
       db              *gorm.DB
       sourceMap       map[uint]*Source
       citationsByBook map[uint][]Citation
       logger          *log.Logger
   }
   ```
   - Maintains an in-memory cache of sources and citations organized by book

2. **Initialization**:
   ```go
   func NewCitationTracker(db *gorm.DB) (*CitationTracker, error) {
       tracker := &CitationTracker{
           db:              db,
           sourceMap:       make(map[uint]*Source),
           citationsByBook: make(map[uint][]Citation),
           logger:          log.New(os.Stdout, "[CitationTracker] ", log.LstdFlags),
       }
       
       // Load all sources and citations from database
       if err := tracker.LoadSources(); err != nil {
           return nil, err
       }
       
       if err := tracker.LoadAllCitations(); err != nil {
           return nil, err
       }
       
       return tracker, nil
   }
   ```
   - Loads all sources and citations into memory at startup

3. **Citation Verification**:
   ```go
   func (t *CitationTracker) VerifyCitationConsistency(bookID uint) ([]string, error) {
       var issues []string
       
       citations, ok := t.citationsByBook[bookID]
       if !ok {
           return nil, fmt.Errorf("no citations found for book ID %d", bookID)
       }
       
       // Check for missing sequential numbers
       numbers := make([]int, len(citations))
       for i, c := range citations {
           numbers[i] = c.Number
       }
       
       sort.Ints(numbers)
       
       for i := 1; i < len(numbers); i++ {
           if numbers[i] != numbers[i-1]+1 {
               issues = append(issues, fmt.Sprintf("Missing citation numbers between %d and %d", 
                   numbers[i-1], numbers[i]))
           }
       }
       
       // Check for duplicate numbers
       numCounts := make(map[int]int)
       for _, c := range citations {
           numCounts[c.Number]++
       }
       
       for num, count := range numCounts {
           if count > 1 {
               issues = append(issues, fmt.Sprintf("Citation number %d appears %d times", num, count))
           }
       }
       
       return issues, nil
   }
   ```
   - Ensures citations are numbered sequentially and consistently

4. **Bibliography Generation**:
   ```go
   func (t *CitationTracker) GenerateBibliography(bookID uint) (string, error) {
       citations, ok := t.citationsByBook[bookID]
       if !ok {
           return "", fmt.Errorf("no citations found for book ID %d", bookID)
       }
       
       // Group citations by source to avoid duplicates
       sourceIDs := make(map[uint]bool)
       for _, c := range citations {
           sourceIDs[c.SourceID] = true
       }
       
       var bibliography strings.Builder
       bibliography.WriteString("# Bibliography\n\n")
       
       // Generate formatted bibliography entries
       for sourceID := range sourceIDs {
           source, ok := t.sourceMap[sourceID]
           if !ok {
               continue
           }
           
           entry := t.formatBibliographyEntry(source)
           bibliography.WriteString(entry)
           bibliography.WriteString("\n\n")
       }
       
       return bibliography.String(), nil
   }
   ```
   - Generates a formatted bibliography from citation sources

### `generate_bibliography.go`

**Purpose**: Command-line utility to generate bibliography sections for book appendices.

**Key Components**:

1. **Main Function**:
   ```go
   func main() {
       // Connect to database
       dbURL := os.Getenv("DATABASE_URL")
       if dbURL == "" {
           log.Fatal("DATABASE_URL environment variable not set")
       }
       
       db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{
           Logger: logger.Default.LogMode(logger.Info),
       })
       if err != nil {
           log.Fatalf("Failed to connect to database: %v", err)
       }
       
       // Initialize citation tracker
       tracker, err := NewCitationTracker(db)
       if err != nil {
           log.Fatalf("Failed to initialize citation tracker: %v", err)
       }
       
       // Generate bibliography for each book
       for bookID := uint(1); bookID <= 3; bookID++ {
           bibliography, err := tracker.GenerateBibliography(bookID)
           if err != nil {
               log.Printf("Warning: %v", err)
               continue
           }
           
           // Update bibliography in database
           if err := updateBibliography(db, bookID, bibliography); err != nil {
               log.Printf("Failed to update bibliography for book %d: %v", bookID, err)
           } else {
               log.Printf("Bibliography updated successfully for book %d", bookID)
           }
       }
   }
   ```
   - Generates and updates bibliography sections for all three books


## CODE_ANALYSIS_PART4.md

# Great Nigeria Project - Code Analysis (Part 4)

## Additional Features

### `internal/celebration`

"Celebrate Nigeria" feature for highlighting Nigerian excellence:

#### `models`, `repository`, `service`, `handlers`
- Implements a comprehensive directory of Nigerian achievements, people, places, and events
- Uses a hierarchical category system for better organization
- Provides advanced search and filtering capabilities
- Supports user interactions including comments, votes, and submissions
- Implements a moderation system for user-generated content

The feature has been significantly enhanced with a more comprehensive data model and implementation. For detailed analysis, see [CELEBRATE_NIGERIA_CODE_ANALYSIS.md](CELEBRATE_NIGERIA_CODE_ANALYSIS.md).

```go
// CelebrationEntry represents the base model for all entries
type CelebrationEntry struct {
    ID             int64      `json:"id"`
    EntryType      string     `json:"entry_type"` // "person", "place", "event"
    Slug           string     `json:"slug"`
    Title          string     `json:"title"`
    ShortDesc      string     `json:"short_desc"`
    FullDesc       string     `json:"full_desc"`
    PrimaryImageURL string     `json:"primary_image_url"`
    Location       string     `json:"location"`
    FeaturedRank   int        `json:"featured_rank"`
    Status         string     `json:"status"` // "draft", "published", "archived"
    Categories     []Category `json:"categories"`
    Facts          []EntryFact `json:"facts"`
    Media          []EntryMedia `json:"media"`
    Comments       []EntryComment `json:"comments"`
    Votes          []EntryVote `json:"votes"`
    CreatedAt      time.Time  `json:"created_at"`
    UpdatedAt      time.Time  `json:"updated_at"`
}

// PersonEntry extends CelebrationEntry with person-specific fields
type PersonEntry struct {
    CelebrationEntryID int64      `json:"celebration_entry_id"`
    BirthDate          *time.Time `json:"birth_date"`
    DeathDate          *time.Time `json:"death_date"`
    Profession         string     `json:"profession"`
    Achievements       string     `json:"achievements"`
    Contributions      string     `json:"contributions"`
    Education          string     `json:"education"`
    RelatedLinks       string     `json:"related_links"`
}

// GetEntriesByCategory returns entries for a specific category
func (s *CelebrationService) GetEntriesByCategory(categorySlug string, page, pageSize int) ([]models.CelebrationEntry, int, error) {
    entries, total, err := s.repo.GetEntriesByCategory(categorySlug, page, pageSize)
    if err != nil {
        return nil, 0, err
    }

    return entries, total, nil
}

// GetEntryBySlug returns a specific entry by its slug
func (s *CelebrationService) GetEntryBySlug(slug string) (*models.CelebrationEntry, error) {
    entry, err := s.repo.GetEntryBySlug(slug)
    if err != nil {
        return nil, err
    }

    return entry, nil
}
```

#### Data Population Script

A comprehensive data population script has been implemented to seed the database with high-quality content:

```go
// From scripts/populate_celebrate_nigeria.go

// Populate people entries
func populatePeople(ctx context.Context, tx *sql.Tx) error {
    people := []PersonData{
        {
            Entry: Entry{
                Title:           "Chinua Achebe",
                Slug:            "chinua-achebe",
                ShortDesc:       "Renowned novelist, poet, and critic...",
                // Additional fields...
            },
            BirthDate:     parseDate("1930-11-16"),
            DeathDate:     parseDate("2013-03-21"),
            Profession:    "Novelist, Poet, Professor, Critic",
            // Additional fields...
        },
        // Additional people entries...
    }

    // Insert each person into the database
    for _, person := range people {
        // Implementation details...
    }

    return nil
}
```

### `internal/project`, `internal/resource`, `internal/report`

Implementation tools for community projects:

- Project management features for tracking initiatives
- Resource libraries for shared materials
- Reporting tools for implementation monitoring

## Backup and Data Integrity

### `backup_db.sh`

Performs regular PostgreSQL database backups:
- Creates full database dumps with timestamps
- Applies compression to save storage space
- Archives older backups to manage disk usage

```bash
#!/bin/bash
# backup_db.sh - Database backup script

# Configuration
DB_NAME="great_nigeria_db"
BACKUP_DIR="/var/backups/great_nigeria"
TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")
BACKUP_FILE="${BACKUP_DIR}/great_nigeria_db_${TIMESTAMP}.sql"
COMPRESS=true
MAX_BACKUPS=10

# Create backup directory if it doesn't exist
mkdir -p ${BACKUP_DIR}

# Perform database dump
echo "Creating database backup: ${BACKUP_FILE}"
pg_dump -U postgres ${DB_NAME} > ${BACKUP_FILE}

# Compress backup if enabled
if [ "${COMPRESS}" = true ]; then
    echo "Compressing backup..."
    gzip ${BACKUP_FILE}
    BACKUP_FILE="${BACKUP_FILE}.gz"
fi

echo "Backup completed: ${BACKUP_FILE}"

# Manage backup retention
echo "Managing backup retention..."
ls -t ${BACKUP_DIR}/great_nigeria_db_*.sql* | tail -n +$((MAX_BACKUPS+1)) | xargs -r rm

echo "Backup process completed successfully."
```

### `backup_db_workflow.sh`

Workflow script that runs as a background service:
- Schedules regular database backups
- Handles project file backups
- Includes disk space management
- Provides logging of backup operations

### `github_backup.sh`

Exports critical data to a GitHub repository:
- Creates structured snapshots of database schema
- Extracts API routes and models for documentation
- Maintains metadata about the backup process
- Pushes changes to a private GitHub repository

### `setup_backup_cron.sh` & `setup_backup_repo.sh`

Setup scripts for the backup system:
- Configures automated backup schedules
- Sets up the GitHub backup repository
- Establishes initial backup structures
- Configures authentication for secure backups

## Frontend Components

### `web/static/book-viewer.html`

Primary frontend component for displaying book content:
- Renders book sections with proper formatting
- Processes markdown content into styled HTML
- Supports interactive elements and embedded media
- Provides navigation between chapters and sections
- Handles front matter and back matter display

### Accessibility Features

The project includes comprehensive accessibility support:
- Voice navigation for hands-free operation
- Screen reader optimizations
- Font size adjustment features
- High contrast mode
- Keyboard navigation enhancements

```javascript
// Voice navigation implementation
const voiceNavigation = {
    initialize: function() {
        if (!('webkitSpeechRecognition' in window)) {
            console.log('Voice navigation not supported in this browser');
            return;
        }

        this.recognition = new webkitSpeechRecognition();
        this.recognition.continuous = true;
        this.recognition.interimResults = false;

        this.setupCommands();
        this.setupEventListeners();
    },

    setupCommands: function() {
        this.commands = {
            'next': this.navigateNext,
            'previous': this.navigatePrevious,
            'chapter': this.navigateToChapter,
            'home': this.navigateToHome,
            'bookmark': this.addBookmark,
            'search': this.search
        };
    },

    setupEventListeners: function() {
        this.recognition.onresult = (event) => {
            const transcript = event.results[event.results.length - 1][0].transcript.trim().toLowerCase();
            this.processCommand(transcript);
        };
    },

    processCommand: function(transcript) {
        // Process voice commands
        for (const [command, action] of Object.entries(this.commands)) {
            if (transcript.includes(command)) {
                action(transcript);
                break;
            }
        }
    },

    // Command implementations
    navigateNext: function() {
        document.querySelector('.next-section-button').click();
    },

    navigatePrevious: function() {
        document.querySelector('.prev-section-button').click();
    },

    // Additional command implementations...
};
```

### Content Rendering

The frontend implements a sophisticated rendering pipeline:
- Converts markdown to styled HTML
- Processes custom interactive elements
- Supports various content components (quotes, poems, calls to action)
- Integrates discussion links with relevant content sections

## Conclusion

The Great Nigeria project represents a robust, well-structured application built on modern microservices principles. The codebase demonstrates clear separation of concerns with distinct layers for data access, business logic, and presentation. The transition to Go microservices provides a solid foundation for the system's continued growth and scalability.

Key technical strengths include:
- Clean architecture with proper separation of concerns
- Consistent error handling and logging
- Robust data backup and integrity measures
- Scalable microservices design
- Comprehensive API documentation

The content management system is particularly well-designed, with a flexible structure that supports the project's unique documentary-interview style while maintaining consistent formatting and interactive elements across all books.

## Next Steps for Development

Based on the code analysis, the following areas could benefit from further development:

1. **Enhanced Testing**: Implement more comprehensive unit and integration tests
2. **Performance Optimization**: Profile and optimize database queries and content rendering
3. **API Documentation**: Complete Swagger documentation for all endpoints
4. **Monitoring**: Implement more robust monitoring and alerting
5. **Caching**: Add Redis caching for frequently accessed content
6. **Mobile Optimization**: Enhance mobile responsiveness of the frontend components
7. **Celebrate Nigeria Feature Completion**:
   - Implement user interaction features (comments, votes, submissions)
   - Enhance search functionality with advanced filtering
   - Complete frontend templates for detail pages
   - Add actual images for entries


## CODE_ANALYSIS_PART5.md

# Great Nigeria Project - Code Analysis (Part 5)

This document extends the code analysis to cover additional modules and components that were not thoroughly documented in the previous parts.

## Table of Contents

1. [Celebration Module](#celebration-module)
2. [Gifts Module](#gifts-module)
3. [Project Module](#project-module)
4. [Report Module](#report-module)
5. [Resource Module](#resource-module)
6. [Template Module](#template-module)
7. [Web Components](#web-components)
8. [Frontend-Backend Integration](#frontend-backend-integration)

## Celebration Module

The Celebration module (`internal/celebration`) implements the "Celebrate Nigeria" feature, which showcases Nigerian excellence across various domains.

### Database Structure (`internal/celebration/database/driver.go`)

```go
// Driver provides database connectivity for the celebration module
type Driver struct {
    DB *gorm.DB
}

// NewDriver creates a new database driver instance
func NewDriver(db *gorm.DB) *Driver {
    return &Driver{
        DB: db,
    }
}

// RunMigrations executes all database migrations for the celebration module
func (d *Driver) RunMigrations() error {
    migrator := migrations.NewMigrator(d.DB)
    return migrator.RunMigrations()
}
```

This component:
- Provides a dedicated database connection for the celebration module
- Manages database migrations specific to celebration features
- Isolates database operations from business logic

### Migrations (`internal/celebration/migrations/`)

The migrations directory contains database schema definitions:

```go
// 001_create_celebration_tables.go
func CreateCelebrationTables() *gorm.DB {
    return func(tx *gorm.DB) error {
        // Create categories table
        if err := tx.AutoMigrate(&models.Category{}); err != nil {
            return err
        }
        
        // Create entries table
        if err := tx.AutoMigrate(&models.Entry{}); err != nil {
            return err
        }
        
        // Create nominations table
        if err := tx.AutoMigrate(&models.Nomination{}); err != nil {
            return err
        }
        
        return nil
    }
}
```

The migration system:
- Creates tables for categories, entries, and nominations
- Establishes relationships between tables
- Provides versioned schema changes

### Models (`internal/celebration/models/models.go`)

```go
// Category represents a classification for celebration entries
type Category struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Slug        string    `json:"slug"`
    ParentID    *uint     `json:"parent_id"`
    ImageURL    string    `json:"image_url"`
    DisplayOrder int       `json:"display_order"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Parent      *Category  `gorm:"foreignKey:ParentID" json:"parent,omitempty"`
    Children    []Category `gorm:"foreignKey:ParentID" json:"children,omitempty"`
    Entries     []Entry    `gorm:"foreignKey:CategoryID" json:"entries,omitempty"`
}

// Entry represents a celebration item showcasing Nigerian excellence
type Entry struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    Content     string    `json:"content"`
    CategoryID  uint      `json:"category_id"`
    Year        int       `json:"year"`
    Location    string    `json:"location"`
    ImageURL    string    `json:"image_url"`
    SourceURL   string    `json:"source_url"`
    Featured    bool      `json:"featured"`
    Verified    bool      `json:"verified"`
    SubmitterID uint      `json:"submitter_id"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Category    Category  `gorm:"foreignKey:CategoryID" json:"category,omitempty"`
}

// Nomination represents a user-submitted celebration entry pending verification
type Nomination struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    Content     string    `json:"content"`
    CategoryID  uint      `json:"category_id"`
    Year        int       `json:"year"`
    Location    string    `json:"location"`
    ImageURL    string    `json:"image_url"`
    SourceURL   string    `json:"source_url"`
    UserID      uint      `json:"user_id"`
    Status      string    `json:"status"` // pending, approved, rejected
    ReviewerID  *uint     `json:"reviewer_id"`
    ReviewNotes string    `json:"review_notes"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

The model structure:
- Defines hierarchical categories with parent-child relationships
- Supports verified entries and user-submitted nominations
- Includes metadata like location, year, and sources
- Implements moderation workflow for community contributions

### Repository (`internal/celebration/repository/repository.go`)

```go
// Repository handles data access for the celebration module
type Repository struct {
    db *gorm.DB
}

// NewRepository creates a new repository instance
func NewRepository(db *gorm.DB) *Repository {
    return &Repository{
        db: db,
    }
}

// GetCategories retrieves categories with optional parent filtering
func (r *Repository) GetCategories(parentID *uint) ([]models.Category, error) {
    var categories []models.Category
    query := r.db.Order("display_order ASC")
    
    if parentID != nil {
        query = query.Where("parent_id = ?", parentID)
    } else {
        query = query.Where("parent_id IS NULL")
    }
    
    if err := query.Find(&categories).Error; err != nil {
        return nil, err
    }
    
    return categories, nil
}

// GetEntries retrieves celebration entries with filtering options
func (r *Repository) GetEntries(categoryID *uint, featured bool, page, pageSize int) ([]models.Entry, int64, error) {
    var entries []models.Entry
    var total int64
    
    query := r.db.Model(&models.Entry{}).Where("verified = ?", true)
    
    if categoryID != nil {
        query = query.Where("category_id = ?", categoryID)
    }
    
    if featured {
        query = query.Where("featured = ?", true)
    }
    
    // Count total entries
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&entries).Error; err != nil {
        return nil, 0, err
    }
    
    return entries, total, nil
}

// Additional repository methods for nominations, verification, etc.
```

The repository layer:
- Implements data access patterns for categories and entries
- Supports filtering, pagination, and sorting
- Handles verification status and featured content
- Manages the nomination workflow

### Service (`internal/celebration/service/service.go`)

```go
// Service implements business logic for the celebration module
type Service struct {
    repo *repository.Repository
}

// NewService creates a new service instance
func NewService(repo *repository.Repository) *Service {
    return &Service{
        repo: repo,
    }
}

// GetCategoryTree retrieves the full category hierarchy
func (s *Service) GetCategoryTree() ([]models.Category, error) {
    // Get root categories
    rootCategories, err := s.repo.GetCategories(nil)
    if err != nil {
        return nil, err
    }
    
    // Recursively load children for each category
    for i := range rootCategories {
        if err := s.loadCategoryChildren(&rootCategories[i]); err != nil {
            return nil, err
        }
    }
    
    return rootCategories, nil
}

// LoadCategoryChildren recursively loads child categories
func (s *Service) loadCategoryChildren(category *models.Category) error {
    children, err := s.repo.GetCategories(&category.ID)
    if err != nil {
        return err
    }
    
    category.Children = children
    
    for i := range category.Children {
        if err := s.loadCategoryChildren(&category.Children[i]); err != nil {
            return err
        }
    }
    
    return nil
}

// GetFeaturedEntries retrieves featured entries across categories
func (s *Service) GetFeaturedEntries(limit int) ([]models.Entry, error) {
    return s.repo.GetFeaturedEntries(limit)
}

// SubmitNomination processes a user-submitted nomination
func (s *Service) SubmitNomination(nomination *models.Nomination) error {
    // Validate nomination
    if nomination.Title == "" || nomination.Description == "" {
        return errors.New("title and description are required")
    }
    
    // Set initial status
    nomination.Status = "pending"
    
    // Save to database
    return s.repo.CreateNomination(nomination)
}

// Additional service methods for moderation, approval, etc.
```

The service layer:
- Implements business logic for category hierarchy
- Manages featured content selection
- Processes user nominations with validation
- Handles moderation workflow

### Handlers (`internal/celebration/handlers/handlers.go`)

```go
// Handler processes HTTP requests for the celebration module
type Handler struct {
    service *service.Service
}

// NewHandler creates a new handler instance
func NewHandler(service *service.Service) *Handler {
    return &Handler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the celebration module
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
    celebrateGroup := router.Group("/celebrate")
    {
        celebrateGroup.GET("/categories", h.GetCategories)
        celebrateGroup.GET("/categories/:id", h.GetCategoryByID)
        celebrateGroup.GET("/entries", h.GetEntries)
        celebrateGroup.GET("/entries/:id", h.GetEntryByID)
        celebrateGroup.GET("/featured", h.GetFeaturedEntries)
        
        // Protected routes
        authorized := celebrateGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.POST("/nominations", h.SubmitNomination)
            authorized.GET("/nominations", h.GetUserNominations)
        }
        
        // Admin routes
        admin := celebrateGroup.Group("/admin")
        admin.Use(middleware.AdminRequired())
        {
            admin.GET("/nominations", h.GetPendingNominations)
            admin.PUT("/nominations/:id/approve", h.ApproveNomination)
            admin.PUT("/nominations/:id/reject", h.RejectNomination)
            admin.POST("/categories", h.CreateCategory)
            admin.PUT("/categories/:id", h.UpdateCategory)
            admin.POST("/entries", h.CreateEntry)
            admin.PUT("/entries/:id", h.UpdateEntry)
            admin.DELETE("/entries/:id", h.DeleteEntry)
        }
    }
}

// GetCategories returns all categories
func (h *Handler) GetCategories(c *gin.Context) {
    hierarchical := c.DefaultQuery("hierarchical", "false") == "true"
    
    var categories []models.Category
    var err error
    
    if hierarchical {
        categories, err = h.service.GetCategoryTree()
    } else {
        parentID := c.Query("parent_id")
        var parentIDPtr *uint
        
        if parentID != "" {
            id, err := strconv.ParseUint(parentID, 10, 32)
            if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid parent_id"})
                return
            }
            idUint := uint(id)
            parentIDPtr = &idUint
        }
        
        categories, err = h.service.GetCategories(parentIDPtr)
    }
    
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve categories"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"categories": categories})
}

// Additional handler methods for entries, nominations, etc.
```

The handler layer:
- Defines API routes for the celebration module
- Implements request processing and response formatting
- Manages authentication and authorization
- Provides admin functionality for content management


## CODE_ANALYSIS_PART5_2.md

# Great Nigeria Project - Code Analysis (Part 5.2)

## Gifts Module

The Gifts module (`internal/gifts`) implements the Nigerian Virtual Gifts system, allowing users to send culturally authentic virtual gifts to each other.

### Models (`internal/gifts/models`)

```go
// GiftCategory represents a classification for virtual gifts
type GiftCategory struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    DisplayOrder int      `json:"display_order"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Gifts       []Gift    `gorm:"foreignKey:CategoryID" json:"gifts,omitempty"`
}

// Gift represents a virtual gift item
type Gift struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    CategoryID  uint      `json:"category_id"`
    ImageURL    string    `json:"image_url"`
    AnimationURL string   `json:"animation_url"`
    SoundURL    string    `json:"sound_url"`
    Price       int       `json:"price"` // In points
    CoinPrice   int       `json:"coin_price"` // In virtual coins
    IsActive    bool      `json:"is_active"`
    IsPremium   bool      `json:"is_premium"`
    DisplayOrder int      `json:"display_order"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Category    GiftCategory `gorm:"foreignKey:CategoryID" json:"category,omitempty"`
}

// GiftTransaction records gift exchanges between users
type GiftTransaction struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    SenderID    uint      `json:"sender_id"`
    RecipientID uint      `json:"recipient_id"`
    GiftID      uint      `json:"gift_id"`
    Message     string    `json:"message"`
    PointsCost  int       `json:"points_cost"`
    CoinsCost   int       `json:"coins_cost"`
    PaymentType string    `json:"payment_type"` // "points" or "coins"
    IsAnonymous bool      `json:"is_anonymous"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    Gift        Gift      `gorm:"foreignKey:GiftID" json:"gift,omitempty"`
    Sender      User      `gorm:"foreignKey:SenderID" json:"sender,omitempty"`
    Recipient   User      `gorm:"foreignKey:RecipientID" json:"recipient,omitempty"`
}

// GiftRevenue tracks revenue sharing for gift transactions
type GiftRevenue struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    TransactionID   uint      `json:"transaction_id"`
    RecipientAmount int       `json:"recipient_amount"` // 50% to recipient
    PlatformAmount  int       `json:"platform_amount"`  // 50% to platform
    ProcessedAt     time.Time `json:"processed_at"`
    
    // Relationships
    Transaction     GiftTransaction `gorm:"foreignKey:TransactionID" json:"transaction,omitempty"`
}

// GiftNotificationSettings stores user preferences for gift notifications
type GiftNotificationSettings struct {
    ID              uint      `gorm:"primarykey" json:"id"`
    UserID          uint      `json:"user_id"`
    ReceiveNotifications bool  `json:"receive_notifications"`
    ShowAnimation   bool      `json:"show_animation"`
    PlaySound       bool      `json:"play_sound"`
    EmailNotification bool    `json:"email_notification"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
}
```

The model structure:
- Defines gift categories and individual gift items
- Tracks gift transactions between users
- Implements revenue sharing between recipients and platform
- Manages notification preferences for gift recipients

### Repository (`internal/gifts/repository`)

```go
// GiftRepository handles data access for gifts
type GiftRepository struct {
    db *gorm.DB
}

// NewGiftRepository creates a new repository instance
func NewGiftRepository(db *gorm.DB) *GiftRepository {
    return &GiftRepository{
        db: db,
    }
}

// GetGiftCategories retrieves all gift categories
func (r *GiftRepository) GetGiftCategories() ([]models.GiftCategory, error) {
    var categories []models.GiftCategory
    
    if err := r.db.Order("display_order ASC").Find(&categories).Error; err != nil {
        return nil, err
    }
    
    return categories, nil
}

// GetGiftsByCategory retrieves gifts for a specific category
func (r *GiftRepository) GetGiftsByCategory(categoryID uint) ([]models.Gift, error) {
    var gifts []models.Gift
    
    if err := r.db.Where("category_id = ? AND is_active = ?", categoryID, true).
        Order("display_order ASC").Find(&gifts).Error; err != nil {
        return nil, err
    }
    
    return gifts, nil
}

// CreateGiftTransaction records a new gift transaction
func (r *GiftRepository) CreateGiftTransaction(transaction *models.GiftTransaction) error {
    return r.db.Create(transaction).Error
}

// GetUserReceivedGifts retrieves gifts received by a user
func (r *GiftRepository) GetUserReceivedGifts(userID uint, page, pageSize int) ([]models.GiftTransaction, int64, error) {
    var transactions []models.GiftTransaction
    var total int64
    
    query := r.db.Model(&models.GiftTransaction{}).
        Where("recipient_id = ?", userID).
        Preload("Gift").
        Preload("Sender", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, profile_image")
        })
    
    // Count total
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).
        Order("created_at DESC").Find(&transactions).Error; err != nil {
        return nil, 0, err
    }
    
    return transactions, total, nil
}

// Additional repository methods for leaderboards, revenue tracking, etc.
```

The repository layer:
- Implements data access for gift categories and items
- Manages gift transactions and revenue sharing
- Supports user gift history and leaderboards
- Handles notification preferences

### Service (`internal/gifts/service`)

```go
// GiftService implements business logic for the gifts module
type GiftService struct {
    repo        *repository.GiftRepository
    userRepo    *repository.UserRepository
    pointsClient points.PointsServiceClient
}

// NewGiftService creates a new service instance
func NewGiftService(repo *repository.GiftRepository, userRepo *repository.UserRepository, pointsClient points.PointsServiceClient) *GiftService {
    return &GiftService{
        repo:        repo,
        userRepo:    userRepo,
        pointsClient: pointsClient,
    }
}

// GetGiftCatalog retrieves the full gift catalog with categories
func (s *GiftService) GetGiftCatalog() ([]models.GiftCategoryWithGifts, error) {
    // Get all categories
    categories, err := s.repo.GetGiftCategories()
    if err != nil {
        return nil, err
    }
    
    // Build response with gifts for each category
    result := make([]models.GiftCategoryWithGifts, len(categories))
    for i, category := range categories {
        gifts, err := s.repo.GetGiftsByCategory(category.ID)
        if err != nil {
            return nil, err
        }
        
        result[i] = models.GiftCategoryWithGifts{
            ID:          category.ID,
            Name:        category.Name,
            Description: category.Description,
            Gifts:       gifts,
        }
    }
    
    return result, nil
}

// SendGift processes a gift transaction
func (s *GiftService) SendGift(senderID, recipientID, giftID uint, message string, isAnonymous bool, paymentType string) (*models.GiftTransaction, error) {
    // Validate recipient exists
    recipient, err := s.userRepo.GetUserByID(recipientID)
    if err != nil {
        return nil, err
    }
    if recipient == nil {
        return nil, errors.New("recipient not found")
    }
    
    // Get gift details
    gift, err := s.repo.GetGiftByID(giftID)
    if err != nil {
        return nil, err
    }
    if gift == nil {
        return nil, errors.New("gift not found")
    }
    if !gift.IsActive {
        return nil, errors.New("gift is not available")
    }
    
    // Determine cost based on payment type
    var pointsCost, coinsCost int
    if paymentType == "points" {
        pointsCost = gift.Price
        coinsCost = 0
        
        // Verify sender has enough points
        hasPoints, err := s.pointsClient.CheckUserHasPoints(context.Background(), &points.CheckPointsRequest{
            UserId: senderID,
            Points: int32(pointsCost),
        })
        if err != nil {
            return nil, err
        }
        if !hasPoints.HasEnough {
            return nil, errors.New("insufficient points")
        }
        
        // Deduct points
        _, err = s.pointsClient.DeductPoints(context.Background(), &points.DeductPointsRequest{
            UserId: senderID,
            Points: int32(pointsCost),
            Reason: fmt.Sprintf("Gift: %s", gift.Name),
        })
        if err != nil {
            return nil, err
        }
    } else if paymentType == "coins" {
        pointsCost = 0
        coinsCost = gift.CoinPrice
        
        // Verify sender has enough coins
        hasCoins, err := s.userRepo.CheckUserHasCoins(senderID, coinsCost)
        if err != nil {
            return nil, err
        }
        if !hasCoins {
            return nil, errors.New("insufficient coins")
        }
        
        // Deduct coins
        err = s.userRepo.DeductUserCoins(senderID, coinsCost)
        if err != nil {
            return nil, err
        }
    } else {
        return nil, errors.New("invalid payment type")
    }
    
    // Create transaction
    transaction := &models.GiftTransaction{
        SenderID:    senderID,
        RecipientID: recipientID,
        GiftID:      giftID,
        Message:     message,
        PointsCost:  pointsCost,
        CoinsCost:   coinsCost,
        PaymentType: paymentType,
        IsAnonymous: isAnonymous,
        CreatedAt:   time.Now(),
    }
    
    // Save transaction
    if err := s.repo.CreateGiftTransaction(transaction); err != nil {
        return nil, err
    }
    
    // Process revenue sharing (50% to recipient)
    go s.processRevenueSharing(transaction)
    
    // Send notification to recipient
    go s.sendGiftNotification(transaction)
    
    return transaction, nil
}

// Additional service methods for analytics, leaderboards, etc.
```

The service layer:
- Implements business logic for gift catalog management
- Processes gift transactions with payment validation
- Handles revenue sharing between recipients and platform
- Manages notifications and analytics

### Handlers (`internal/gifts/handlers`)

```go
// GiftHandler processes HTTP requests for the gifts module
type GiftHandler struct {
    service *service.GiftService
}

// NewGiftHandler creates a new handler instance
func NewGiftHandler(service *service.GiftService) *GiftHandler {
    return &GiftHandler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the gifts module
func (h *GiftHandler) RegisterRoutes(router *gin.RouterGroup) {
    giftsGroup := router.Group("/gifts")
    {
        giftsGroup.GET("/catalog", h.GetGiftCatalog)
        giftsGroup.GET("/categories", h.GetGiftCategories)
        giftsGroup.GET("/categories/:id", h.GetGiftsByCategory)
        
        // Protected routes
        authorized := giftsGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.POST("/send", h.SendGift)
            authorized.GET("/received", h.GetReceivedGifts)
            authorized.GET("/sent", h.GetSentGifts)
            authorized.GET("/leaderboard", h.GetGiftLeaderboard)
            authorized.GET("/settings", h.GetNotificationSettings)
            authorized.PUT("/settings", h.UpdateNotificationSettings)
        }
        
        // Admin routes
        admin := giftsGroup.Group("/admin")
        admin.Use(middleware.AdminRequired())
        {
            admin.POST("/categories", h.CreateGiftCategory)
            admin.PUT("/categories/:id", h.UpdateGiftCategory)
            admin.POST("/gifts", h.CreateGift)
            admin.PUT("/gifts/:id", h.UpdateGift)
            admin.GET("/analytics", h.GetGiftAnalytics)
            admin.GET("/revenue", h.GetRevenueReport)
        }
    }
}

// GetGiftCatalog returns the full gift catalog
func (h *GiftHandler) GetGiftCatalog(c *gin.Context) {
    catalog, err := h.service.GetGiftCatalog()
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve gift catalog"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"catalog": catalog})
}

// SendGift processes a gift transaction
func (h *GiftHandler) SendGift(c *gin.Context) {
    var input struct {
        RecipientID uint   `json:"recipient_id" binding:"required"`
        GiftID      uint   `json:"gift_id" binding:"required"`
        Message     string `json:"message"`
        IsAnonymous bool   `json:"is_anonymous"`
        PaymentType string `json:"payment_type" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get sender ID from authenticated user
    userID := middleware.GetUserID(c)
    
    transaction, err := h.service.SendGift(userID, input.RecipientID, input.GiftID, 
        input.Message, input.IsAnonymous, input.PaymentType)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Gift sent successfully",
        "transaction": transaction,
    })
}

// Additional handler methods for gift management, analytics, etc.
```

The handler layer:
- Defines API routes for the gifts module
- Processes gift transactions and catalog requests
- Manages authentication and authorization
- Provides admin functionality for gift management and analytics


## CODE_ANALYSIS_PART5_3.md

# Great Nigeria Project - Code Analysis (Part 5.3)

## Project Module

The Project module (`internal/project`) enables users to create and collaborate on community implementation projects based on book initiatives.

### Models (`internal/project/models`)

```go
// Project represents a community implementation initiative
type Project struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    BookID      *uint     `json:"book_id"`
    ChapterID   *uint     `json:"chapter_id"`
    SectionID   *uint     `json:"section_id"`
    CreatorID   uint      `json:"creator_id"`
    Status      string    `json:"status"` // planning, active, completed, archived
    StartDate   time.Time `json:"start_date"`
    EndDate     *time.Time `json:"end_date"`
    ImageURL    string    `json:"image_url"`
    Location    string    `json:"location"`
    IsVirtual   bool      `json:"is_virtual"`
    IsPublic    bool      `json:"is_public"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Creator     User      `gorm:"foreignKey:CreatorID" json:"creator,omitempty"`
    Members     []ProjectMember `gorm:"foreignKey:ProjectID" json:"members,omitempty"`
    Tasks       []ProjectTask `gorm:"foreignKey:ProjectID" json:"tasks,omitempty"`
    Updates     []ProjectUpdate `gorm:"foreignKey:ProjectID" json:"updates,omitempty"`
    Resources   []ProjectResource `gorm:"foreignKey:ProjectID" json:"resources,omitempty"`
}

// ProjectMember represents a user participating in a project
type ProjectMember struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ProjectID   uint      `json:"project_id"`
    UserID      uint      `json:"user_id"`
    Role        string    `json:"role"` // owner, admin, member
    JoinedAt    time.Time `json:"joined_at"`
    
    // Relationships
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// ProjectTask represents a task within a project
type ProjectTask struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ProjectID   uint      `json:"project_id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    Status      string    `json:"status"` // pending, in_progress, completed
    Priority    string    `json:"priority"` // low, medium, high
    AssigneeID  *uint     `json:"assignee_id"`
    DueDate     *time.Time `json:"due_date"`
    CompletedAt *time.Time `json:"completed_at"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Assignee    *User     `gorm:"foreignKey:AssigneeID" json:"assignee,omitempty"`
}

// ProjectUpdate represents a progress update for a project
type ProjectUpdate struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ProjectID   uint      `json:"project_id"`
    UserID      uint      `json:"user_id"`
    Content     string    `json:"content"`
    MediaURLs   pq.StringArray `gorm:"type:text[]" json:"media_urls"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// ProjectResource represents a resource shared within a project
type ProjectResource struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ProjectID   uint      `json:"project_id"`
    UserID      uint      `json:"user_id"`
    Title       string    `json:"title"`
    Description string    `json:"description"`
    Type        string    `json:"type"` // link, document, image, video
    URL         string    `json:"url"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}
```

The model structure:
- Defines projects with metadata and book references
- Tracks project membership with role-based permissions
- Manages tasks with assignment and status tracking
- Supports progress updates and resource sharing

### Repository (`internal/project/repository`)

```go
// ProjectRepository handles data access for projects
type ProjectRepository struct {
    db *gorm.DB
}

// NewProjectRepository creates a new repository instance
func NewProjectRepository(db *gorm.DB) *ProjectRepository {
    return &ProjectRepository{
        db: db,
    }
}

// GetProjects retrieves projects with filtering options
func (r *ProjectRepository) GetProjects(filters map[string]interface{}, page, pageSize int) ([]models.Project, int64, error) {
    var projects []models.Project
    var total int64
    
    query := r.db.Model(&models.Project{})
    
    // Apply filters
    for key, value := range filters {
        query = query.Where(key, value)
    }
    
    // Count total
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).
        Preload("Creator", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Order("created_at DESC").Find(&projects).Error; err != nil {
        return nil, 0, err
    }
    
    return projects, total, nil
}

// GetProjectByID retrieves a project by ID with preloaded relationships
func (r *ProjectRepository) GetProjectByID(id uint) (*models.Project, error) {
    var project models.Project
    
    err := r.db.Preload("Creator").
        Preload("Members.User", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Tasks", func(db *gorm.DB) *gorm.DB {
            return db.Order("priority DESC, due_date ASC")
        }).
        Preload("Tasks.Assignee", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Updates", func(db *gorm.DB) *gorm.DB {
            return db.Order("created_at DESC")
        }).
        Preload("Updates.User").
        Preload("Resources").
        First(&project, id).Error
    
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil
        }
        return nil, err
    }
    
    return &project, nil
}

// CreateProject creates a new project
func (r *ProjectRepository) CreateProject(project *models.Project) error {
    return r.db.Transaction(func(tx *gorm.DB) error {
        // Create project
        if err := tx.Create(project).Error; err != nil {
            return err
        }
        
        // Add creator as owner
        member := models.ProjectMember{
            ProjectID: project.ID,
            UserID:    project.CreatorID,
            Role:      "owner",
            JoinedAt:  time.Now(),
        }
        
        return tx.Create(&member).Error
    })
}

// Additional repository methods for tasks, members, updates, etc.
```

The repository layer:
- Implements data access for projects and related entities
- Supports filtering and pagination for project listings
- Manages relationships between projects, members, tasks, and resources
- Uses transactions for data consistency

### Service (`internal/project/service`)

```go
// ProjectService implements business logic for the project module
type ProjectService struct {
    repo        *repository.ProjectRepository
    userRepo    *repository.UserRepository
    pointsClient points.PointsServiceClient
}

// NewProjectService creates a new service instance
func NewProjectService(repo *repository.ProjectRepository, userRepo *repository.UserRepository, pointsClient points.PointsServiceClient) *ProjectService {
    return &ProjectService{
        repo:        repo,
        userRepo:    userRepo,
        pointsClient: pointsClient,
    }
}

// GetPublicProjects retrieves public projects with pagination
func (s *ProjectService) GetPublicProjects(page, pageSize int) ([]models.Project, int64, error) {
    filters := map[string]interface{}{
        "is_public": true,
    }
    return s.repo.GetProjects(filters, page, pageSize)
}

// GetUserProjects retrieves projects for a specific user
func (s *ProjectService) GetUserProjects(userID uint, page, pageSize int) ([]models.Project, int64, error) {
    // Get projects where user is a member
    memberProjects, err := s.repo.GetProjectsByMember(userID)
    if err != nil {
        return nil, 0, err
    }
    
    // Extract project IDs
    var projectIDs []uint
    for _, project := range memberProjects {
        projectIDs = append(projectIDs, project.ID)
    }
    
    // If user is not a member of any projects, return empty result
    if len(projectIDs) == 0 {
        return []models.Project{}, 0, nil
    }
    
    // Get full project details with pagination
    filters := map[string]interface{}{
        "id": projectIDs,
    }
    return s.repo.GetProjects(filters, page, pageSize)
}

// CreateProject creates a new project and awards points
func (s *ProjectService) CreateProject(project *models.Project) (*models.Project, error) {
    // Validate project data
    if project.Title == "" {
        return nil, errors.New("project title is required")
    }
    
    // Set default values
    project.Status = "planning"
    project.CreatedAt = time.Now()
    project.UpdatedAt = time.Now()
    
    // Create project
    if err := s.repo.CreateProject(project); err != nil {
        return nil, err
    }
    
    // Award points for creating a project
    go func() {
        _, err := s.pointsClient.AwardPoints(context.Background(), &points.AwardPointsRequest{
            UserId: project.CreatorID,
            Points: 50,
            Reason: "Created a new community project",
        })
        if err != nil {
            log.Printf("Failed to award points for project creation: %v", err)
        }
    }()
    
    return project, nil
}

// JoinProject adds a user to a project
func (s *ProjectService) JoinProject(projectID, userID uint) error {
    // Check if project exists
    project, err := s.repo.GetProjectByID(projectID)
    if err != nil {
        return err
    }
    if project == nil {
        return errors.New("project not found")
    }
    
    // Check if user is already a member
    isMember, err := s.repo.IsUserProjectMember(projectID, userID)
    if err != nil {
        return err
    }
    if isMember {
        return errors.New("user is already a member of this project")
    }
    
    // Add user as member
    member := models.ProjectMember{
        ProjectID: projectID,
        UserID:    userID,
        Role:      "member",
        JoinedAt:  time.Now(),
    }
    
    if err := s.repo.CreateProjectMember(&member); err != nil {
        return err
    }
    
    // Award points for joining a project
    go func() {
        _, err := s.pointsClient.AwardPoints(context.Background(), &points.AwardPointsRequest{
            UserId: userID,
            Points: 10,
            Reason: "Joined a community project",
        })
        if err != nil {
            log.Printf("Failed to award points for joining project: %v", err)
        }
    }()
    
    return nil
}

// Additional service methods for tasks, updates, resources, etc.
```

The service layer:
- Implements business logic for project management
- Integrates with the points system for rewards
- Manages project membership and permissions
- Handles task assignment and progress tracking

### Handlers (`internal/project/handlers`)

```go
// ProjectHandler processes HTTP requests for the project module
type ProjectHandler struct {
    service *service.ProjectService
}

// NewProjectHandler creates a new handler instance
func NewProjectHandler(service *service.ProjectService) *ProjectHandler {
    return &ProjectHandler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the project module
func (h *ProjectHandler) RegisterRoutes(router *gin.RouterGroup) {
    projectsGroup := router.Group("/projects")
    {
        projectsGroup.GET("/public", h.GetPublicProjects)
        projectsGroup.GET("/:id", h.GetProjectByID)
        
        // Protected routes
        authorized := projectsGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.GET("/", h.GetUserProjects)
            authorized.POST("/", h.CreateProject)
            authorized.PUT("/:id", h.UpdateProject)
            authorized.POST("/:id/join", h.JoinProject)
            authorized.POST("/:id/leave", h.LeaveProject)
            authorized.POST("/:id/tasks", h.CreateTask)
            authorized.PUT("/:id/tasks/:taskId", h.UpdateTask)
            authorized.POST("/:id/updates", h.AddUpdate)
            authorized.POST("/:id/resources", h.AddResource)
            authorized.GET("/:id/members", h.GetProjectMembers)
            authorized.PUT("/:id/members/:userId", h.UpdateMemberRole)
        }
    }
}

// GetPublicProjects returns public projects with pagination
func (h *ProjectHandler) GetPublicProjects(c *gin.Context) {
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
    
    projects, total, err := h.service.GetPublicProjects(page, pageSize)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve projects"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "projects": projects,
        "pagination": gin.H{
            "total":      total,
            "page":       page,
            "page_size":  pageSize,
            "total_pages": int(math.Ceil(float64(total) / float64(pageSize))),
        },
    })
}

// CreateProject handles project creation
func (h *ProjectHandler) CreateProject(c *gin.Context) {
    var input models.Project
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Set creator ID from authenticated user
    userID := middleware.GetUserID(c)
    input.CreatorID = userID
    
    project, err := h.service.CreateProject(&input)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{
        "message": "Project created successfully",
        "project": project,
    })
}

// Additional handler methods for project management, tasks, updates, etc.
```

The handler layer:
- Defines API routes for the project module
- Processes project creation and management requests
- Handles task assignment and progress updates
- Manages project membership and permissions


## CODE_ANALYSIS_PART5_4.md

# Great Nigeria Project - Code Analysis (Part 5.4)

## Report Module

The Report module (`internal/report`) handles content reporting and moderation for the platform.

### Models (`internal/report/models`)

```go
// ReportType defines the type of content being reported
type ReportType string

const (
    ReportTypeDiscussion ReportType = "discussion"
    ReportTypeComment    ReportType = "comment"
    ReportTypeUser       ReportType = "user"
    ReportTypeProject    ReportType = "project"
    ReportTypeResource   ReportType = "resource"
)

// ReportReason defines the reason for reporting content
type ReportReason string

const (
    ReportReasonSpam             ReportReason = "spam"
    ReportReasonHarassment       ReportReason = "harassment"
    ReportReasonInappropriate    ReportReason = "inappropriate"
    ReportReasonViolation        ReportReason = "violation"
    ReportReasonOther            ReportReason = "other"
)

// ReportStatus defines the status of a report
type ReportStatus string

const (
    ReportStatusPending   ReportStatus = "pending"
    ReportStatusReviewed  ReportStatus = "reviewed"
    ReportStatusResolved  ReportStatus = "resolved"
    ReportStatusRejected  ReportStatus = "rejected"
)

// Report represents a user-submitted content report
type Report struct {
    ID          uint        `gorm:"primarykey" json:"id"`
    ReporterID  uint        `json:"reporter_id"`
    EntityType  ReportType  `json:"entity_type"`
    EntityID    uint        `json:"entity_id"`
    Reason      ReportReason `json:"reason"`
    Description string      `json:"description"`
    Status      ReportStatus `json:"status"`
    ReviewerID  *uint       `json:"reviewer_id"`
    ReviewNotes string      `json:"review_notes"`
    ActionTaken string      `json:"action_taken"`
    CreatedAt   time.Time   `json:"created_at"`
    UpdatedAt   time.Time   `json:"updated_at"`
    
    // Relationships
    Reporter    User        `gorm:"foreignKey:ReporterID" json:"reporter,omitempty"`
    Reviewer    *User       `gorm:"foreignKey:ReviewerID" json:"reviewer,omitempty"`
}

// ReportEvidence represents additional evidence for a report
type ReportEvidence struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ReportID    uint      `json:"report_id"`
    Type        string    `json:"type"` // screenshot, link, text
    Content     string    `json:"content"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    Report      Report    `gorm:"foreignKey:ReportID" json:"report,omitempty"`
}
```

The model structure:
- Defines report types for different content entities
- Categorizes report reasons for better moderation
- Tracks report status through the moderation workflow
- Supports evidence submission for better context

### Repository (`internal/report/repository`)

```go
// ReportRepository handles data access for reports
type ReportRepository struct {
    db *gorm.DB
}

// NewReportRepository creates a new repository instance
func NewReportRepository(db *gorm.DB) *ReportRepository {
    return &ReportRepository{
        db: db,
    }
}

// CreateReport creates a new content report
func (r *ReportRepository) CreateReport(report *models.Report) error {
    return r.db.Create(report).Error
}

// AddReportEvidence adds evidence to a report
func (r *ReportRepository) AddReportEvidence(evidence *models.ReportEvidence) error {
    return r.db.Create(evidence).Error
}

// GetReportByID retrieves a report by ID with preloaded relationships
func (r *ReportRepository) GetReportByID(id uint) (*models.Report, error) {
    var report models.Report
    
    err := r.db.Preload("Reporter", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Reviewer", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        First(&report, id).Error
    
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil
        }
        return nil, err
    }
    
    return &report, nil
}

// GetReportEvidence retrieves evidence for a report
func (r *ReportRepository) GetReportEvidence(reportID uint) ([]models.ReportEvidence, error) {
    var evidence []models.ReportEvidence
    
    if err := r.db.Where("report_id = ?", reportID).Find(&evidence).Error; err != nil {
        return nil, err
    }
    
    return evidence, nil
}

// GetReports retrieves reports with filtering and pagination
func (r *ReportRepository) GetReports(filters map[string]interface{}, page, pageSize int) ([]models.Report, int64, error) {
    var reports []models.Report
    var total int64
    
    query := r.db.Model(&models.Report{})
    
    // Apply filters
    for key, value := range filters {
        query = query.Where(key, value)
    }
    
    // Count total
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).
        Preload("Reporter", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Reviewer", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Order("created_at DESC").Find(&reports).Error; err != nil {
        return nil, 0, err
    }
    
    return reports, total, nil
}

// UpdateReportStatus updates the status of a report
func (r *ReportRepository) UpdateReportStatus(id uint, status models.ReportStatus, reviewerID uint, notes, action string) error {
    return r.db.Model(&models.Report{}).
        Where("id = ?", id).
        Updates(map[string]interface{}{
            "status":       status,
            "reviewer_id":  reviewerID,
            "review_notes": notes,
            "action_taken": action,
            "updated_at":   time.Now(),
        }).Error
}
```

The repository layer:
- Implements data access for reports and evidence
- Supports filtering and pagination for report listings
- Manages report status updates and moderation actions
- Preloads relationships for efficient data access

### Service (`internal/report/service`)

```go
// ReportService implements business logic for the report module
type ReportService struct {
    repo            *repository.ReportRepository
    userRepo        *repository.UserRepository
    discussionRepo  *repository.DiscussionRepository
    commentRepo     *repository.CommentRepository
    projectRepo     *repository.ProjectRepository
}

// NewReportService creates a new service instance
func NewReportService(
    repo *repository.ReportRepository,
    userRepo *repository.UserRepository,
    discussionRepo *repository.DiscussionRepository,
    commentRepo *repository.CommentRepository,
    projectRepo *repository.ProjectRepository,
) *ReportService {
    return &ReportService{
        repo:           repo,
        userRepo:       userRepo,
        discussionRepo: discussionRepo,
        commentRepo:    commentRepo,
        projectRepo:    projectRepo,
    }
}

// CreateReport creates a new content report
func (s *ReportService) CreateReport(report *models.Report) (*models.Report, error) {
    // Validate report data
    if report.EntityType == "" || report.EntityID == 0 || report.Reason == "" {
        return nil, errors.New("entity type, entity ID, and reason are required")
    }
    
    // Verify entity exists
    exists, err := s.verifyEntityExists(report.EntityType, report.EntityID)
    if err != nil {
        return nil, err
    }
    if !exists {
        return nil, errors.New("reported entity does not exist")
    }
    
    // Set initial status
    report.Status = models.ReportStatusPending
    report.CreatedAt = time.Now()
    report.UpdatedAt = time.Now()
    
    // Create report
    if err := s.repo.CreateReport(report); err != nil {
        return nil, err
    }
    
    return report, nil
}

// verifyEntityExists checks if the reported entity exists
func (s *ReportService) verifyEntityExists(entityType models.ReportType, entityID uint) (bool, error) {
    switch entityType {
    case models.ReportTypeDiscussion:
        discussion, err := s.discussionRepo.GetDiscussionByID(entityID)
        return discussion != nil, err
    case models.ReportTypeComment:
        comment, err := s.commentRepo.GetCommentByID(entityID)
        return comment != nil, err
    case models.ReportTypeUser:
        user, err := s.userRepo.GetUserByID(entityID)
        return user != nil, err
    case models.ReportTypeProject:
        project, err := s.projectRepo.GetProjectByID(entityID)
        return project != nil, err
    default:
        return false, errors.New("unsupported entity type")
    }
}

// AddReportEvidence adds evidence to a report
func (s *ReportService) AddReportEvidence(evidence *models.ReportEvidence) error {
    // Validate evidence
    if evidence.ReportID == 0 || evidence.Type == "" || evidence.Content == "" {
        return errors.New("report ID, type, and content are required")
    }
    
    // Verify report exists
    report, err := s.repo.GetReportByID(evidence.ReportID)
    if err != nil {
        return err
    }
    if report == nil {
        return errors.New("report not found")
    }
    
    // Add evidence
    evidence.CreatedAt = time.Now()
    return s.repo.AddReportEvidence(evidence)
}

// GetPendingReports retrieves pending reports with pagination
func (s *ReportService) GetPendingReports(page, pageSize int) ([]models.Report, int64, error) {
    filters := map[string]interface{}{
        "status": models.ReportStatusPending,
    }
    return s.repo.GetReports(filters, page, pageSize)
}

// ReviewReport processes a report review
func (s *ReportService) ReviewReport(id uint, reviewerID uint, status models.ReportStatus, notes, action string) error {
    // Validate status
    if status != models.ReportStatusReviewed && 
       status != models.ReportStatusResolved && 
       status != models.ReportStatusRejected {
        return errors.New("invalid report status")
    }
    
    // Verify report exists
    report, err := s.repo.GetReportByID(id)
    if err != nil {
        return err
    }
    if report == nil {
        return errors.New("report not found")
    }
    
    // Update report status
    return s.repo.UpdateReportStatus(id, status, reviewerID, notes, action)
}

// TakeActionOnEntity performs moderation actions on reported content
func (s *ReportService) TakeActionOnEntity(reportID uint, action string) error {
    // Get report details
    report, err := s.repo.GetReportByID(reportID)
    if err != nil {
        return err
    }
    if report == nil {
        return errors.New("report not found")
    }
    
    // Take action based on entity type
    switch report.EntityType {
    case models.ReportTypeDiscussion:
        return s.takeActionOnDiscussion(report.EntityID, action)
    case models.ReportTypeComment:
        return s.takeActionOnComment(report.EntityID, action)
    case models.ReportTypeUser:
        return s.takeActionOnUser(report.EntityID, action)
    case models.ReportTypeProject:
        return s.takeActionOnProject(report.EntityID, action)
    default:
        return errors.New("unsupported entity type")
    }
}

// Additional service methods for specific entity actions
```

The service layer:
- Implements business logic for report creation and processing
- Verifies entity existence before accepting reports
- Manages evidence submission and retrieval
- Handles moderation actions on reported content

### Handlers (`internal/report/handlers`)

```go
// ReportHandler processes HTTP requests for the report module
type ReportHandler struct {
    service *service.ReportService
}

// NewReportHandler creates a new handler instance
func NewReportHandler(service *service.ReportService) *ReportHandler {
    return &ReportHandler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the report module
func (h *ReportHandler) RegisterRoutes(router *gin.RouterGroup) {
    reportsGroup := router.Group("/reports")
    {
        // Protected routes
        authorized := reportsGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.POST("/", h.CreateReport)
            authorized.POST("/:id/evidence", h.AddReportEvidence)
            authorized.GET("/user", h.GetUserReports)
        }
        
        // Admin/moderator routes
        moderation := reportsGroup.Group("/moderation")
        moderation.Use(middleware.ModeratorRequired())
        {
            moderation.GET("/pending", h.GetPendingReports)
            moderation.GET("/:id", h.GetReportByID)
            moderation.PUT("/:id/review", h.ReviewReport)
            moderation.POST("/:id/action", h.TakeAction)
            moderation.GET("/stats", h.GetModerationStats)
        }
    }
}

// CreateReport handles report creation
func (h *ReportHandler) CreateReport(c *gin.Context) {
    var input models.Report
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Set reporter ID from authenticated user
    userID := middleware.GetUserID(c)
    input.ReporterID = userID
    
    report, err := h.service.CreateReport(&input)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{
        "message": "Report submitted successfully",
        "report": report,
    })
}

// AddReportEvidence handles evidence submission
func (h *ReportHandler) AddReportEvidence(c *gin.Context) {
    reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
        return
    }
    
    var input models.ReportEvidence
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    input.ReportID = uint(reportID)
    
    if err := h.service.AddReportEvidence(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Evidence added successfully",
    })
}

// GetPendingReports returns pending reports for moderators
func (h *ReportHandler) GetPendingReports(c *gin.Context) {
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
    
    reports, total, err := h.service.GetPendingReports(page, pageSize)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve reports"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "reports": reports,
        "pagination": gin.H{
            "total":      total,
            "page":       page,
            "page_size":  pageSize,
            "total_pages": int(math.Ceil(float64(total) / float64(pageSize))),
        },
    })
}

// ReviewReport handles report review by moderators
func (h *ReportHandler) ReviewReport(c *gin.Context) {
    reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
        return
    }
    
    var input struct {
        Status      models.ReportStatus `json:"status" binding:"required"`
        Notes       string              `json:"notes"`
        Action      string              `json:"action"`
    }
    
    if err := c.ShouldBindJSON(&input); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get reviewer ID from authenticated user
    reviewerID := middleware.GetUserID(c)
    
    if err := h.service.ReviewReport(uint(reportID), reviewerID, input.Status, input.Notes, input.Action); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Report reviewed successfully",
    })
}

// Additional handler methods for moderation actions, statistics, etc.
```

The handler layer:
- Defines API routes for the report module
- Processes report creation and evidence submission
- Handles moderation review and actions
- Provides statistics and reporting for moderators


## CODE_ANALYSIS_PART5_5.md

# Great Nigeria Project - Code Analysis (Part 5.5)

## Resource Module

The Resource module (`internal/resource`) manages educational resources and materials for the platform.

### Models (`internal/resource/models`)

```go
// ResourceType defines the type of resource
type ResourceType string

const (
    ResourceTypeDocument ResourceType = "document"
    ResourceTypeVideo    ResourceType = "video"
    ResourceTypeAudio    ResourceType = "audio"
    ResourceTypeLink     ResourceType = "link"
    ResourceTypeImage    ResourceType = "image"
    ResourceTypeOther    ResourceType = "other"
)

// Resource represents an educational resource
type Resource struct {
    ID          uint        `gorm:"primarykey" json:"id"`
    Title       string      `json:"title"`
    Description string      `json:"description"`
    Type        ResourceType `json:"type"`
    URL         string      `json:"url"`
    FileSize    int64       `json:"file_size"`
    MimeType    string      `json:"mime_type"`
    BookID      *uint       `json:"book_id"`
    ChapterID   *uint       `json:"chapter_id"`
    SectionID   *uint       `json:"section_id"`
    CreatorID   uint        `json:"creator_id"`
    IsPublic    bool        `json:"is_public"`
    AccessLevel int         `json:"access_level"` // 1=Free, 2=Points, 3=Premium
    PointsCost  int         `json:"points_cost"`
    Downloads   int         `json:"downloads"`
    CreatedAt   time.Time   `json:"created_at"`
    UpdatedAt   time.Time   `json:"updated_at"`
    
    // Relationships
    Creator     User        `gorm:"foreignKey:CreatorID" json:"creator,omitempty"`
    Tags        []ResourceTag `gorm:"many2many:resource_tag_map;" json:"tags,omitempty"`
}

// ResourceTag represents a tag for categorizing resources
type ResourceTag struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    Name        string    `json:"name"`
    Slug        string    `json:"slug"`
    Description string    `json:"description"`
    CreatedAt   time.Time `json:"created_at"`
    
    // Relationships
    Resources   []Resource `gorm:"many2many:resource_tag_map;" json:"resources,omitempty"`
}

// ResourceDownload tracks resource downloads by users
type ResourceDownload struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ResourceID  uint      `json:"resource_id"`
    UserID      uint      `json:"user_id"`
    DownloadedAt time.Time `json:"downloaded_at"`
    
    // Relationships
    Resource    Resource  `gorm:"foreignKey:ResourceID" json:"resource,omitempty"`
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// ResourceRating stores user ratings for resources
type ResourceRating struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    ResourceID  uint      `json:"resource_id"`
    UserID      uint      `json:"user_id"`
    Rating      int       `json:"rating"` // 1-5 stars
    Comment     string    `json:"comment"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    // Relationships
    Resource    Resource  `gorm:"foreignKey:ResourceID" json:"resource,omitempty"`
    User        User      `gorm:"foreignKey:UserID" json:"user,omitempty"`
}
```

The model structure:
- Defines various resource types (documents, videos, links, etc.)
- Supports tagging for better organization
- Tracks downloads and user ratings
- Implements access control based on membership level

### Repository (`internal/resource/repository`)

```go
// ResourceRepository handles data access for resources
type ResourceRepository struct {
    db *gorm.DB
}

// NewResourceRepository creates a new repository instance
func NewResourceRepository(db *gorm.DB) *ResourceRepository {
    return &ResourceRepository{
        db: db,
    }
}

// GetResources retrieves resources with filtering and pagination
func (r *ResourceRepository) GetResources(filters map[string]interface{}, page, pageSize int) ([]models.Resource, int64, error) {
    var resources []models.Resource
    var total int64
    
    query := r.db.Model(&models.Resource{})
    
    // Apply filters
    for key, value := range filters {
        query = query.Where(key, value)
    }
    
    // Count total
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // Apply pagination
    offset := (page - 1) * pageSize
    if err := query.Offset(offset).Limit(pageSize).
        Preload("Creator", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Tags").
        Order("created_at DESC").Find(&resources).Error; err != nil {
        return nil, 0, err
    }
    
    return resources, total, nil
}

// GetResourceByID retrieves a resource by ID with preloaded relationships
func (r *ResourceRepository) GetResourceByID(id uint) (*models.Resource, error) {
    var resource models.Resource
    
    err := r.db.Preload("Creator", func(db *gorm.DB) *gorm.DB {
            return db.Select("id, username, full_name, profile_image")
        }).
        Preload("Tags").
        First(&resource, id).Error
    
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil
        }
        return nil, err
    }
    
    return &resource, nil
}

// CreateResource creates a new resource
func (r *ResourceRepository) CreateResource(resource *models.Resource, tagIDs []uint) error {
    return r.db.Transaction(func(tx *gorm.DB) error {
        // Create resource
        if err := tx.Create(resource).Error; err != nil {
            return err
        }
        
        // Add tags if provided
        if len(tagIDs) > 0 {
            for _, tagID := range tagIDs {
                if err := tx.Exec("INSERT INTO resource_tag_map (resource_id, resource_tag_id) VALUES (?, ?)", 
                    resource.ID, tagID).Error; err != nil {
                    return err
                }
            }
        }
        
        return nil
    })
}

// RecordResourceDownload records a resource download
func (r *ResourceRepository) RecordResourceDownload(resourceID, userID uint) error {
    // Update download count
    if err := r.db.Model(&models.Resource{}).
        Where("id = ?", resourceID).
        UpdateColumn("downloads", gorm.Expr("downloads + ?", 1)).Error; err != nil {
        return err
    }
    
    // Record download
    download := models.ResourceDownload{
        ResourceID:   resourceID,
        UserID:       userID,
        DownloadedAt: time.Now(),
    }
    
    return r.db.Create(&download).Error
}

// GetResourceTags retrieves all resource tags
func (r *ResourceRepository) GetResourceTags() ([]models.ResourceTag, error) {
    var tags []models.ResourceTag
    
    if err := r.db.Order("name ASC").Find(&tags).Error; err != nil {
        return nil, err
    }
    
    return tags, nil
}

// CreateResourceTag creates a new resource tag
func (r *ResourceRepository) CreateResourceTag(tag *models.ResourceTag) error {
    return r.db.Create(tag).Error
}

// Additional repository methods for ratings, user access, etc.
```

The repository layer:
- Implements data access for resources and related entities
- Supports filtering and pagination for resource listings
- Manages resource tags and download tracking
- Uses transactions for data consistency

### Service (`internal/resource/service`)

```go
// ResourceService implements business logic for the resource module
type ResourceService struct {
    repo        *repository.ResourceRepository
    userRepo    *repository.UserRepository
    pointsClient points.PointsServiceClient
    storageClient storage.StorageServiceClient
}

// NewResourceService creates a new service instance
func NewResourceService(
    repo *repository.ResourceRepository,
    userRepo *repository.UserRepository,
    pointsClient points.PointsServiceClient,
    storageClient storage.StorageServiceClient,
) *ResourceService {
    return &ResourceService{
        repo:         repo,
        userRepo:     userRepo,
        pointsClient: pointsClient,
        storageClient: storageClient,
    }
}

// GetPublicResources retrieves public resources with pagination
func (s *ResourceService) GetPublicResources(page, pageSize int) ([]models.Resource, int64, error) {
    filters := map[string]interface{}{
        "is_public": true,
    }
    return s.repo.GetResources(filters, page, pageSize)
}

// GetResourcesByBook retrieves resources for a specific book
func (s *ResourceService) GetResourcesByBook(bookID uint, page, pageSize int) ([]models.Resource, int64, error) {
    filters := map[string]interface{}{
        "book_id": bookID,
        "is_public": true,
    }
    return s.repo.GetResources(filters, page, pageSize)
}

// GetResourcesByTag retrieves resources with a specific tag
func (s *ResourceService) GetResourcesByTag(tagID uint, page, pageSize int) ([]models.Resource, int64, error) {
    return s.repo.GetResourcesByTag(tagID, page, pageSize)
}

// CreateResource creates a new resource
func (s *ResourceService) CreateResource(resource *models.Resource, tagIDs []uint, file *multipart.FileHeader) (*models.Resource, error) {
    // Validate resource data
    if resource.Title == "" {
        return nil, errors.New("resource title is required")
    }
    
    // Handle file upload if provided
    if file != nil {
        // Get file details
        resource.FileSize = file.Size
        resource.MimeType = file.Header.Get("Content-Type")
        
        // Upload file to storage
        uploadReq := &storage.UploadRequest{
            Filename:  file.Filename,
            Size:      file.Size,
            MimeType:  resource.MimeType,
            CreatorID: resource.CreatorID,
        }
        
        uploadResp, err := s.storageClient.UploadFile(context.Background(), uploadReq)
        if err != nil {
            return nil, fmt.Errorf("failed to upload file: %w", err)
        }
        
        // Set resource URL to uploaded file URL
        resource.URL = uploadResp.URL
    } else if resource.URL == "" {
        return nil, errors.New("either file or URL is required")
    }
    
    // Set default values
    resource.Downloads = 0
    resource.CreatedAt = time.Now()
    resource.UpdatedAt = time.Now()
    
    // Create resource
    if err := s.repo.CreateResource(resource, tagIDs); err != nil {
        return nil, err
    }
    
    // Award points for creating a resource
    go func() {
        _, err := s.pointsClient.AwardPoints(context.Background(), &points.AwardPointsRequest{
            UserId: resource.CreatorID,
            Points: 20,
            Reason: "Created an educational resource",
        })
        if err != nil {
            log.Printf("Failed to award points for resource creation: %v", err)
        }
    }()
    
    return resource, nil
}

// DownloadResource processes a resource download
func (s *ResourceService) DownloadResource(resourceID, userID uint) (string, error) {
    // Get resource details
    resource, err := s.repo.GetResourceByID(resourceID)
    if err != nil {
        return "", err
    }
    if resource == nil {
        return "", errors.New("resource not found")
    }
    
    // Check access permissions
    if !resource.IsPublic {
        // Check if user has access based on membership level
        user, err := s.userRepo.GetUserByID(userID)
        if err != nil {
            return "", err
        }
        if user == nil {
            return "", errors.New("user not found")
        }
        
        if user.MembershipLevel < resource.AccessLevel {
            return "", errors.New("insufficient membership level to access this resource")
        }
    }
    
    // Handle points-based resources
    if resource.PointsCost > 0 {
        // Check if user has already downloaded this resource
        hasDownloaded, err := s.repo.HasUserDownloadedResource(resourceID, userID)
        if err != nil {
            return "", err
        }
        
        // If not previously downloaded, check and deduct points
        if !hasDownloaded {
            // Verify user has enough points
            hasPoints, err := s.pointsClient.CheckUserHasPoints(context.Background(), &points.CheckPointsRequest{
                UserId: userID,
                Points: int32(resource.PointsCost),
            })
            if err != nil {
                return "", err
            }
            if !hasPoints.HasEnough {
                return "", errors.New("insufficient points to download this resource")
            }
            
            // Deduct points
            _, err = s.pointsClient.DeductPoints(context.Background(), &points.DeductPointsRequest{
                UserId: userID,
                Points: int32(resource.PointsCost),
                Reason: fmt.Sprintf("Downloaded resource: %s", resource.Title),
            })
            if err != nil {
                return "", err
            }
        }
    }
    
    // Record download
    if err := s.repo.RecordResourceDownload(resourceID, userID); err != nil {
        return "", err
    }
    
    // For external URLs, return the URL directly
    if resource.Type == models.ResourceTypeLink {
        return resource.URL, nil
    }
    
    // For stored files, generate a signed URL
    signedURL, err := s.storageClient.GetSignedURL(context.Background(), &storage.SignedURLRequest{
        URL:       resource.URL,
        ExpiresIn: 3600, // 1 hour
    })
    if err != nil {
        return "", err
    }
    
    return signedURL.URL, nil
}

// Additional service methods for ratings, tags, etc.
```

The service layer:
- Implements business logic for resource management
- Integrates with storage service for file uploads
- Manages access control based on membership level
- Handles points-based downloads and tracking

### Handlers (`internal/resource/handlers`)

```go
// ResourceHandler processes HTTP requests for the resource module
type ResourceHandler struct {
    service *service.ResourceService
}

// NewResourceHandler creates a new handler instance
func NewResourceHandler(service *service.ResourceService) *ResourceHandler {
    return &ResourceHandler{
        service: service,
    }
}

// RegisterRoutes sets up API routes for the resource module
func (h *ResourceHandler) RegisterRoutes(router *gin.RouterGroup) {
    resourcesGroup := router.Group("/resources")
    {
        resourcesGroup.GET("/public", h.GetPublicResources)
        resourcesGroup.GET("/tags", h.GetResourceTags)
        resourcesGroup.GET("/tags/:id", h.GetResourcesByTag)
        resourcesGroup.GET("/book/:id", h.GetResourcesByBook)
        resourcesGroup.GET("/:id", h.GetResourceByID)
        
        // Protected routes
        authorized := resourcesGroup.Group("/")
        authorized.Use(middleware.AuthRequired())
        {
            authorized.POST("/", h.CreateResource)
            authorized.GET("/download/:id", h.DownloadResource)
            authorized.POST("/:id/rating", h.RateResource)
            authorized.GET("/user", h.GetUserResources)
        }
        
        // Admin routes
        admin := resourcesGroup.Group("/admin")
        admin.Use(middleware.AdminRequired())
        {
            admin.POST("/tags", h.CreateResourceTag)
            admin.PUT("/:id", h.UpdateResource)
            admin.DELETE("/:id", h.DeleteResource)
            admin.GET("/stats", h.GetResourceStats)
        }
    }
}

// GetPublicResources returns public resources with pagination
func (h *ResourceHandler) GetPublicResources(c *gin.Context) {
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
    
    resources, total, err := h.service.GetPublicResources(page, pageSize)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve resources"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "resources": resources,
        "pagination": gin.H{
            "total":      total,
            "page":       page,
            "page_size":  pageSize,
            "total_pages": int(math.Ceil(float64(total) / float64(pageSize))),
        },
    })
}

// CreateResource handles resource creation with file upload
func (h *ResourceHandler) CreateResource(c *gin.Context) {
    // Parse form data
    if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
        c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form data"})
        return
    }
    
    // Get resource data
    var resource models.Resource
    if err := c.ShouldBind(&resource); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // Get tag IDs
    var tagIDs []uint
    tagIDsStr := c.PostForm("tag_ids")
    if tagIDsStr != "" {
        for _, idStr := range strings.Split(tagIDsStr, ",") {
            id, err := strconv.ParseUint(idStr, 10, 32)
            if err != nil {
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tag ID format"})
                return
            }
            tagIDs = append(tagIDs, uint(id))
        }
    }
    
    // Get file if uploaded
    var file *multipart.FileHeader
    uploadedFile, _ := c.FormFile("file")
    if uploadedFile != nil {
        file = uploadedFile
    }
    
    // Set creator ID from authenticated user
    userID := middleware.GetUserID(c)
    resource.CreatorID = userID
    
    // Create resource
    createdResource, err := h.service.CreateResource(&resource, tagIDs, file)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{
        "message": "Resource created successfully",
        "resource": createdResource,
    })
}

// DownloadResource handles resource download requests
func (h *ResourceHandler) DownloadResource(c *gin.Context) {
    resourceID, err := strconv.ParseUint(c.Param("id"), 10, 32)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid resource ID"})
        return
    }
    
    // Get user ID from authenticated user
    userID := middleware.GetUserID(c)
    
    // Process download
    downloadURL, err := h.service.DownloadResource(uint(resourceID), userID)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "message": "Resource download processed successfully",
        "download_url": downloadURL,
    })
}

// Additional handler methods for resource management, ratings, tags, etc.
```

The handler layer:
- Defines API routes for the resource module
- Processes resource creation with file uploads
- Handles resource downloads and access control
- Manages resource tags and ratings


## CODE_ANALYSIS_PART5_6.md

# Great Nigeria Project - Code Analysis (Part 5.6)

## Template Module

The Template module (`internal/template`) manages HTML templates and rendering for the platform.

### Template Engine (`internal/template/engine.go`)

```go
// Engine provides template rendering functionality
type Engine struct {
    templates  *template.Template
    funcMap    template.FuncMap
    config     *config.Config
    assetsPath string
}

// NewEngine creates a new template engine instance
func NewEngine(config *config.Config) *Engine {
    engine := &Engine{
        config:     config,
        assetsPath: config.AssetsPath,
        funcMap:    make(template.FuncMap),
    }
    
    // Register default template functions
    engine.registerDefaultFunctions()
    
    // Parse templates
    engine.parseTemplates()
    
    return engine
}

// registerDefaultFunctions adds built-in template functions
func (e *Engine) registerDefaultFunctions() {
    e.funcMap["formatDate"] = func(t time.Time) string {
        return t.Format("January 2, 2006")
    }
    
    e.funcMap["formatDateTime"] = func(t time.Time) string {
        return t.Format("January 2, 2006 3:04 PM")
    }
    
    e.funcMap["truncate"] = func(s string, length int) string {
        if len(s) <= length {
            return s
        }
        return s[:length] + "..."
    }
    
    e.funcMap["markdown"] = func(s string) template.HTML {
        renderer := blackfriday.NewHTMLRenderer(blackfriday.HTMLRendererParameters{
            Flags: blackfriday.CommonHTMLFlags,
        })
        parser := blackfriday.New(blackfriday.WithRenderer(renderer))
        html := parser.Parse([]byte(s))
        return template.HTML(html.String())
    }
    
    e.funcMap["asset"] = func(path string) string {
        return "/static/" + path
    }
    
    e.funcMap["config"] = func(key string) interface{} {
        return e.config.Get(key)
    }
    
    // Additional helper functions...
}

// parseTemplates loads and parses all templates
func (e *Engine) parseTemplates() {
    // Parse base layouts
    layouts, err := filepath.Glob(filepath.Join(e.assetsPath, "templates/layouts/*.html"))
    if err != nil {
        log.Fatalf("Failed to find layout templates: %v", err)
    }
    
    // Parse page templates
    pages, err := filepath.Glob(filepath.Join(e.assetsPath, "templates/pages/*.html"))
    if err != nil {
        log.Fatalf("Failed to find page templates: %v", err)
    }
    
    // Parse partial templates
    partials, err := filepath.Glob(filepath.Join(e.assetsPath, "templates/partials/*.html"))
    if err != nil {
        log.Fatalf("Failed to find partial templates: %v", err)
    }
    
    // Combine all template files
    templateFiles := append(layouts, pages...)
    templateFiles = append(templateFiles, partials...)
    
    // Parse templates with function map
    e.templates, err = template.New("").Funcs(e.funcMap).ParseFiles(templateFiles...)
    if err != nil {
        log.Fatalf("Failed to parse templates: %v", err)
    }
}

// Render renders a template with the given data
func (e *Engine) Render(w http.ResponseWriter, name string, data interface{}) error {
    // Create a buffer to catch template rendering errors
    buf := new(bytes.Buffer)
    
    // Execute template into buffer
    if err := e.templates.ExecuteTemplate(buf, name, data); err != nil {
        return err
    }
    
    // Set content type and write response
    w.Header().Set("Content-Type", "text/html; charset=utf-8")
    _, err := buf.WriteTo(w)
    return err
}

// RegisterFunction adds a custom template function
func (e *Engine) RegisterFunction(name string, fn interface{}) {
    e.funcMap[name] = fn
    e.parseTemplates() // Re-parse templates to include new function
}
```

The template engine:
- Manages HTML templates with layouts, pages, and partials
- Provides helper functions for common formatting tasks
- Supports markdown rendering for content
- Implements error handling for template rendering

### Template Middleware (`internal/template/middleware.go`)

```go
// TemplateMiddleware provides template-related middleware functions
type TemplateMiddleware struct {
    engine *Engine
    config *config.Config
}

// NewTemplateMiddleware creates a new middleware instance
func NewTemplateMiddleware(engine *Engine, config *config.Config) *TemplateMiddleware {
    return &TemplateMiddleware{
        engine: engine,
        config: config,
    }
}

// TemplateContext adds common template context data
func (m *TemplateMiddleware) TemplateContext() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Get user from context if authenticated
        var user *models.User
        userValue, exists := c.Get("user")
        if exists {
            user = userValue.(*models.User)
        }
        
        // Create template context
        templateContext := map[string]interface{}{
            "AppName":       m.config.AppName,
            "AppVersion":    m.config.AppVersion,
            "CurrentYear":   time.Now().Year(),
            "CurrentUser":   user,
            "IsProduction":  m.config.Environment == "production",
            "CurrentPath":   c.Request.URL.Path,
            "QueryParams":   c.Request.URL.Query(),
            "CSRFToken":     c.GetString("csrf_token"),
            "FlashMessages": getFlashMessages(c),
        }
        
        // Add template context to Gin context
        c.Set("TemplateContext", templateContext)
        
        c.Next()
    }
}

// getFlashMessages retrieves flash messages from session
func getFlashMessages(c *gin.Context) map[string][]string {
    session := sessions.Default(c)
    flashes := session.Flashes()
    
    messages := make(map[string][]string)
    
    for _, flash := range flashes {
        if flashMap, ok := flash.(map[string]string); ok {
            category := flashMap["category"]
            message := flashMap["message"]
            
            if _, exists := messages[category]; !exists {
                messages[category] = []string{}
            }
            
            messages[category] = append(messages[category], message)
        }
    }
    
    session.Save()
    
    return messages
}

// SetFlashMessage adds a flash message to the session
func SetFlashMessage(c *gin.Context, category, message string) {
    session := sessions.Default(c)
    session.AddFlash(map[string]string{
        "category": category,
        "message":  message,
    })
    session.Save()
}
```

The template middleware:
- Adds common context data to all templates
- Manages user authentication state in templates
- Implements flash messaging for user notifications
- Provides CSRF protection for forms

## Web Components

The web components in the `/web` directory implement the frontend of the platform.

### Book Viewer (`web/static/js/book-viewer.js`)

```javascript
/**
 * Book Viewer Component
 * Handles the interactive book reading experience
 */
class BookViewer {
    constructor(options) {
        this.container = document.getElementById(options.containerId);
        this.bookId = options.bookId;
        this.chapterId = options.chapterId;
        this.sectionId = options.sectionId;
        this.userId = options.userId;
        this.apiBaseUrl = options.apiBaseUrl || '/api';
        this.currentPosition = 0;
        this.totalLength = 0;
        this.bookContent = null;
        this.bookmarks = [];
        this.notes = [];
        this.progressInterval = null;
        this.progressUpdateDelay = 5000; // 5 seconds
        
        // Initialize viewer
        this.init();
    }
    
    /**
     * Initialize the book viewer
     */
    async init() {
        try {
            // Load content
            await this.loadContent();
            
            // Load user progress
            if (this.userId) {
                await this.loadUserProgress();
                await this.loadBookmarks();
                await this.loadNotes();
            }
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Start progress tracking
            this.startProgressTracking();
        } catch (error) {
            console.error('Failed to initialize book viewer:', error);
            this.showError('Failed to load book content. Please try again later.');
        }
    }
    
    /**
     * Load book content from API
     */
    async loadContent() {
        const url = `${this.apiBaseUrl}/books/${this.bookId}/sections/${this.sectionId}`;
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`Failed to load content: ${response.statusText}`);
        }
        
        const data = await response.json();
        this.bookContent = data.content;
        this.totalLength = this.bookContent.length;
        
        // Render content
        this.renderContent();
    }
    
    /**
     * Load user's reading progress
     */
    async loadUserProgress() {
        const url = `${this.apiBaseUrl}/books/${this.bookId}/progress?section_id=${this.sectionId}`;
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${this.getAuthToken()}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.progress) {
                this.currentPosition = data.progress.position || 0;
                this.scrollToPosition(this.currentPosition);
            }
        }
    }
    
    /**
     * Render book content
     */
    renderContent() {
        if (!this.bookContent) return;
        
        // Process content (convert markdown, handle interactive elements)
        const processedContent = this.processContent(this.bookContent);
        
        // Update container
        this.container.innerHTML = processedContent;
        
        // Initialize interactive elements
        this.initializeInteractiveElements();
    }
    
    /**
     * Process content for rendering
     */
    processContent(content) {
        // Convert markdown to HTML
        let html = marked(content);
        
        // Process interactive elements
        html = this.processInteractiveElements(html);
        
        // Process citations
        html = this.processCitations(html);
        
        return html;
    }
    
    /**
     * Process interactive elements in content
     */
    processInteractiveElements(html) {
        // Replace quiz placeholders with interactive quizzes
        html = html.replace(/\{\{quiz:([^}]+)\}\}/g, (match, quizData) => {
            const quiz = JSON.parse(quizData);
            return this.renderQuiz(quiz);
        });
        
        // Replace reflection placeholders with reflection components
        html = html.replace(/\{\{reflection:([^}]+)\}\}/g, (match, reflectionData) => {
            const reflection = JSON.parse(reflectionData);
            return this.renderReflection(reflection);
        });
        
        // Replace action step placeholders
        html = html.replace(/\{\{action:([^}]+)\}\}/g, (match, actionData) => {
            const action = JSON.parse(actionData);
            return this.renderActionStep(action);
        });
        
        return html;
    }
    
    /**
     * Initialize interactive elements after rendering
     */
    initializeInteractiveElements() {
        // Initialize quizzes
        document.querySelectorAll('.book-quiz').forEach(quizElement => {
            new BookQuiz(quizElement);
        });
        
        // Initialize reflections
        document.querySelectorAll('.book-reflection').forEach(reflectionElement => {
            new BookReflection(reflectionElement);
        });
        
        // Initialize action steps
        document.querySelectorAll('.book-action-step').forEach(actionElement => {
            new BookActionStep(actionElement);
        });
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Scroll event for progress tracking
        this.container.addEventListener('scroll', this.handleScroll.bind(this));
        
        // Bookmark button
        document.getElementById('bookmark-button').addEventListener('click', this.handleBookmark.bind(this));
        
        // Note button
        document.getElementById('note-button').addEventListener('click', this.handleNote.bind(this));
        
        // Navigation buttons
        document.getElementById('prev-section').addEventListener('click', this.navigateToPrevSection.bind(this));
        document.getElementById('next-section').addEventListener('click', this.navigateToNextSection.bind(this));
    }
    
    /**
     * Handle scroll events
     */
    handleScroll() {
        // Calculate current position
        const scrollTop = this.container.scrollTop;
        const scrollHeight = this.container.scrollHeight - this.container.clientHeight;
        const scrollPercentage = (scrollTop / scrollHeight) * 100;
        
        // Update progress bar
        document.getElementById('progress-bar').style.width = `${scrollPercentage}%`;
        
        // Update current position
        this.currentPosition = Math.floor(scrollTop);
    }
    
    /**
     * Start tracking reading progress
     */
    startProgressTracking() {
        this.progressInterval = setInterval(() => {
            this.updateProgress();
        }, this.progressUpdateDelay);
    }
    
    /**
     * Update reading progress on server
     */
    async updateProgress() {
        if (!this.userId) return;
        
        const scrollHeight = this.container.scrollHeight - this.container.clientHeight;
        const scrollPercentage = (this.currentPosition / scrollHeight) * 100;
        
        try {
            const url = `${this.apiBaseUrl}/books/${this.bookId}/progress`;
            await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    chapter_id: this.chapterId,
                    section_id: this.sectionId,
                    position: this.currentPosition,
                    percent_complete: scrollPercentage
                })
            });
        } catch (error) {
            console.error('Failed to update reading progress:', error);
        }
    }
    
    // Additional methods for bookmarks, notes, navigation, etc.
}

// Initialize book viewer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const viewerOptions = JSON.parse(document.getElementById('viewer-options').textContent);
    new BookViewer(viewerOptions);
});
```

The book viewer component:
- Manages the interactive reading experience
- Tracks reading progress and syncs with the server
- Handles interactive elements like quizzes and reflections
- Supports bookmarks and notes

### Celebration Component (`web/static/js/celebration.js`)

```javascript
/**
 * Celebration Component
 * Handles the "Celebrate Nigeria" feature
 */
class CelebrationComponent {
    constructor(options) {
        this.container = document.getElementById(options.containerId);
        this.apiBaseUrl = options.apiBaseUrl || '/api';
        this.userId = options.userId;
        this.categories = [];
        this.entries = [];
        this.currentCategory = null;
        this.currentPage = 1;
        this.pageSize = 12;
        this.totalEntries = 0;
        
        // Initialize component
        this.init();
    }
    
    /**
     * Initialize the celebration component
     */
    async init() {
        try {
            // Load categories
            await this.loadCategories();
            
            // Load featured entries
            await this.loadFeaturedEntries();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Render initial view
            this.renderCategories();
            this.renderFeaturedEntries();
        } catch (error) {
            console.error('Failed to initialize celebration component:', error);
            this.showError('Failed to load celebration content. Please try again later.');
        }
    }
    
    /**
     * Load celebration categories
     */
    async loadCategories() {
        const url = `${this.apiBaseUrl}/celebrate/categories?hierarchical=true`;
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`Failed to load categories: ${response.statusText}`);
        }
        
        const data = await response.json();
        this.categories = data.categories;
    }
    
    /**
     * Load featured celebration entries
     */
    async loadFeaturedEntries() {
        const url = `${this.apiBaseUrl}/celebrate/featured?limit=6`;
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`Failed to load featured entries: ${response.statusText}`);
        }
        
        const data = await response.json();
        this.featuredEntries = data.entries;
    }
    
    /**
     * Load entries for a specific category
     */
    async loadCategoryEntries(categoryId, page = 1) {
        const url = `${this.apiBaseUrl}/celebrate/entries?category_id=${categoryId}&page=${page}&page_size=${this.pageSize}`;
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`Failed to load category entries: ${response.statusText}`);
        }
        
        const data = await response.json();
        this.entries = data.entries;
        this.totalEntries = data.pagination.total;
        this.currentPage = page;
        this.currentCategory = categoryId;
        
        // Render entries
        this.renderEntries();
        this.renderPagination();
    }
    
    /**
     * Render celebration categories
     */
    renderCategories() {
        const categoriesContainer = document.getElementById('celebration-categories');
        
        let html = '<div class="categories-grid">';
        this.categories.forEach(category => {
            html += `
                <div class="category-card" data-category-id="${category.id}">
                    <div class="category-image">
                        <img src="${category.image_url || '/static/img/default-category.jpg'}" alt="${category.name}">
                    </div>
                    <div class="category-info">
                        <h3>${category.name}</h3>
                        <p>${category.description}</p>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        categoriesContainer.innerHTML = html;
    }
    
    /**
     * Render featured celebration entries
     */
    renderFeaturedEntries() {
        const featuredContainer = document.getElementById('celebration-featured');
        
        let html = '<div class="featured-slider">';
        this.featuredEntries.forEach(entry => {
            html += `
                <div class="featured-slide" data-entry-id="${entry.id}">
                    <div class="featured-image">
                        <img src="${entry.image_url || '/static/img/default-entry.jpg'}" alt="${entry.title}">
                    </div>
                    <div class="featured-info">
                        <h3>${entry.title}</h3>
                        <p>${entry.description}</p>
                        <span class="featured-category">${entry.category.name}</span>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        featuredContainer.innerHTML = html;
        
        // Initialize slider
        new Swiper('.featured-slider', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
        });
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Category selection
        document.addEventListener('click', event => {
            const categoryCard = event.target.closest('.category-card');
            if (categoryCard) {
                const categoryId = categoryCard.dataset.categoryId;
                this.loadCategoryEntries(categoryId);
            }
        });
        
        // Entry details
        document.addEventListener('click', event => {
            const entryCard = event.target.closest('.entry-card, .featured-slide');
            if (entryCard) {
                const entryId = entryCard.dataset.entryId;
                this.showEntryDetails(entryId);
            }
        });
        
        // Pagination
        document.addEventListener('click', event => {
            const pageLink = event.target.closest('.page-link');
            if (pageLink) {
                event.preventDefault();
                const page = parseInt(pageLink.dataset.page);
                this.loadCategoryEntries(this.currentCategory, page);
            }
        });
        
        // Nomination form
        document.getElementById('nominate-button').addEventListener('click', () => {
            this.showNominationForm();
        });
    }
    
    // Additional methods for entry details, nominations, etc.
}

// Initialize celebration component when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const celebrationOptions = JSON.parse(document.getElementById('celebration-options').textContent);
    new CelebrationComponent(celebrationOptions);
});
```

The celebration component:
- Manages the "Celebrate Nigeria" feature
- Displays categories and entries in a responsive grid
- Supports pagination and filtering
- Handles nominations and entry details

## Frontend-Backend Integration

The integration between frontend components and backend services is managed through several key mechanisms:

### API Client (`web/static/js/api-client.js`)

```javascript
/**
 * API Client
 * Handles communication with the backend API
 */
class ApiClient {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || '/api';
        this.authToken = options.authToken || this.getStoredToken();
    }
    
    /**
     * Get stored authentication token
     */
    getStoredToken() {
        return localStorage.getItem('auth_token');
    }
    
    /**
     * Set authentication token
     */
    setAuthToken(token) {
        this.authToken = token;
        localStorage.setItem('auth_token', token);
    }
    
    /**
     * Clear authentication token
     */
    clearAuthToken() {
        this.authToken = null;
        localStorage.removeItem('auth_token');
    }
    
    /**
     * Get request headers
     */
    getHeaders(includeAuth = true) {
        const headers = {
            'Content-Type': 'application/json',
        };
        
        if (includeAuth && this.authToken) {
            headers['Authorization'] = `Bearer ${this.authToken}`;
        }
        
        return headers;
    }
    
    /**
     * Make API request
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const method = options.method || 'GET';
        const includeAuth = options.includeAuth !== false;
        const headers = this.getHeaders(includeAuth);
        
        const requestOptions = {
            method,
            headers,
            credentials: 'same-origin',
        };
        
        if (options.body) {
            requestOptions.body = JSON.stringify(options.body);
        }
        
        try {
            const response = await fetch(url, requestOptions);
            
            // Handle authentication errors
            if (response.status === 401 && includeAuth) {
                this.clearAuthToken();
                // Redirect to login page
                window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
                return null;
            }
            
            // Parse response
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'API request failed');
            }
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
    
    /**
     * GET request
     */
    async get(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'GET' });
    }
    
    /**
     * POST request
     */
    async post(endpoint, body, options = {}) {
        return this.request(endpoint, { ...options, method: 'POST', body });
    }
    
    /**
     * PUT request
     */
    async put(endpoint, body, options = {}) {
        return this.request(endpoint, { ...options, method: 'PUT', body });
    }
    
    /**
     * DELETE request
     */
    async delete(endpoint, options = {}) {
        return this.request(endpoint, { ...options, method: 'DELETE' });
    }
}

// Create global API client instance
window.apiClient = new ApiClient();
```

The API client:
- Provides a consistent interface for API communication
- Handles authentication and token management
- Implements error handling and redirects
- Supports all HTTP methods needed for the application

### WebSocket Integration (`web/static/js/websocket.js`)

```javascript
/**
 * WebSocket Client
 * Handles real-time communication with the backend
 */
class WebSocketClient {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || this.getWebSocketUrl();
        this.authToken = options.authToken || apiClient.getStoredToken();
        this.socket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.eventHandlers = {};
        
        // Initialize connection
        if (this.authToken) {
            this.connect();
        }
    }
    
    /**
     * Get WebSocket URL based on current environment
     */
    getWebSocketUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        return `${protocol}//${host}/ws`;
    }
    
    /**
     * Connect to WebSocket server
     */
    connect() {
        if (this.socket) {
            this.socket.close();
        }
        
        const url = `${this.baseUrl}?token=${this.authToken}`;
        this.socket = new WebSocket(url);
        
        this.socket.onopen = this.handleOpen.bind(this);
        this.socket.onmessage = this.handleMessage.bind(this);
        this.socket.onclose = this.handleClose.bind(this);
        this.socket.onerror = this.handleError.bind(this);
    }
    
    /**
     * Handle WebSocket open event
     */
    handleOpen() {
        console.log('WebSocket connection established');
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
        
        // Send authentication message
        this.send('authenticate', { token: this.authToken });
    }
    
    /**
     * Handle WebSocket message event
     */
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            const { type, data } = message;
            
            // Trigger event handlers
            if (this.eventHandlers[type]) {
                this.eventHandlers[type].forEach(handler => {
                    handler(data);
                });
            }
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }
    
    /**
     * Handle WebSocket close event
     */
    handleClose(event) {
        console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);
        
        // Attempt to reconnect if not a clean close
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            this.reconnectDelay *= 2; // Exponential backoff
            
            console.log(`Attempting to reconnect in ${this.reconnectDelay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectDelay);
        }
    }
    
    /**
     * Handle WebSocket error event
     */
    handleError(error) {
        console.error('WebSocket error:', error);
    }
    
    /**
     * Send message to WebSocket server
     */
    send(type, data) {
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
            console.error('Cannot send message: WebSocket is not connected');
            return false;
        }
        
        const message = JSON.stringify({ type, data });
        this.socket.send(message);
        return true;
    }
    
    /**
     * Register event handler
     */
    on(type, handler) {
        if (!this.eventHandlers[type]) {
            this.eventHandlers[type] = [];
        }
        
        this.eventHandlers[type].push(handler);
    }
    
    /**
     * Remove event handler
     */
    off(type, handler) {
        if (!this.eventHandlers[type]) return;
        
        if (handler) {
            this.eventHandlers[type] = this.eventHandlers[type].filter(h => h !== handler);
        } else {
            delete this.eventHandlers[type];
        }
    }
}

// Create global WebSocket client instance
window.wsClient = new WebSocketClient();
```

The WebSocket client:
- Provides real-time communication for features like chat and notifications
- Handles authentication and reconnection
- Implements event-based message handling
- Supports multiple concurrent event listeners

## Conclusion

This analysis covers the additional modules and components that were not thoroughly documented in the previous parts. The Celebration, Gifts, Project, Report, Resource, and Template modules provide essential functionality for the Great Nigeria platform, while the web components implement the frontend user experience.

The modular architecture allows for independent development and scaling of each component, while the integration mechanisms ensure seamless communication between the frontend and backend systems. This approach supports the platform's goals of providing an engaging, educational, and community-driven experience.
