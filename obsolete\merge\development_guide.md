

## AI_AGENT_INSTRUCTIONS.md

# Great Nigeria Library - AI Agent Instructions

This document provides instructions for AI agents working on the Great Nigeria Library project.

## Project Organization Standards

When working on this project, please follow these organization standards:

1. **Directory Structure**:
   - cmd/: Service entry points
   - database/: Database backups and scripts
   - docs/: Documentation
     - rchitecture/: System architecture documentation
     - code/: Code documentation and analysis
     - content/: Book content structure and guidelines
     - development/: Development guides and standards
     - eatures/: Feature specifications
     - project/: Project management documentation
   - internal/: Core business logic
   - obsolete/: Obsolete files
   - pkg/: Shared utilities
   - scripts/: Utility scripts
   - web/: Frontend assets

2. **Documentation Updates**:
   - When implementing a new feature, update the task list in docs/project/TASK_LIST.md
   - Add the file location of the implementation to the task list
   - Update relevant documentation files to reflect the changes

3. **Code Organization**:
   - Place new code in the appropriate directories based on functionality
   - Follow the established naming conventions
   - Group related functionality together

4. **Obsolete Files**:
   - Do not delete files that are no longer needed
   - Move them to the obsolete directory instead

5. **Script Usage**:
   - Use the scripts in the scripts directory for common tasks
   - Run scripts from the root directory of the project

## Task Completion Checklist

When completing a task, follow this checklist:

1. **Update Task List**:
   - Mark the task as completed in docs/project/TASK_LIST.md
   - Add the file location of the implementation

2. **Update Documentation**:
   - Update relevant documentation files to reflect the changes
   - Add any new documentation needed for the feature

3. **Test the Feature**:
   - Write tests for the feature
   - Run the tests to ensure they pass

4. **Update README**:
   - If the feature is significant, update the main README.md file

## Critical Files

The following files are critical to the project and should not be modified without careful consideration:

- Core service files in internal/ directory
- Frontend files in web/static/ directory
- Common utilities in pkg/common/ directory
- Nigerian Virtual Gifts System files
- Celebrate Nigeria Feature files

See docs/project/TASK_LIST.md for a complete list of implemented features and their file locations.


## CELEBRATE_NIGERIA_DATA_POPULATION_GUIDE.md

# Celebrate Nigeria Data Population Guide

This guide explains how to run the data population script for the Celebrate Nigeria feature to import initial data into the database.

## Prerequisites

Before running the data population script, ensure you have:

1. A running PostgreSQL database with the correct schema
2. The Go programming language installed (version 1.16 or higher)
3. Environment variables set up in a `.env` file with database connection details
4. Proper permissions to write to the database

## Database Configuration

The script uses the following environment variables for database connection:

```
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=greatnigeria
```

Make sure these are set in your `.env` file or in your environment.

## Directory Structure

The script will create the following directory structure for images:

```
web/static/images/celebrate/
├── people/
├── places/
└── events/
```

## Running the Script

### Option 1: Using the Convenience Script

The easiest way to run the data population is to use the provided convenience script:

```bash
# Make the script executable
chmod +x scripts/run_celebrate_nigeria_population.sh

# Run the script
./scripts/run_celebrate_nigeria_population.sh
```

This script will:
1. Create the necessary image directories
2. Build the Go data population script
3. Run the script to populate the database
4. Clean up temporary files

### Option 2: Manual Execution

If you prefer to run the steps manually:

1. Create the image directories:
   ```bash
   chmod +x scripts/create_celebrate_image_dirs.sh
   ./scripts/create_celebrate_image_dirs.sh
   ```

2. Build and run the data population script:
   ```bash
   go build -o populate_celebrate_nigeria scripts/populate_celebrate_nigeria.go
   ./populate_celebrate_nigeria
   ```

3. Clean up:
   ```bash
   rm -f populate_celebrate_nigeria
   ```

## What Data Gets Imported

The script will import the following data:

### People
- Chinua Achebe (Nigerian novelist, poet, and critic)
- Wole Soyinka (Nobel Prize-winning playwright and poet)
- Ngozi Okonjo-Iweala (Economist and WTO Director-General)

### Places
- Zuma Rock (Impressive monolith near Abuja)
- Osun-Osogbo Sacred Grove (UNESCO World Heritage Site)
- Eko Atlantic City (Urban development project in Lagos)

### Events
- Eyo Festival (Lagos cultural tradition with masquerades)
- Nigeria Independence Day (Annual celebration on October 1)
- Lagos International Jazz Festival (Annual music festival)

Each entry includes:
- Basic information (title, description, etc.)
- Category associations
- Key facts
- Media items (images)
- Type-specific data (birth dates for people, coordinates for places, etc.)

## Verifying the Import

After running the script, you can verify the data was imported correctly by:

1. Checking the database tables:
   ```sql
   SELECT COUNT(*) FROM celebration_entries;
   SELECT COUNT(*) FROM person_entries;
   SELECT COUNT(*) FROM place_entries;
   SELECT COUNT(*) FROM event_entries;
   ```

2. Accessing the Celebrate Nigeria feature in your browser:
   - Main page: `http://localhost:5000/celebrate`
   - People: `http://localhost:5000/celebrate/person/chinua-achebe`
   - Places: `http://localhost:5000/celebrate/place/zuma-rock`
   - Events: `http://localhost:5000/celebrate/event/eyo-festival`

## Troubleshooting

### Common Issues

**Database Connection Errors**
- Verify your database is running
- Check the connection details in your `.env` file
- Ensure the database user has proper permissions

**Missing Tables**
- Make sure you've run the database migrations
- Check if the schema exists and is correctly set up

**Image Directory Issues**
- Verify you have write permissions to the web/static directory
- Check if the directories were created correctly

### Logs

The script outputs detailed logs to help diagnose issues:
- Successful operations are logged with "Successfully inserted..."
- Errors are logged with detailed error messages

## Adding Custom Data

To add your own custom data, you can modify the `scripts/populate_celebrate_nigeria.go` file:

1. Add new entries to the appropriate arrays:
   - `people` array for person entries
   - `places` array for place entries
   - `events` array for event entries

2. Follow the existing structure for each entry type

3. Run the script again to import your custom data

## Conclusion

After successfully running the data population script, the Celebrate Nigeria feature will have initial data that showcases various aspects of Nigerian culture, history, and achievements. This provides a foundation that can be expanded with more entries over time.

For any issues not covered in this guide, please refer to the technical documentation or contact the development team.


## DATABASE_SCHEMA_PART1.md

# Great Nigeria Platform - Database Schema (Part 1)

## Overview

This document outlines the database schema for the Great Nigeria platform using PostgreSQL with GORM as the ORM. The database is designed to support all aspects of the platform, including content management, user interactions, and payment processing.

## Table of Contents

1. [Overview](#overview)
2. [Entity Relationship Diagram](#entity-relationship-diagram)
3. [Core Tables](#core-tables)
   - [Users](#users)
   - [Books](#books)
   - [Chapters](#chapters)
   - [Sections](#sections)
4. [User Progress and Engagement](#user-progress-and-engagement)
   - [UserProgress](#userprogress)
   - [Bookmarks](#bookmarks)
5. [Discussion System](#discussion-system)
   - [Discussions](#discussions)
   - [Comments](#comments)
   - [UserLikes](#userlikes)
6. [Points and Rewards System](#points-and-rewards-system)
   - [UserActivities](#useractivities)
   - [TopicCompletions](#topiccompletions)
   - [MembershipLevels](#membershiplevels)
7. [Payment System](#payment-system)
   - [Purchases](#purchases)
   - [Plans](#plans)
   - [Subscriptions](#subscriptions)
8. [Citation System](#citation-system)
9. [Database Management](#database-management)
10. [Backup and Restoration](#backup-and-restoration)

## Entity Relationship Diagram

```
+-------------+     +-------------+     +-------------+
|    Users    |<--->| UserProgress|<--->|    Books    |
+-------------+     +-------------+     +-------------+
      ^  ^                                  ^
      |  |                                  |
      |  v                                  v
+-------------+     +-------------+     +-------------+
|   Comments  |<--->| Discussions |<--->|  Bookmarks  |
+-------------+     +-------------+     +-------------+
      ^                   ^
      |                   |
      v                   v
+-------------+     +-------------+
|UserActivities|     | UserLikes  |
+-------------+     +-------------+
      ^
      |
      v
+-------------+     +-------------+
|TopicCompletion|    | Purchases  |
+-------------+     +-------------+
```

## Core Tables

### Users

The Users table stores all user account information, including authentication details, profile information, and membership status.

```go
type User struct {
    ID              uint `gorm:"primaryKey"`
    Username        string `gorm:"size:50;not null;unique"`
    Email           string `gorm:"size:100;not null;unique"`
    Password        string `gorm:"size:255;not null"`
    FullName        string `gorm:"size:100"`
    Bio             string `gorm:"type:text"`
    ProfileImage    string `gorm:"size:255"`
    MembershipLevel int `gorm:"default:1"` // 1=Basic, 2=Engaged, 3=Active, 4=Premium
    PointsBalance   int `gorm:"default:0"`
    IsActive        bool `gorm:"default:true"`
    IsAdmin         bool `gorm:"default:false"`
    LastLogin       time.Time
    CreatedAt       time.Time
    UpdatedAt       time.Time
    DeletedAt       gorm.DeletedAt `gorm:"index"`
    
    // Relationships
    Progress        []UserProgress `gorm:"foreignKey:UserID"`
    Bookmarks       []Bookmark `gorm:"foreignKey:UserID"`
    Discussions     []Discussion `gorm:"foreignKey:UserID"`
    Comments        []Comment `gorm:"foreignKey:UserID"`
    Activities      []UserActivity `gorm:"foreignKey:UserID"`
    Likes           []UserLike `gorm:"foreignKey:UserID"`
    Completions     []TopicCompletion `gorm:"foreignKey:UserID"`
    Purchases       []Purchase `gorm:"foreignKey:UserID"`
}
```

**Key Features:**
- Unique username and email for identification
- Secure password storage (hashed)
- Profile information (full name, bio, profile image)
- Membership level tracking
- Points balance for the rewards system
- Soft delete support via DeletedAt

### Books

The Books table stores information about the books available on the platform, including metadata and access control settings.

```go
type Book struct {
    ID          uint `gorm:"primaryKey"`
    Title       string `gorm:"size:255;not null"`
    Description string `gorm:"type:text"`
    Author      string `gorm:"size:100"`
    CoverImage  string `gorm:"size:255"`
    AccessLevel int `gorm:"default:1"` // 1=Free, 2=Points, 3=Premium
    PointsRequired int `gorm:"default:0"`
    PublishedAt time.Time
    CreatedAt   time.Time
    UpdatedAt   time.Time
    DeletedAt   gorm.DeletedAt `gorm:"index"`
    
    // Relationships
    Chapters    []Chapter `gorm:"foreignKey:BookID"`
    Bookmarks   []Bookmark `gorm:"foreignKey:BookID"`
}
```

**Key Features:**
- Book metadata (title, description, author)
- Cover image URL
- Access level control (free, points-based, premium)
- Points required for access (if applicable)
- Publication tracking
- Relationship to chapters

### Chapters

The Chapters table organizes books into chapters, providing a hierarchical structure for content.

```go
type Chapter struct {
    ID          uint `gorm:"primaryKey"`
    BookID      uint `gorm:"index;not null"`
    Title       string `gorm:"size:255;not null"`
    OrderIndex  int `gorm:"not null"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    Book        Book
    Sections    []Section `gorm:"foreignKey:ChapterID"`
}
```

**Key Features:**
- Association with a specific book
- Title for the chapter
- Order index for proper sequencing
- Relationship to sections

### Sections

The Sections table contains the actual content of the books, organized within chapters.

```go
type Section struct {
    ID          uint `gorm:"primaryKey"`
    ChapterID   uint `gorm:"index;not null"`
    Title       string `gorm:"size:255;not null"`
    Content     string `gorm:"type:text"`
    OrderIndex  int `gorm:"not null"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    Chapter     Chapter
}
```

**Key Features:**
- Association with a specific chapter
- Title for the section
- Content storage (text/HTML)
- Order index for proper sequencing

## User Progress and Engagement

### UserProgress

The UserProgress table tracks a user's progress through the books, including their last position and completion percentage.

```go
type UserProgress struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    BookID      uint `gorm:"index;not null"`
    ChapterID   uint `gorm:"index"`
    SectionID   uint `gorm:"index"`
    LastPosition int
    PercentComplete float64 `gorm:"default:0"`
    LastUpdated time.Time
    CreatedAt   time.Time
    
    // Relationships
    User        User
    Book        Book
}
```

**Key Features:**
- Tracks which user is reading which book
- Records the specific chapter and section
- Stores the last position within a section
- Calculates overall completion percentage
- Timestamps for progress tracking

### Bookmarks

The Bookmarks table allows users to save specific locations within books for later reference.

```go
type Bookmark struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    BookID      uint `gorm:"index;not null"`
    ChapterID   uint `gorm:"index"`
    SectionID   uint `gorm:"index"`
    Position    int
    Note        string `gorm:"type:text"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    User        User
    Book        Book
}
```

**Key Features:**
- Associates a bookmark with a specific user
- Records the book, chapter, and section
- Stores the exact position within a section
- Allows users to add notes to bookmarks


## DATABASE_SCHEMA_PART2.md

# Great Nigeria Platform - Database Schema (Part 2)

## Discussion System

### Discussions

The Discussions table stores forum topics and other discussion-based content, with links to relevant book sections.

```go
type DiscussionType string

const (
    DiscussionTypeGeneral      DiscussionType = "general"
    DiscussionTypeForumTopic   DiscussionType = "forum_topic"
    DiscussionTypeActionableStep DiscussionType = "actionable_step"
)

type Discussion struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    Title       string `gorm:"size:255;not null"`
    Content     string `gorm:"type:text;not null"`
    Type        DiscussionType `gorm:"type:varchar(20);default:'general'"`
    BookID      uint `gorm:"index"`
    ChapterID   uint `gorm:"index"`
    SectionID   uint `gorm:"index"`
    Views       int `gorm:"default:0"`
    IsSticky    bool `gorm:"default:false"`
    IsClosed    bool `gorm:"default:false"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    DeletedAt   gorm.DeletedAt `gorm:"index"`
    
    // Relationships
    User        User
    Comments    []Comment `gorm:"foreignKey:DiscussionID"`
    Likes       []UserLike `gorm:"foreignKey:DiscussionID;foreignKey:EntityType:discussion"`
}
```

**Key Features:**
- Multiple discussion types (general, forum topic, actionable step)
- Association with a specific user (creator)
- Optional association with book content (book, chapter, section)
- View count tracking
- Moderation controls (sticky, closed)
- Soft delete support
- Relationships to comments and likes

### Comments

The Comments table stores user responses to discussions, with support for threaded conversations.

```go
type Comment struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    DiscussionID uint `gorm:"index;not null"`
    ParentID    *uint `gorm:"index"` // For nested comments
    Content     string `gorm:"type:text;not null"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    DeletedAt   gorm.DeletedAt `gorm:"index"`
    
    // Relationships
    User        User
    Discussion  Discussion
    Parent      *Comment `gorm:"foreignKey:ParentID"`
    Replies     []Comment `gorm:"foreignKey:ParentID"`
    Likes       []UserLike `gorm:"foreignKey:CommentID;foreignKey:EntityType:comment"`
}
```

**Key Features:**
- Association with a specific user (author)
- Link to the parent discussion
- Support for nested comments (replies to comments)
- Soft delete support
- Relationship to likes

### UserLikes

The UserLikes table tracks user reactions to discussions and comments.

```go
type UserLike struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    EntityID    uint `gorm:"index;not null"` // ID of the entity being liked
    EntityType  string `gorm:"size:20;not null"` // Type of entity (discussion, comment)
    CreatedAt   time.Time
    
    // Relationships
    User        User
}
```

**Key Features:**
- Association with a specific user
- Polymorphic relationship to liked entities (discussions, comments)
- Timestamp for like tracking

## Points and Rewards System

### UserActivities

The UserActivities table records user actions that earn points, providing a detailed activity history.

```go
type ActivityType string

const (
    ActivityTypeRead       ActivityType = "read"
    ActivityTypeComment    ActivityType = "comment"
    ActivityTypeDiscussion ActivityType = "discussion"
    ActivityTypeShare      ActivityType = "share"
    ActivityTypeComplete   ActivityType = "complete"
)

type UserActivity struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    ActivityType ActivityType `gorm:"type:varchar(20);not null"`
    EntityID    uint `gorm:"index"` // ID of the entity related to this activity
    EntityType  string `gorm:"size:20"` // Type of entity (book, chapter, discussion, etc.)
    Points      int `gorm:"default:0"`
    Metadata    string `gorm:"type:jsonb"` // Additional activity data
    CreatedAt   time.Time
    
    // Relationships
    User        User
}
```

**Key Features:**
- Association with a specific user
- Activity type categorization
- Polymorphic relationship to activity targets
- Points awarded for the activity
- JSON metadata for additional context
- Timestamp for activity tracking

### TopicCompletions

The TopicCompletions table tracks when users complete specific sections, awarding points for completion.

```go
type TopicCompletion struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    BookID      uint `gorm:"index;not null"`
    ChapterID   uint `gorm:"index;not null"`
    SectionID   uint `gorm:"index;not null"`
    Points      int `gorm:"default:0"`
    CompletedAt time.Time
    
    // Relationships
    User        User
}
```

**Key Features:**
- Association with a specific user
- Records the specific book, chapter, and section completed
- Points awarded for completion
- Timestamp for completion tracking

### MembershipLevels

The MembershipLevels table defines the different membership tiers and their requirements.

```go
type MembershipLevel struct {
    ID          uint `gorm:"primaryKey"`
    Level       int `gorm:"unique;not null"`
    Name        string `gorm:"size:50;not null"`
    Description string `gorm:"type:text"`
    PointsRequired int `gorm:"not null"`
    Benefits    string `gorm:"type:jsonb"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
}
```

**Key Features:**
- Numeric level identifier
- Name and description for the membership level
- Points threshold required to achieve the level
- JSON-encoded benefits associated with the level

## Payment System

### Purchases

The Purchases table records all financial transactions on the platform.

```go
type PaymentGateway string

const (
    PaymentGatewayPaystack    PaymentGateway = "paystack"
    PaymentGatewayFlutterwave PaymentGateway = "flutterwave"
    PaymentGatewaySquad       PaymentGateway = "squad"
)

type PaymentStatus string

const (
    PaymentStatusPending    PaymentStatus = "pending"
    PaymentStatusCompleted  PaymentStatus = "completed"
    PaymentStatusFailed     PaymentStatus = "failed"
    PaymentStatusRefunded   PaymentStatus = "refunded"
)

type PaymentType string

const (
    PaymentTypePremium      PaymentType = "premium"
    PaymentTypeDonation     PaymentType = "donation"
)

type Currency string

const (
    CurrencyNGN Currency = "NGN"
    CurrencyUSD Currency = "USD"
)

type Purchase struct {
    ID             uint `gorm:"primaryKey"`
    UserID         uint `gorm:"index;not null"`
    Amount         float64 `gorm:"not null"`
    Currency       Currency `gorm:"type:varchar(3);default:'NGN'"`
    Description    string `gorm:"type:text"`
    PaymentType    PaymentType `gorm:"type:varchar(20);not null"`
    Gateway        PaymentGateway `gorm:"type:varchar(20);not null"`
    Status         PaymentStatus `gorm:"type:varchar(20);default:'pending'"`
    TransactionID  string `gorm:"size:100;index"`
    GatewayReference string `gorm:"size:100;index"`
    PurchaseDate   time.Time
    ExpiryDate     *time.Time
    Metadata       string `gorm:"type:jsonb"` // Additional purchase data
    CreatedAt      time.Time
    UpdatedAt      time.Time
    
    // Relationships
    User           User
}
```

**Key Features:**
- Association with a specific user
- Transaction amount and currency
- Payment type categorization
- Support for multiple Nigerian payment gateways
- Payment status tracking
- Transaction reference IDs
- Purchase and expiry dates
- JSON metadata for additional transaction details

### Plans

The Plans table defines the subscription plans available on the platform.

```go
type PlanDuration string

const (
    PlanDurationMonthly PlanDuration = "monthly"
    PlanDurationQuarterly PlanDuration = "quarterly"
    PlanDurationAnnual PlanDuration = "annual"
)

type Plan struct {
    ID          uint `gorm:"primaryKey"`
    Name        string `gorm:"size:100;not null"`
    Description string `gorm:"type:text"`
    Duration    PlanDuration `gorm:"type:varchar(20);not null"`
    Price       float64 `gorm:"not null"`
    Currency    Currency `gorm:"type:varchar(3);default:'NGN'"`
    Features    string `gorm:"type:jsonb"`
    IsActive    bool `gorm:"default:true"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    Subscriptions []Subscription `gorm:"foreignKey:PlanID"`
}
```

**Key Features:**
- Plan name and description
- Duration options (monthly, quarterly, annual)
- Price and currency
- JSON-encoded features included in the plan
- Active status flag

### Subscriptions

The Subscriptions table tracks user subscriptions to premium plans.

```go
type SubscriptionStatus string

const (
    SubscriptionStatusActive    SubscriptionStatus = "active"
    SubscriptionStatusCancelled SubscriptionStatus = "cancelled"
    SubscriptionStatusExpired   SubscriptionStatus = "expired"
)

type Subscription struct {
    ID          uint `gorm:"primaryKey"`
    UserID      uint `gorm:"index;not null"`
    PlanID      uint `gorm:"index;not null"`
    Status      SubscriptionStatus `gorm:"type:varchar(20);default:'active'"`
    StartDate   time.Time
    EndDate     time.Time
    CancelledAt *time.Time
    CreatedAt   time.Time
    UpdatedAt   time.Time
    
    // Relationships
    User        User
    Plan        Plan
}
```

**Key Features:**
- Association with a specific user
- Link to the subscription plan
- Subscription status tracking
- Start and end dates
- Cancellation tracking


## DATABASE_SCHEMA_PART3.md

# Great Nigeria Platform - Database Schema (Part 3)

## Citation System

The citation system uses a separate set of tables to track citations and bibliographic references across all books.

### Citations

```sql
CREATE TABLE IF NOT EXISTS citations (
    id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL,
    citation_key VARCHAR(100) NOT NULL,
    ref_number INTEGER NOT NULL,
    author TEXT NOT NULL,
    year VARCHAR(20) NOT NULL,
    title TEXT NOT NULL,
    source TEXT NOT NULL,
    url TEXT,
    type VARCHAR(50) NOT NULL,
    cited_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(book_id, citation_key)
);
```

**Key Features:**
- Unique citation key per book
- Reference number for in-text citations
- Author, year, title, and source information
- URL for online sources
- Citation type categorization
- Usage count tracking

### Citation Usages

```sql
CREATE TABLE IF NOT EXISTS citation_usages (
    id SERIAL PRIMARY KEY,
    citation_id INTEGER NOT NULL REFERENCES citations(id),
    book_id INTEGER NOT NULL,
    chapter_id INTEGER NOT NULL,
    section_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(citation_id, book_id, chapter_id, section_id)
);
```

**Key Features:**
- Links citations to specific locations in the books
- Tracks where each citation is used
- Prevents duplicate usage records

### Bibliographies

```sql
CREATE TABLE IF NOT EXISTS bibliographies (
    id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL UNIQUE,
    title TEXT NOT NULL,
    description TEXT,
    last_generated TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Features:**
- One bibliography per book
- Title and description for the bibliography
- Timestamp for when the bibliography was last generated

### Citation Categories

```sql
CREATE TABLE IF NOT EXISTS citation_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name)
);
```

**Key Features:**
- Categorizes citations by type
- Provides display order for bibliography organization
- Includes descriptions for each category

## Database Management

The Great Nigeria platform includes several tools and scripts for database management:

### Database Connection

The database connection is managed through a dedicated package that handles connection pooling, configuration, and migrations:

```go
// NewDatabase creates a new database connection
func NewDatabase(cfg *config.Config) (*gorm.DB, error) {
    var gormConfig gorm.Config

    // Configure logger based on debug mode
    if cfg.DebugMode {
        gormConfig.Logger = logger.Default.LogMode(logger.Info)
    } else {
        gormConfig.Logger = logger.Default.LogMode(logger.Error)
    }

    // Connect to database
    db, err := gorm.Open(postgres.Open(cfg.GetDatabaseURL()), &gormConfig)
    if err != nil {
        return nil, fmt.Errorf("failed to connect to database: %w", err)
    }

    // Configure connection pool
    sqlDB, err := db.DB()
    if err != nil {
        return nil, fmt.Errorf("failed to get SQL DB: %w", err)
    }

    // Set connection pool parameters
    sqlDB.SetMaxIdleConns(10)
    sqlDB.SetMaxOpenConns(100)
    sqlDB.SetConnMaxLifetime(time.Hour)

    // Auto-migrate schemas
    if err := autoMigrate(db); err != nil {
        return nil, fmt.Errorf("failed to auto-migrate database: %w", err)
    }

    return db, nil
}
```

**Key Features:**
- Configurable logging based on debug mode
- Connection pooling with optimized parameters
- Automatic schema migration

### Auto-Migration

The platform uses GORM's auto-migration feature to manage database schema changes:

```go
// autoMigrate automatically migrates the database schemas
func autoMigrate(db *gorm.DB) error {
    // Auto-migrate all models
    return db.AutoMigrate(
        // User-related models
        &models.User{},
        
        // Authentication-related models
        &models.VerificationStatus{},
        &models.VerificationRequest{},
        &models.UserBadge{},
        &models.ProfileCompletionStatus{},
        
        // Book-related models
        &models.Book{},
        &models.Chapter{},
        &models.Section{},
        &models.UserProgress{},
        &models.Bookmark{},
        
        // Discussion-related models
        &models.Discussion{},
        &models.Comment{},
        &models.UserLike{},
        
        // Feedback-related models
        &models.ContentMoodFeedback{},
        &models.ContentDifficultyFeedback{},
        
        // Points-related models
        &models.UserActivity{},
        &models.TopicCompletion{},
        &models.MembershipLevel{},
        
        // Payment-related models
        &models.Purchase{},
        &models.Plan{},
        &models.Subscription{},
    )
}
```

**Key Features:**
- Automatic schema creation and updates
- Organized by model category
- Comprehensive coverage of all database models

### Transaction Support

The platform includes a helper function for transaction management:

```go
// Transaction executes the given function in a database transaction
func Transaction(db *gorm.DB, fn func(tx *gorm.DB) error) error {
    return db.Transaction(fn)
}
```

**Key Features:**
- Simplified transaction handling
- Automatic rollback on error
- Consistent transaction pattern across the codebase

## Backup and Restoration

The Great Nigeria platform includes comprehensive tools for database backup and restoration:

### Database Backup

The platform uses a PowerShell script (`manage_database.ps1`) to handle database backups:

```powershell
# Backup database
function Backup-Database {
    $params = Get-DbParams
    Set-PgPassword -Password $params.Password
    
    try {
        if (-not (Test-DatabaseExists -DbName $params.Name)) {
            Write-Host "Database '$($params.Name)' does not exist." -ForegroundColor Yellow
            return
        }
        
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupDir = ".\backups"
        
        if (-not (Test-Path $backupDir)) {
            New-Item -ItemType Directory -Path $backupDir | Out-Null
        }
        
        if (-not $BackupFile) {
            $BackupFile = "$backupDir\$($params.Name)_$timestamp.sql"
        }
        
        Write-Host "Backing up database '$($params.Name)' to '$BackupFile'..." -ForegroundColor Cyan
        pg_dump -h $params.Host -p $params.Port -U $params.User -F p -f $BackupFile $params.Name
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Database backup created successfully: $BackupFile" -ForegroundColor Green
        } else {
            Write-Host "Failed to create database backup. Error code: $LASTEXITCODE" -ForegroundColor Red
        }
    } catch {
        Write-Host "Error backing up database: $_" -ForegroundColor Red
    } finally {
        Clear-PgPassword
    }
}
```

**Key Features:**
- Timestamp-based backup naming
- Automatic backup directory creation
- Comprehensive error handling
- Success/failure reporting

### Database Restoration

The platform includes a restoration function to recover from backups:

```powershell
# Restore database
function Restore-Database {
    if (-not $BackupFile) {
        Write-Host "Backup file not specified. Use -BackupFile parameter." -ForegroundColor Red
        return
    }
    
    if (-not (Test-Path $BackupFile)) {
        Write-Host "Backup file '$BackupFile' not found." -ForegroundColor Red
        return
    }
    
    $params = Get-DbParams
    Set-PgPassword -Password $params.Password
    
    try {
        # Create database if it doesn't exist
        if (-not (Test-DatabaseExists -DbName $params.Name)) {
            Write-Host "Database '$($params.Name)' does not exist. Creating it..." -ForegroundColor Yellow
            psql -h $params.Host -p $params.Port -U $params.User -c "CREATE DATABASE $($params.Name)" postgres
        } else {
            $confirmation = Read-Host "Database '$($params.Name)' already exists. Do you want to drop and recreate it? (y/n)"
            if ($confirmation -eq "y") {
                Write-Host "Dropping database '$($params.Name)'..." -ForegroundColor Cyan
                psql -h $params.Host -p $params.Port -U $params.User -c "DROP DATABASE $($params.Name)" postgres
                
                Write-Host "Creating database '$($params.Name)'..." -ForegroundColor Cyan
                psql -h $params.Host -p $params.Port -U $params.User -c "CREATE DATABASE $($params.Name)" postgres
            }
        }
        
        Write-Host "Restoring database from '$BackupFile'..." -ForegroundColor Cyan
        psql -h $params.Host -p $params.Port -U $params.User -d $params.Name -f $BackupFile
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Database restored successfully." -ForegroundColor Green
        } else {
            Write-Host "Failed to restore database. Error code: $LASTEXITCODE" -ForegroundColor Red
        }
    } catch {
        Write-Host "Error restoring database: $_" -ForegroundColor Red
    } finally {
        Clear-PgPassword
    }
}
```

**Key Features:**
- Validation of backup file existence
- Database creation if needed
- Option to drop and recreate existing database
- Comprehensive error handling
- Success/failure reporting

## Indexes and Performance

The database schema includes several indexes to optimize query performance:

1. Primary keys on all tables
2. Foreign key indexes (UserID, BookID, ChapterID, SectionID)
3. Unique indexes for username, email, and other unique fields
4. Composite indexes for common query patterns
5. Soft delete indexes (DeletedAt)

## Data Access Patterns

The platform uses repository interfaces to encapsulate data access patterns:

```go
// UserRepository interface example
type UserRepository interface {
    GetByID(id uint) (*User, error)
    GetByUsername(username string) (*User, error)
    GetByEmail(email string) (*User, error)
    Create(user *User) error
    Update(user *User) error
    Delete(id uint) error
    UpdateMembershipLevel(userID uint, level int) error
    AddPoints(userID uint, points int) error
}

// Implementation example
type PostgresUserRepository struct {
    DB *gorm.DB
}

func (r *PostgresUserRepository) GetByID(id uint) (*User, error) {
    var user User
    err := r.DB.First(&user, id).Error
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil // User not found
        }
        return nil, err
    }
    return &user, nil
}

// Other methods would be implemented similarly...
```

**Key Features:**
- Clean separation of data access logic
- Consistent error handling
- Repository pattern for testability
- Type-safe operations

## Conclusion

The Great Nigeria platform's database schema is designed to support all aspects of the application, from content management to user engagement and payment processing. The schema follows best practices for relational database design, with proper normalization, indexing, and relationship modeling.

The platform includes comprehensive tools for database management, including connection pooling, migrations, backups, and restorations. The repository pattern provides a clean abstraction for data access, making the codebase more maintainable and testable.

As the platform evolves, the database schema can be extended to support new features while maintaining backward compatibility through careful migration management.


## DEVELOPMENT_GUIDE.md

# Great Nigeria Platform - Development Guide

## Overview

This guide provides comprehensive information for developers working on the Great Nigeria platform. It covers coding standards, development workflows, testing procedures, and best practices.

## Table of Contents

1. [Development Environment](#development-environment)
2. [Coding Standards](#coding-standards)
3. [Development Workflow](#development-workflow)
4. [Testing](#testing)
5. [Documentation](#documentation)
6. [Performance Considerations](#performance-considerations)
7. [Security Guidelines](#security-guidelines)
8. [Deployment](#deployment)

## Development Environment

### Recommended Tools

- **IDE**: Visual Studio Code with the following extensions:
  - Go (by Go Team at Google)
  - ESLint (by Microsoft)
  - Prettier (by Prettier)
  - EditorConfig (by EditorConfig)
  - GitLens (by GitKraken)

- **API Testing**: Postman or Insomnia
- **Database Management**: pgAdmin or DBeaver
- **Git Client**: GitKraken, SourceTree, or command line

### Local Development Setup

Refer to the [Setup Guide](SETUP_GUIDE.md) for detailed instructions on setting up your local development environment.

### Development Modes

The platform supports several development modes:

1. **Standard Mode**: Default development mode with live API and local database
2. **Mock Mode**: Uses mock data instead of real API calls (faster for UI development)
3. **Hybrid Mode**: Uses real API for some services and mock data for others

To enable different modes, set the `DEV_MODE` environment variable:
```
DEV_MODE=standard|mock|hybrid
```

## Coding Standards

### Go Code Standards

- Follow the [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
- Use [gofmt](https://golang.org/cmd/gofmt/) to format code
- Follow the standard Go project layout
- Use meaningful variable and function names
- Write comprehensive comments for public functions and packages
- Keep functions small and focused on a single responsibility
- Use error handling consistently

### JavaScript/TypeScript Standards

- Follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use ESLint and Prettier for code formatting
- Use TypeScript for type safety
- Prefer functional components with hooks in React
- Use meaningful variable and function names
- Document complex functions and components

### CSS/SCSS Standards

- Follow the [BEM (Block Element Modifier)](http://getbem.com/) methodology
- Use SCSS for styling
- Keep selectors simple and avoid deep nesting
- Use variables for colors, fonts, and other repeated values
- Organize styles by component
- Ensure responsive design for all components

### Commit Message Standards

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

Types:
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: Code changes that neither fix a bug nor add a feature
- `perf`: Performance improvements
- `test`: Adding or correcting tests
- `chore`: Changes to the build process or auxiliary tools

Example:
```
feat(auth): implement two-factor authentication

- Add SMS verification
- Add email verification
- Update user settings page

Closes #123
```

## Development Workflow

### Git Workflow

The project follows a modified Git Flow workflow:

1. **main**: Production-ready code
2. **develop**: Integration branch for features
3. **feature/***:  Feature branches
4. **bugfix/***:  Bug fix branches
5. **release/***:  Release preparation branches
6. **hotfix/***:  Hotfix branches for production issues

#### Feature Development Process

1. Create a new feature branch from `develop`:
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/your-feature-name
   ```

2. Develop and test your feature

3. Commit changes following the commit message standards

4. Push your branch to the remote repository:
   ```bash
   git push -u origin feature/your-feature-name
   ```

5. Create a pull request to merge into `develop`

6. Address code review feedback

7. Once approved, merge the pull request

### Code Review Process

All code changes must go through code review before being merged:

1. **Self-review**: Review your own code before submitting
2. **Peer review**: At least one other developer must review the code
3. **Automated checks**: All automated tests and linting must pass
4. **Review criteria**:
   - Code correctness
   - Code quality and style
   - Test coverage
   - Documentation
   - Performance considerations
   - Security implications

### Dependency Management

#### Go Dependencies

- Use Go modules for dependency management
- Pin dependencies to specific versions
- Document third-party dependencies in the README
- Regularly update dependencies for security patches

#### JavaScript Dependencies

- Use npm for dependency management
- Pin dependencies to specific versions in package.json
- Use `npm audit` to check for security vulnerabilities
- Regularly update dependencies for security patches

## Testing

### Testing Strategy

The project follows a comprehensive testing strategy:

1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test interactions between components
3. **End-to-End Tests**: Test complete user flows
4. **Performance Tests**: Test system performance under load
5. **Security Tests**: Test for security vulnerabilities

### Backend Testing

- Use the standard Go testing package
- Aim for at least 80% code coverage
- Use table-driven tests where appropriate
- Mock external dependencies
- Use testify for assertions and mocks

Example Go test:
```go
func TestUserService_GetByID(t *testing.T) {
    // Setup
    mockRepo := new(mocks.UserRepository)
    service := NewUserService(mockRepo)
    
    // Test cases
    tests := []struct {
        name     string
        userID   uint
        mockUser *models.User
        mockErr  error
        wantUser *models.User
        wantErr  bool
    }{
        {
            name:     "successful retrieval",
            userID:   1,
            mockUser: &models.User{ID: 1, Username: "testuser"},
            mockErr:  nil,
            wantUser: &models.User{ID: 1, Username: "testuser"},
            wantErr:  false,
        },
        {
            name:     "user not found",
            userID:   2,
            mockUser: nil,
            mockErr:  nil,
            wantUser: nil,
            wantErr:  false,
        },
        {
            name:     "repository error",
            userID:   3,
            mockUser: nil,
            mockErr:  errors.New("database error"),
            wantUser: nil,
            wantErr:  true,
        },
    }
    
    // Run tests
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Setup expectations
            mockRepo.On("GetByID", tt.userID).Return(tt.mockUser, tt.mockErr)
            
            // Call the function
            user, err := service.GetByID(tt.userID)
            
            // Assert results
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
            assert.Equal(t, tt.wantUser, user)
            
            // Verify expectations
            mockRepo.AssertExpectations(t)
        })
    }
}
```

### Frontend Testing

- Use Jest for unit testing
- Use React Testing Library for component testing
- Use Cypress for end-to-end testing
- Test component rendering, user interactions, and state changes

Example React component test:
```javascript
import { render, screen, fireEvent } from '@testing-library/react';
import UserProfile from './UserProfile';

describe('UserProfile', () => {
  const mockUser = {
    id: 1,
    username: 'testuser',
    fullName: 'Test User',
    email: '<EMAIL>',
  };

  test('renders user information correctly', () => {
    render(<UserProfile user={mockUser} />);
    
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('@testuser')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  test('edit button toggles edit mode', () => {
    render(<UserProfile user={mockUser} />);
    
    // Initially not in edit mode
    expect(screen.queryByLabelText('Full Name')).not.toBeInTheDocument();
    
    // Click edit button
    fireEvent.click(screen.getByText('Edit Profile'));
    
    // Now in edit mode
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test User')).toBeInTheDocument();
  });
});
```

### Running Tests

#### Backend Tests

```bash
# Run all tests
go test ./...

# Run tests for a specific package
go test ./internal/auth/...

# Run tests with coverage
go test -cover ./...

# Generate coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

#### Frontend Tests

```bash
# Run all tests
npm test

# Run tests for a specific component
npm test -- UserProfile

# Run tests with coverage
npm test -- --coverage

# Run end-to-end tests
npm run cypress:open
```

## Documentation

### Code Documentation

- Document all public functions, types, and packages
- Use [GoDoc](https://blog.golang.org/godoc) style for Go code
- Use [JSDoc](https://jsdoc.app/) for JavaScript/TypeScript
- Include examples for complex functions
- Document assumptions and edge cases

Example Go documentation:
```go
// UserService provides methods for managing users.
type UserService struct {
    repo UserRepository
}

// NewUserService creates a new UserService with the given repository.
func NewUserService(repo UserRepository) *UserService {
    return &UserService{
        repo: repo,
    }
}

// GetByID retrieves a user by their ID.
// Returns nil if the user is not found.
// Returns an error if the repository operation fails.
func (s *UserService) GetByID(id uint) (*User, error) {
    return s.repo.GetByID(id)
}
```

Example JavaScript documentation:
```javascript
/**
 * UserProfile component displays and allows editing of user information.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.user - User object containing profile information
 * @param {Function} props.onUpdate - Callback function called when user is updated
 * @returns {JSX.Element} Rendered component
 */
function UserProfile({ user, onUpdate }) {
    // Component implementation
}
```

### API Documentation

- Document all API endpoints
- Include request and response formats
- Document authentication requirements
- Include example requests and responses
- Document error responses

The API documentation is available at `/api/docs` when running in development mode.

## Performance Considerations

### Backend Performance

- Use database indexes for frequently queried fields
- Implement caching for expensive operations
- Use connection pooling for database connections
- Optimize database queries (avoid N+1 queries)
- Use pagination for large result sets
- Profile and optimize hot code paths

### Frontend Performance

- Minimize bundle size with code splitting
- Optimize images and assets
- Implement lazy loading for components and routes
- Use memoization for expensive calculations
- Optimize rendering with React.memo and useMemo
- Implement virtualization for long lists

### Monitoring and Profiling

- Use pprof for Go profiling
- Use Lighthouse for frontend performance auditing
- Implement logging for performance-critical operations
- Set up monitoring for production performance

## Security Guidelines

### Authentication and Authorization

- Use JWT for authentication
- Implement proper authorization checks
- Use secure password hashing (bcrypt)
- Implement rate limiting for authentication endpoints
- Use secure cookie settings (HttpOnly, Secure, SameSite)

### Data Protection

- Validate all user input
- Use parameterized queries to prevent SQL injection
- Implement proper error handling to avoid information leakage
- Use HTTPS for all communications
- Encrypt sensitive data at rest

### Common Vulnerabilities

- Prevent Cross-Site Scripting (XSS) with proper output encoding
- Prevent Cross-Site Request Forgery (CSRF) with tokens
- Avoid Server-Side Request Forgery (SSRF) by validating URLs
- Implement proper Content Security Policy (CSP)
- Regularly update dependencies to patch security vulnerabilities

## Deployment

### Deployment Environments

1. **Development**: For active development
2. **Staging**: For testing before production
3. **Production**: Live environment

### Deployment Process

1. Merge changes into the appropriate branch
2. Run the CI/CD pipeline
3. Deploy to the target environment
4. Run smoke tests
5. Monitor for issues

### Deployment Configuration

- Use environment variables for configuration
- Use different configuration files for different environments
- Document required environment variables

### Rollback Procedure

In case of deployment issues:

1. Identify the issue
2. Decide whether to fix forward or roll back
3. If rolling back:
   - Revert to the previous version in the deployment system
   - Verify the rollback was successful
   - Document the issue for future reference

## Conclusion

Following these development guidelines will ensure a consistent, high-quality codebase for the Great Nigeria platform. If you have questions or suggestions for improving these guidelines, please contact the development team.


## GO_BACKEND_INTEGRATION.md

# Go Backend Integration Guide

This document provides instructions for configuring the Go backend to work with the React TypeScript frontend.

## Overview

The Go backend needs to be configured to:

1. Allow Cross-Origin Resource Sharing (CORS) for the React frontend
2. Serve API endpoints that the React frontend can consume
3. Handle authentication and authorization for protected routes

## CORS Configuration

### 1. Install CORS Middleware

```bash
go get github.com/gin-contrib/cors
```

### 2. Import the CORS Package

In `cmd/api-gateway/main.go`, add the CORS package to the imports:

```go
import (
    // ... other imports
    "github.com/gin-contrib/cors"
)
```

### 3. Configure CORS Middleware

Add the CORS middleware to the router before registering any routes:

```go
// CORS configuration
router.Use(cors.New(cors.Config{
    AllowOrigins:     []string{"http://localhost:3000", "https://your-production-frontend-domain.com"},
    AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
    AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
    ExposeHeaders:    []string{"Content-Length"},
    AllowCredentials: true,
    MaxAge:           12 * time.Hour,
}))
```

### 4. Environment-Specific Configuration

For better flexibility, make the CORS configuration environment-specific:

```go
// CORS configuration
var allowedOrigins []string
if os.Getenv("ENV") == "production" {
    allowedOrigins = []string{"https://your-production-frontend-domain.com"}
} else {
    allowedOrigins = []string{"http://localhost:3000"}
}

router.Use(cors.New(cors.Config{
    AllowOrigins:     allowedOrigins,
    AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
    AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
    ExposeHeaders:    []string{"Content-Length"},
    AllowCredentials: true,
    MaxAge:           12 * time.Hour,
}))
```

## API Endpoints

The React frontend expects the following API endpoints:

### Authentication Endpoints

- `POST /api/auth/register`: Register a new user
- `POST /api/auth/login`: Login a user
- `GET /api/auth/me`: Get the current user

### Book Endpoints

- `GET /api/books`: Get all books
- `GET /api/books/:id`: Get a book by ID
- `GET /api/books/:id/chapters`: Get chapters for a book
- `GET /api/books/chapters/:id`: Get a chapter by ID
- `GET /api/books/sections/:id`: Get a section by ID
- `POST /api/books/:id/progress`: Save reading progress
- `GET /api/books/:id/progress`: Get reading progress for a book
- `POST /api/books/:id/bookmarks`: Add a bookmark
- `GET /api/books/:id/bookmarks`: Get bookmarks for a book
- `DELETE /api/books/bookmarks/:id`: Delete a bookmark

### User Profile Endpoints

- `GET /api/users/:id/profile`: Get user profile
- `PUT /api/users/:id/profile`: Update user profile
- `GET /api/users/:id/reading-stats`: Get reading statistics
- `GET /api/users/:id/bookmarks`: Get user bookmarks
- `GET /api/users/:id/activities`: Get user activities
- `POST /api/users/:id/change-password`: Change user password
- `POST /api/users/:id/avatar`: Upload user avatar

### Forum Endpoints

- `GET /api/forum/categories`: Get all forum categories
- `GET /api/forum/categories/:id/topics`: Get topics by category
- `GET /api/forum/topics/:id`: Get a topic by ID
- `POST /api/forum/topics`: Create a new topic
- `POST /api/forum/topics/:id/replies`: Create a reply to a topic
- `POST /api/forum/replies/:id/vote`: Vote on a reply
- `GET /api/forum/search`: Search topics
- `DELETE /api/forum/topics/:id`: Delete a topic
- `DELETE /api/forum/replies/:id`: Delete a reply

### Resource Endpoints

- `GET /api/resources/categories`: Get all resource categories
- `GET /api/resources/categories/:id/resources`: Get resources by category
- `GET /api/resources/:id`: Get a resource by ID
- `GET /api/resources/:id/download`: Download a resource
- `POST /api/resources/:id/track-download`: Track resource download
- `GET /api/resources/search`: Search resources

### Celebrate Nigeria Endpoints

- `GET /api/celebrate/featured`: Get featured entries
- `GET /api/celebrate/:type/:slug`: Get an entry by type and slug
- `GET /api/celebrate/search`: Search entries
- `GET /api/celebrate/categories`: Get all categories
- `POST /api/celebrate/submit`: Submit a new entry
- `POST /api/celebrate/entries/:id/vote`: Vote for an entry
- `POST /api/celebrate/entries/:id/comments`: Comment on an entry
- `GET /api/celebrate/random`: Get a random entry

## Authentication and Authorization

### JWT Authentication

The React frontend expects JWT authentication:

1. When a user logs in or registers, the backend should return a JWT token
2. The frontend will include this token in the `Authorization` header of subsequent requests
3. The backend should validate this token for protected routes

### Example Login Handler

```go
func (h *AuthHandler) Login(c *gin.Context) {
    var loginRequest struct {
        Email    string `json:"email" binding:"required,email"`
        Password string `json:"password" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&loginRequest); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    user, err := h.authService.Authenticate(loginRequest.Email, loginRequest.Password)
    if err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
        return
    }
    
    token, err := h.authService.GenerateToken(user.ID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "user": user,
        "token": token,
    })
}
```

### Example Auth Middleware

```go
func AuthMiddleware(authService *service.AuthService) gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
            return
        }
        
        // Extract token from "Bearer <token>"
        tokenParts := strings.Split(authHeader, " ")
        if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
            return
        }
        
        token := tokenParts[1]
        userID, err := authService.ValidateToken(token)
        if err != nil {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
            return
        }
        
        // Set user ID in context for later use
        c.Set("userID", userID)
        c.Next()
    }
}
```

## Error Handling

The React frontend expects consistent error responses:

```go
func ErrorResponse(c *gin.Context, statusCode int, errorCode string, message string, details string) {
    c.JSON(statusCode, gin.H{
        "error": gin.H{
            "code": errorCode,
            "message": message,
            "details": details,
        },
    })
}
```

Common error codes:
- `unauthorized`: Authentication required or token invalid
- `forbidden`: User does not have permission to access the resource
- `not_found`: Resource not found
- `validation_error`: Request validation failed
- `server_error`: Internal server error

## Testing the Integration

### 1. Start the Go Backend

```bash
go run cmd/api-gateway/main.go
```

### 2. Start the React Frontend

```bash
cd great-nigeria-frontend
npm start
```

### 3. Test API Endpoints

Use tools like Postman or curl to test the API endpoints:

```bash
# Test authentication
curl -X POST http://localhost:5000/api/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"password123"}'

# Test protected endpoint
curl -X GET http://localhost:5000/api/auth/me -H "Authorization: Bearer <token>"
```

### 4. Test CORS

Check the browser's developer console for any CORS-related errors when the React frontend makes requests to the Go backend.

## Conclusion

By following these instructions, you will have configured the Go backend to work with the React TypeScript frontend. The backend will handle API requests, authentication, and authorization, while the frontend will provide a modern, responsive user interface.


## README Database Doc.md

# Great Nigeria Platform - Database Documentation

This directory contains documentation for the database schema and management tools used in the Great Nigeria platform.

## Main Documentation Files

- [DATABASE_SCHEMA_PART1.md](DATABASE_SCHEMA_PART1.md) - Part 1 of the database schema documentation, covering core tables and user engagement
- [DATABASE_SCHEMA_PART2.md](DATABASE_SCHEMA_PART2.md) - Part 2 of the database schema documentation, covering discussion system, points system, and payment system
- [DATABASE_SCHEMA_PART3.md](DATABASE_SCHEMA_PART3.md) - Part 3 of the database schema documentation, covering citation system, database management, and backup/restoration

## Overview

The Great Nigeria platform uses PostgreSQL as its primary database, with GORM as the ORM layer. The database schema is designed to support all aspects of the platform, including:

- User management and authentication
- Book content organization and access control
- User progress tracking
- Discussion forums and comments
- Points and activities tracking
- Payment processing
- Citation and bibliography management

## Database Schema

The database schema is organized into several logical groups:

### Core Tables
- **Users**: User accounts and profiles
- **Books**: Book metadata and access control
- **Chapters**: Book chapter organization
- **Sections**: Book content sections

### User Progress and Engagement
- **UserProgress**: Tracks reading progress
- **Bookmarks**: User-saved locations in books

### Discussion System
- **Discussions**: Forum topics and discussions
- **Comments**: User responses to discussions
- **UserLikes**: User reactions to content

### Points and Rewards System
- **UserActivities**: Records of point-earning actions
- **TopicCompletions**: Tracks completed sections
- **MembershipLevels**: Defines membership tiers

### Payment System
- **Purchases**: Financial transactions
- **Plans**: Subscription plan definitions
- **Subscriptions**: User subscriptions

### Citation System
- **Citations**: Bibliographic references
- **CitationUsages**: Tracks citation usage
- **Bibliographies**: Book bibliography metadata
- **CitationCategories**: Citation type categories

## Database Management

The platform includes several tools for database management:

- **Connection Management**: Configurable connection pooling
- **Auto-Migration**: Automatic schema updates
- **Transaction Support**: Simplified transaction handling
- **Backup and Restoration**: Comprehensive backup tools

## Related Documentation

For more information about the project, refer to:
- [Architecture Documentation](../architecture/ARCHITECTURE_OVERVIEW.md) - Details of the system architecture
- [Code Analysis Documentation](../code/) - Analysis of the codebase
- [Implementation Documentation](../implementation/) - Implementation plans


## README.md

# Great Nigeria Platform - Development Documentation

This directory contains comprehensive documentation for developing and setting up the Great Nigeria platform.

## Main Documentation Files

- [SETUP_GUIDE.md](SETUP_GUIDE.md) - Detailed instructions for setting up the development environment
- [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md) - Coding standards, workflows, and best practices

## Overview

The development documentation provides essential information for developers working on the Great Nigeria platform. It covers environment setup, coding standards, development workflows, testing procedures, and best practices.

### Key Development Areas

1. **Environment Setup**
   - Prerequisites and required software
   - Repository setup and structure
   - Backend and frontend setup
   - Database configuration
   - Running the application locally

2. **Development Standards**
   - Coding standards for Go, JavaScript/TypeScript, and CSS/SCSS
   - Commit message standards
   - Git workflow and branching strategy
   - Code review process
   - Dependency management

3. **Testing Procedures**
   - Testing strategy and coverage requirements
   - Backend testing with Go
   - Frontend testing with Jest and React Testing Library
   - End-to-end testing with Cypress
   - Running and automating tests

4. **Performance and Security**
   - Backend and frontend performance considerations
   - Authentication and authorization best practices
   - Data protection guidelines
   - Common vulnerability prevention
   - Monitoring and profiling

### Development Workflow

The Great Nigeria platform follows a modified Git Flow workflow:

1. **Feature Development**:
   - Create feature branch from `develop`
   - Develop and test feature
   - Submit pull request
   - Address code review feedback
   - Merge to `develop`

2. **Release Process**:
   - Create release branch from `develop`
   - Perform final testing
   - Fix any release-specific issues
   - Merge to `main` and tag with version
   - Merge back to `develop`

3. **Hotfix Process**:
   - Create hotfix branch from `main`
   - Fix critical issue
   - Test thoroughly
   - Merge to `main` and tag with version
   - Merge to `develop`

## Getting Started

New developers should follow these steps to get started:

1. Read the [Setup Guide](SETUP_GUIDE.md) to set up your development environment
2. Review the [Development Guide](DEVELOPMENT_GUIDE.md) to understand coding standards and workflows
3. Set up the local development environment
4. Run the application locally
5. Make a small change to get familiar with the workflow

## Related Documentation

For more information about the project, refer to:
- [Architecture Documentation](../architecture/ARCHITECTURE_OVERVIEW.md) - Technical architecture
- [API Documentation](../api/) - API endpoints and usage
- [Database Documentation](../database/) - Database schema and management


## SERVER_SETUP_GUIDE.md

# Great Nigeria Library Project Setup Guide

This guide will help you set up the Great Nigeria Library project on your server from its GitHub repositories.

## Prerequisites

Before starting, ensure your server has the following installed:
- Git
- Go (version 1.18 or later)
- MySQL or MariaDB
- Node.js and npm (for the frontend)

## Step 1: Clone the Repositories

First, clone both the backend and frontend repositories:

```bash
# Create a project directory
mkdir -p /path/to/project
cd /path/to/project

# Clone the backend repository
git clone https://github.com/yerenwgventures/GreatNigeriaLibrary.git
cd GreatNigeriaLibrary

# Clone the frontend repository inside the backend directory
git clone https://github.com/yerenwgventures/great-nigeria-frontend.git
```

## Step 2: Set Up the Database

```bash
# Navigate to the database directory
cd /path/to/project/GreatNigeriaLibrary/database

# Import the database
mysql -u your_username -p your_database_name < great_nigeria_db_2025-04-23.sql
```

If the SQL file is compressed:
```bash
gunzip -c great_nigeria_db_2025-04-23.sql.gz | mysql -u your_username -p your_database_name
```

## Step 3: Configure Environment Variables

Create or update the `.env` file in the root directory:

```bash
cd /path/to/project/GreatNigeriaLibrary
cp .env.example .env  # If an example file exists
```

Edit the `.env` file with your database credentials and other configuration:

```
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=your_database_name

# Server Configuration
SERVER_PORT=5000
API_BASE_URL=http://localhost:5000/api
```

## Step 4: Build and Run the Backend

```bash
cd /path/to/project/GreatNigeriaLibrary

# Get Go dependencies
go mod download

# Build the API gateway
go build -o bin/api-gateway ./cmd/api-gateway

# Run the API gateway
./bin/api-gateway
```

For production deployment, you might want to set up a service manager like systemd:

```bash
# Create a systemd service file
sudo nano /etc/systemd/system/great-nigeria-api.service
```

Add the following content:

```
[Unit]
Description=Great Nigeria Library API Gateway
After=network.target mysql.service

[Service]
User=your_server_user
WorkingDirectory=/path/to/project/GreatNigeriaLibrary
ExecStart=/path/to/project/GreatNigeriaLibrary/bin/api-gateway
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
sudo systemctl enable great-nigeria-api
sudo systemctl start great-nigeria-api
```

## Step 5: Set Up the Frontend

```bash
cd /path/to/project/GreatNigeriaLibrary/great-nigeria-frontend

# Install dependencies
npm install

# Create or update environment variables
cp .env.example .env  # If an example file exists
```

Edit the `.env` file:

```
REACT_APP_API_URL=http://your_server_ip:5000/api
```

Build the frontend:

```bash
npm run build
```

## Step 6: Serve the Frontend

### Option 1: Using the Go server

The Go backend can serve the frontend static files. Make sure the build output is in the correct location for the Go server to find it.

### Option 2: Using a web server like Nginx

```bash
# Install Nginx if not already installed
sudo apt-get install nginx

# Create a site configuration
sudo nano /etc/nginx/sites-available/great-nigeria
```

Add the following configuration:

```
server {
    listen 80;
    server_name your_domain.com;

    location / {
        root /path/to/project/GreatNigeriaLibrary/great-nigeria-frontend/build;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site and restart Nginx:

```bash
sudo ln -s /etc/nginx/sites-available/great-nigeria /etc/nginx/sites-enabled/
sudo nginx -t  # Test the configuration
sudo systemctl restart nginx
```

## Step 7: Run Database Migrations (if needed)

```bash
cd /path/to/project/GreatNigeriaLibrary
go run ./migrations/runner.go
```

## Step 8: Verify the Setup

1. Check if the API server is running:
```bash
curl http://localhost:5000/api/health
```

2. Open your browser and navigate to your domain or server IP to see if the frontend is working.

## Troubleshooting

### Database Connection Issues
- Verify database credentials in the `.env` file
- Check if the MySQL service is running: `sudo systemctl status mysql`
- Ensure the database exists and has been properly imported

### API Server Issues
- Check the logs: `sudo journalctl -u great-nigeria-api`
- Verify the Go version: `go version`
- Make sure all dependencies are installed: `go mod download`

### Frontend Issues
- Check for build errors in the npm build output
- Verify that the API URL in the frontend `.env` file is correct
- Check browser console for any JavaScript errors

## Maintenance

### Updating the Project
To update the project with the latest changes from GitHub:

```bash
cd /path/to/project/GreatNigeriaLibrary
git pull

cd great-nigeria-frontend
git pull
npm install  # If dependencies have changed
npm run build

# Restart the API server
sudo systemctl restart great-nigeria-api
```

### Backing Up the Database
Regular database backups are recommended:

```bash
mysqldump -u your_username -p your_database_name > backup_$(date +%Y-%m-%d).sql
```

## Important Files and Directories

Make sure the following key files and directories are included in your deployment:

- `/cmd/api-gateway/` - Contains the main API gateway code
- `/internal/` - Contains all the internal packages and business logic
- `/migrations/` - Database migration files
- `/web/templates/` - HTML templates for server-rendered pages
- `/database/` - Database scripts and backups
- `/.env` - Environment configuration file
- `/go.mod` and `/go.sum` - Go module files
- `/great-nigeria-frontend/` - React frontend application


## SETUP_GUIDE.md

# Great Nigeria Platform - Setup Guide

## Overview

This guide provides comprehensive instructions for setting up the Great Nigeria platform development environment. Follow these steps to get the platform running locally for development and testing.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Repository Setup](#repository-setup)
3. [Backend Setup](#backend-setup)
4. [Frontend Setup](#frontend-setup)
5. [Database Setup](#database-setup)
6. [Configuration](#configuration)
7. [Running the Application](#running-the-application)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

Before setting up the Great Nigeria platform, ensure you have the following prerequisites installed:

### Required Software

- **Git**: Version control system
  - Version 2.30.0 or higher
  - [Download Git](https://git-scm.com/downloads)

- **Go**: Backend programming language
  - Version 1.18 or higher
  - [Download Go](https://golang.org/dl/)

- **Node.js**: JavaScript runtime for frontend development
  - Version 16.x or higher
  - [Download Node.js](https://nodejs.org/)

- **PostgreSQL**: Database system
  - Version 13.x or higher
  - [Download PostgreSQL](https://www.postgresql.org/download/)

- **Docker** (optional): Containerization platform
  - Version 20.10.x or higher
  - [Download Docker](https://www.docker.com/products/docker-desktop)

### Development Tools

- **Visual Studio Code** (recommended): Code editor
  - [Download VS Code](https://code.visualstudio.com/)
  - Recommended extensions:
    - Go (by Go Team at Google)
    - ESLint (by Microsoft)
    - Prettier (by Prettier)
    - Docker (by Microsoft)

- **Postman** (optional): API testing tool
  - [Download Postman](https://www.postman.com/downloads/)

### System Requirements

- **Operating System**: Windows 10/11, macOS 10.15+, or Linux
- **RAM**: 8GB minimum, 16GB recommended
- **Disk Space**: 5GB minimum for source code, dependencies, and database

## Repository Setup

### Clone the Repository

1. Open a terminal or command prompt
2. Clone the repository:
   ```bash
   git clone https://github.com/greatnigeria/platform.git
   ```
3. Navigate to the project directory:
   ```bash
   cd platform
   ```

### Repository Structure

The repository is organized as follows:

```
platform/
├── cmd/                  # Application entry points
│   ├── api/              # API server
│   ├── worker/           # Background worker
│   └── cli/              # Command-line tools
├── internal/             # Internal packages
│   ├── auth/             # Authentication service
│   ├── content/          # Content service
│   ├── discussion/       # Discussion service
│   ├── payment/          # Payment service
│   ├── points/           # Points service
│   └── ...               # Other services
├── pkg/                  # Public packages
│   ├── common/           # Common utilities
│   ├── config/           # Configuration
│   └── models/           # Shared models
├── web/                  # Frontend code
│   ├── static/           # Static assets
│   ├── templates/        # HTML templates
│   └── src/              # JavaScript source
├── scripts/              # Utility scripts
├── migrations/           # Database migrations
├── docs/                 # Documentation
└── .env.example          # Example environment variables
```

## Backend Setup

### Install Go Dependencies

1. Navigate to the project root directory
2. Install Go dependencies:
   ```bash
   go mod download
   ```

### Build Backend Services

1. Build the API server:
   ```bash
   go build -o bin/api ./cmd/api
   ```

2. Build the worker (optional):
   ```bash
   go build -o bin/worker ./cmd/worker
   ```

## Frontend Setup

### Install Node.js Dependencies

1. Navigate to the web directory:
   ```bash
   cd web
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

### Build Frontend Assets

1. Build the frontend assets:
   ```bash
   npm run build
   ```

## Database Setup

### Configure PostgreSQL

1. Start PostgreSQL service if not already running
2. Create a new database:
   ```bash
   createdb great_nigeria
   ```

### Run Database Migrations

1. Navigate to the project root directory
2. Run the database migrations:
   ```bash
   ./scripts/migrate.ps1
   ```

### Load Sample Data (Optional)

1. Load sample data for development:
   ```bash
   ./scripts/seed_data.ps1
   ```

## Configuration

### Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your local configuration:
   ```
   # Database Configuration
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=great_nigeria
   DB_USER=postgres
   DB_PASSWORD=your_password

   # Server Configuration
   PORT=8080
   ENV=development
   DEBUG=true

   # JWT Configuration
   JWT_SECRET=your_jwt_secret
   JWT_EXPIRY=24h

   # Payment Gateway Configuration (if needed)
   PAYSTACK_SECRET_KEY=your_paystack_secret
   FLUTTERWAVE_SECRET_KEY=your_flutterwave_secret
   ```

### Application Configuration

1. Navigate to the `pkg/config` directory
2. Review and modify configuration files as needed:
   - `config.go`: Main configuration structure
   - `database.go`: Database configuration
   - `server.go`: Server configuration

## Running the Application

### Start the Backend Server

1. Navigate to the project root directory
2. Start the API server:
   ```bash
   ./bin/api
   ```
   Or using Go directly:
   ```bash
   go run ./cmd/api
   ```

### Start the Frontend Development Server (Optional)

1. Navigate to the web directory:
   ```bash
   cd web
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

### Using Docker (Optional)

1. Build and start all services using Docker Compose:
   ```bash
   docker-compose up --build
   ```

## Testing

### Run Backend Tests

1. Navigate to the project root directory
2. Run all tests:
   ```bash
   go test ./...
   ```

3. Run tests for a specific package:
   ```bash
   go test ./internal/auth/...
   ```

### Run Frontend Tests

1. Navigate to the web directory:
   ```bash
   cd web
   ```

2. Run frontend tests:
   ```bash
   npm test
   ```

## Troubleshooting

### Common Issues

#### Database Connection Issues

**Problem**: Unable to connect to the database
**Solution**:
1. Verify PostgreSQL is running:
   ```bash
   pg_isready
   ```
2. Check database credentials in `.env` file
3. Ensure the database exists:
   ```bash
   psql -l
   ```

#### Port Already in Use

**Problem**: Port already in use when starting the server
**Solution**:
1. Find the process using the port:
   ```bash
   netstat -ano | findstr :8080
   ```
2. Kill the process:
   ```bash
   taskkill /PID <PID> /F
   ```
3. Or change the port in the `.env` file

#### Go Module Issues

**Problem**: Issues with Go modules or dependencies
**Solution**:
1. Clear Go module cache:
   ```bash
   go clean -modcache
   ```
2. Update Go modules:
   ```bash
   go mod tidy
   ```

#### Frontend Build Issues

**Problem**: Errors when building frontend assets
**Solution**:
1. Clear Node.js cache:
   ```bash
   npm cache clean --force
   ```
2. Delete node_modules and reinstall:
   ```bash
   rm -rf node_modules
   npm install
   ```

### Getting Help

If you encounter issues not covered in this guide:

1. Check the project documentation in the `docs` directory
2. Search for similar issues in the project issue tracker
3. Contact the development <NAME_EMAIL>


## update_app_instructions.md

# Instructions for Updating App.tsx

To add the ProgressDashboardPage to your application, you need to update the App.tsx file in your frontend repository. Here are the steps:

1. **Copy the following files to your frontend repository**:
   - Copy `frontend_files/ProgressDashboardPage.tsx` to `C:\Users\<USER>\GreatNigeriaFrontend\great-nigeria-frontend\src\pages\ProgressDashboardPage.tsx`
   - Copy `frontend_files/progressSlice.ts` to `C:\Users\<USER>\GreatNigeriaFrontend\great-nigeria-frontend\src\features\progress\progressSlice.ts`
   - Copy `frontend_files/progressService.ts` to `C:\Users\<USER>\GreatNigeriaFrontend\great-nigeria-frontend\src\api\progressService.ts`

2. **Update your App.tsx file**:
   - Add the import for ProgressDashboardPage:
     ```tsx
     import ProgressDashboardPage from './pages/ProgressDashboardPage';
     ```
   - Add a new route for the ProgressDashboardPage:
     ```tsx
     <Route path="/progress" element={<ProgressDashboardPage />} />
     ```

3. **Update your store/index.ts file**:
   - Add the import for progressReducer:
     ```tsx
     import progressReducer from '../features/progress/progressSlice';
     ```
   - Add progressReducer to the reducer object:
     ```tsx
     reducer: {
       // existing reducers...
       progress: progressReducer,
     },
     ```

4. **Update your api/index.ts file**:
   - Add the import for progressService:
     ```tsx
     import progressService from './progressService';
     ```
   - Export progressService:
     ```tsx
     export { 
       // existing exports...
       progressService 
     };
     ```

5. **Add a link to the ProgressDashboardPage in your navigation**:
   - Find your navigation component (likely in Header.tsx or similar)
   - Add a link to the progress dashboard:
     ```tsx
     <Link to="/progress">Progress</Link>
     ```

## Backend Implementation

For the backend implementation, copy the following files to your backend repository:

1. **Copy the progress models**:
   - Copy `internal/progress/models/progress.go` to your backend repository

2. **Copy the progress repository**:
   - Copy `internal/progress/repository/progress_repository.go` to your backend repository

3. **Copy the progress service**:
   - Copy `internal/progress/service/progress_service.go` to your backend repository

4. **Copy the progress handlers**:
   - Copy `internal/progress/handlers/progress_handler.go` to your backend repository

5. **Update your API Gateway router**:
   - Find your router.go file (likely in internal/gateway/router.go)
   - Initialize the progress repository, service, and handler
   - Register the progress routes

Example router update:
```go
// Initialize progress components
progressRepo := progress_repository.NewSQLProgressRepository(db)
progressService := progress_service.NewProgressService(progressRepo)
progressHandler := progress_handlers.NewProgressHandler(progressService)

// Register progress routes
progressHandler.RegisterRoutes(apiGroup)
```

## Testing

After implementing these changes, test the progress dashboard by:

1. Starting your backend server
2. Starting your frontend development server
3. Navigating to the /progress route in your browser
4. Verifying that the progress dashboard loads correctly
5. Testing the different tabs and features of the dashboard
