package main

import (
        "database/sql"
        "encoding/json"
        "fmt"
        "os"
        "strconv"
        "strings"
        "time"

        _ "github.com/lib/pq"
)

// Database connection details
var (
        dbUser     = os.Getenv("PGUSER")
        dbPassword = os.Getenv("PGPASSWORD")
        dbName     = os.Getenv("PGDATABASE")
        dbHost     = os.Getenv("PGHOST")
        dbPort     = os.Getenv("PGPORT")
)

// NewTocData represents the structure of our book3_toc_data.json file
type NewTocData map[string]map[string]struct {
        Title       string            `json:"title"`
        Subsections map[string]string `json:"subsections"`
}

// Database connection string
func getDBConnectionString() string {
        // First try to use the DATABASE_URL environment variable
        dbURL := os.Getenv("DATABASE_URL")
        if dbURL != "" {
                // Parse the DATABASE_URL to check if it already has sslmode parameter
                if strings.Contains(dbURL, "sslmode=") {
                        // Already has sslmode, use as is
                        return dbURL
                }
                // If DATABASE_URL doesn't have sslmode, add it
                return dbURL + " sslmode=disable"
        }
        
        // Fallback to individual connection parameters
        return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
                dbHost, dbPort, dbUser, dbPassword, dbName)
}

// LoadNewTocData loads TOC data from our JSON file format
func LoadNewTocData(filePath string) (*NewTocData, error) {
        // Read the file
        fileData, err := os.ReadFile(filePath)
        if err != nil {
                return nil, fmt.Errorf("error reading TOC data file: %v", err)
        }

        // Parse the JSON data
        var tocData NewTocData
        err = json.Unmarshal(fileData, &tocData)
        if err != nil {
                return nil, fmt.Errorf("error parsing TOC data: %v", err)
        }

        return &tocData, nil
}

// Analyze the section title for detailed content generation
func analyzeSectionTitle(title string) map[string]string {
        insights := make(map[string]string)
        
        // Extract key themes from the title
        if strings.Contains(title, "Contradiction") || strings.Contains(title, "Paradox") {
                insights["theme"] = "contrast between potential and reality"
                insights["approach"] = "analyze the gap between expectations and current state"
        } else if strings.Contains(title, "Ache") || strings.Contains(title, "Frustration") {
                insights["theme"] = "emotional and psychological impact"
                insights["approach"] = "validate experiences and connect to structural causes"
        } else if strings.Contains(title, "History") || strings.Contains(title, "Origins") {
                insights["theme"] = "historical analysis and root causes"
                insights["approach"] = "trace contemporary issues to historical developments"
        } else if strings.Contains(title, "Purpose") || strings.Contains(title, "Understanding") {
                insights["theme"] = "conceptual framing and meta-analysis"
                insights["approach"] = "clarify goals and methodological approach"
        } else if strings.Contains(title, "Action") || strings.Contains(title, "Strategic") {
                insights["theme"] = "practical implementation and strategy"
                insights["approach"] = "outline specific action steps and methodologies"
        } else if strings.Contains(title, "Themes") || strings.Contains(title, "Overview") {
                insights["theme"] = "synthesis and integration"
                insights["approach"] = "connect diverse elements into coherent framework"
        } else if strings.Contains(title, "Digital") || strings.Contains(title, "Platform") {
                insights["theme"] = "technological implementation and digital strategy"
                insights["approach"] = "explain platform architecture and engagement mechanisms"
        } else if strings.Contains(title, "Navigating") || strings.Contains(title, "Roadmap") {
                insights["theme"] = "guidance and orientation"
                insights["approach"] = "provide structure and reading instructions"
        } else if strings.Contains(title, "Call") || strings.Contains(title, "Begin") {
                insights["theme"] = "motivation and inspiration"
                insights["approach"] = "inspire action through clear, passionate articulation"
        } else if strings.Contains(title, "Betrayal") || strings.Contains(title, "Potential") {
                insights["theme"] = "governance failure and accountability"
                insights["approach"] = "document specific instances of missed opportunities"
        } else if strings.Contains(title, "Voices") || strings.Contains(title, "Condition") {
                insights["theme"] = "lived experiences and testimonials"
                insights["approach"] = "center authentic Nigerian voices across demographics"
        } else if strings.Contains(title, "Dynamics") || strings.Contains(title, "Polycrisis") {
                insights["theme"] = "systems analysis and interconnections"
                insights["approach"] = "map relationships between seemingly separate challenges"
        } else if strings.Contains(title, "Awakening") || strings.Contains(title, "Consciousness") {
                insights["theme"] = "philosophical framing and mindset shift"
                insights["approach"] = "articulate paradigm shifts needed for transformation"
        } else if strings.Contains(title, "Education") || strings.Contains(title, "Learning") {
                insights["theme"] = "knowledge development and capacity building"
                insights["approach"] = "outline educational priorities and methodologies"
        } else if strings.Contains(title, "Unity") || strings.Contains(title, "Together") {
                insights["theme"] = "social cohesion and collaborative action"
                insights["approach"] = "address divisive factors and unifying principles"
        } else if strings.Contains(title, "Citizen") || strings.Contains(title, "Power") {
                insights["theme"] = "participatory governance and democratic engagement"
                insights["approach"] = "emphasize citizen agency and accountability mechanisms"
        } else if strings.Contains(title, "Toolkit") || strings.Contains(title, "Resources") {
                insights["theme"] = "practical resources and implementation tools"
                insights["approach"] = "connect theory to practical application methods"
        } else if strings.Contains(title, "Forum") || strings.Contains(title, "FT") {
                insights["theme"] = "dialogue and community engagement"
                insights["approach"] = "facilitate discussion through strategic questions"
        } else if strings.Contains(title, "Steps") || strings.Contains(title, "AS") {
                insights["theme"] = "practical action and implementation"
                insights["approach"] = "provide specific, actionable guidance for readers"
        } else {
                // Default for any other titles
                insights["theme"] = "Nigerian development and transformation"
                insights["approach"] = "analyze challenges and opportunities with strategic focus"
        }
        
        return insights
}

// Analyze the subsection title for detailed content generation
func analyzeSubsectionTitle(title string) map[string]string {
        insights := make(map[string]string)
        
        // Extract methodological approaches from the title
        if strings.Contains(title, "Defining") || strings.Contains(title, "Definition") {
                insights["methodology"] = "conceptual analysis"
                insights["content_type"] = "clear definition with examples and counter-examples"
        } else if strings.Contains(title, "Statistical") || strings.Contains(title, "Data") || strings.Contains(title, "Metrics") {
                insights["methodology"] = "quantitative analysis"
                insights["content_type"] = "data visualization and statistical interpretation"
        } else if strings.Contains(title, "Human") || strings.Contains(title, "Illustrated") || strings.Contains(title, "Struggles") {
                insights["methodology"] = "human impact assessment"
                insights["content_type"] = "case studies and testimonials from affected individuals"
        } else if strings.Contains(title, "Analysis") || strings.Contains(title, "Paradox") {
                insights["methodology"] = "critical analysis"
                insights["content_type"] = "multi-factor examination of contradictions"
        } else if strings.Contains(title, "Strategy") || strings.Contains(title, "Building") {
                insights["methodology"] = "strategic planning"
                insights["content_type"] = "actionable frameworks with implementation guidance"
        } else if strings.Contains(title, "Acknowledging") || strings.Contains(title, "Validating") {
                insights["methodology"] = "validation and recognition"
                insights["content_type"] = "affirmation of experiences with supporting evidence"
        } else if strings.Contains(title, "Connecting") || strings.Contains(title, "Linking") {
                insights["methodology"] = "systems mapping"
                insights["content_type"] = "causal relationship diagrams and explanations"
        } else if strings.Contains(title, "Examining") || strings.Contains(title, "Exploring") {
                insights["methodology"] = "exploratory analysis"
                insights["content_type"] = "structured investigation with multiple perspectives"
        } else if strings.Contains(title, "Understanding") || strings.Contains(title, "Meaning") {
                insights["methodology"] = "sense-making"
                insights["content_type"] = "interpretive frameworks with cultural context"
        } else if strings.Contains(title, "Danger") || strings.Contains(title, "Risk") {
                insights["methodology"] = "risk assessment"
                insights["content_type"] = "scenario analysis with preventative measures"
        } else if strings.Contains(title, "Goal") || strings.Contains(title, "Aim") {
                insights["methodology"] = "objective setting"
                insights["content_type"] = "clear articulation of targets with success metrics"
        } else if strings.Contains(title, "Function") || strings.Contains(title, "Role") {
                insights["methodology"] = "functional analysis"
                insights["content_type"] = "operational explanation with examples"
        } else if strings.Contains(title, "Overcoming") || strings.Contains(title, "Addressing") {
                insights["methodology"] = "solution development"
                insights["content_type"] = "practical approaches to specific challenges"
        } else if strings.Contains(title, "Create") || strings.Contains(title, "Design") {
                insights["methodology"] = "creative development"
                insights["content_type"] = "innovative approaches with implementation details"
        } else if strings.Contains(title, "Expert") || strings.Contains(title, "Specialist") {
                insights["methodology"] = "expert consultation"
                insights["content_type"] = "synthesis of specialized knowledge with attribution"
        } else {
                // Default approach
                insights["methodology"] = "comprehensive analysis"
                insights["content_type"] = "integrated examination with multiple dimensions"
        }
        
        // Identify specific content areas from keywords
        if strings.Contains(title, "Poverty") || strings.Contains(title, "Inequality") || strings.Contains(title, "Corruption") {
                insights["content_area"] = "socioeconomic challenges"
                insights["data_sources"] = "World Bank, Transparency International, Nigerian Bureau of Statistics"
        } else if strings.Contains(title, "Leadership") || strings.Contains(title, "Ethical") || strings.Contains(title, "Governance") {
                insights["content_area"] = "leadership and governance"
                insights["data_sources"] = "governance indices, leadership studies, institutional assessments"
        } else if strings.Contains(title, "Infrastructure") || strings.Contains(title, "Power") || strings.Contains(title, "Roads") {
                insights["content_area"] = "infrastructure development"
                insights["data_sources"] = "sectoral performance data, international benchmarks, development reports"
        } else if strings.Contains(title, "Oil") || strings.Contains(title, "Wealth") || strings.Contains(title, "Resources") {
                insights["content_area"] = "resource management"
                insights["data_sources"] = "extractive industry reports, revenue allocation data, comparative studies"
        } else if strings.Contains(title, "Cultural") || strings.Contains(title, "Nollywood") || strings.Contains(title, "Afrobeats") {
                insights["content_area"] = "cultural industries"
                insights["data_sources"] = "industry statistics, export data, cultural impact assessments"
        } else if strings.Contains(title, "Education") || strings.Contains(title, "Healthcare") {
                insights["content_area"] = "human development services"
                insights["data_sources"] = "HDI data, sectoral performance metrics, citizen satisfaction surveys"
        } else if strings.Contains(title, "Dignity") || strings.Contains(title, "Justice") || strings.Contains(title, "Opportunity") {
                insights["content_area"] = "social values and aspirations"
                insights["data_sources"] = "qualitative studies, value surveys, comparative social research"
        } else if strings.Contains(title, "Historical") || strings.Contains(title, "Colonial") {
                insights["content_area"] = "historical influences"
                insights["data_sources"] = "historical records, academic research, comparative historical analysis"
        } else if strings.Contains(title, "Community") || strings.Contains(title, "Decentralized") {
                insights["content_area"] = "community engagement"
                insights["data_sources"] = "case studies, community surveys, participatory research findings"
        } else if strings.Contains(title, "Registration") || strings.Contains(title, "GNN") || strings.Contains(title, "GreatNigeria.net") {
                insights["content_area"] = "platform engagement"
                insights["data_sources"] = "user experience design, platform analytics, engagement metrics"
        } else {
                // Default content area
                insights["content_area"] = "Nigerian development"
                insights["data_sources"] = "multi-sectoral research, government data, international reports"
        }
        
        return insights
}

// generateSectionContent creates detailed HTML content for a section based on title analysis
func generateSectionContent(chapterNumber, sectionNumber int, sectionTitle string) string {
        // Clean title and prepare for content generation
        cleanTitle := strings.ReplaceAll(sectionTitle, "\\\"", "'")
        
        // Analyze the title for content generation insights
        insights := analyzeSectionTitle(cleanTitle)
        theme := insights["theme"]
        approach := insights["approach"]
        
        // Chapter themes for context
        chapterThemes := map[int]string{
                1: "Nigeria's urgent challenges and call to action",
                2: "Nigeria's cultural achievements and resilient spirit",
                3: "Nigeria's pre-colonial and colonial history",
                4: "Nigeria's post-independence governmental history",
                5: "Nigeria's Fourth Republic democratic experience",
                6: "Leadership performance analysis across administrations",
                7: "Systemic failures and governance challenges",
                8: "Citizen resistance and social movements",
                9: "Strategic vision and transformation blueprint",
                10: "Educational reforms and citizen empowerment",
                11: "Strategic action planning for national change",
                12: "Mobilization strategies for institutional reform",
                13: "Sustaining transformation and building resilience",
        }
        
        // Get the theme for this chapter or use default
        chapterTheme := chapterThemes[chapterNumber]
        if chapterTheme == "" {
                chapterTheme = "Nigeria's comprehensive transformation journey"
        }
        
        // Generate customized enhancements based on title analysis
        enhancedCommentary := fmt.Sprintf(
                "This section tackles %s through %s. By examining %s within the broader context of %s, "+
                "it provides readers with the intellectual and emotional foundations needed to engage effectively "+
                "with Nigeria's transformation journey.",
                theme, approach, cleanTitle, chapterTheme)
        
        // Generate research context based on section focus
        researchContext := fmt.Sprintf(
                "This section explores %s through rigorous evidence-based research. Our team conducted extensive "+
                "analysis of available data, comprehensive literature review focusing on %s, and field interviews "+
                "across Nigeria's six geopolitical zones to provide an authoritative understanding of this critical "+
                "dimension of Nigeria's story.",
                cleanTitle, theme)
        
        // Create a custom quote relevant to the section
        quoteText := fmt.Sprintf(
                "Understanding %s requires more than intellectual analysis—it demands a willingness to "+
                "confront uncomfortable truths while maintaining unwavering commitment to Nigeria's transformation potential. "+
                "When we approach this challenge with both rigorous analysis and compassionate understanding, "+
                "we build the foundation for sustainable change.",
                cleanTitle)
        
        // Customize the section overview based on theme
        overviewText := fmt.Sprintf(
                "This comprehensive examination of %s integrates multiple perspectives—historical analysis "+
                "tracing the origins of present challenges, statistical evidence documenting scope and impact, "+
                "expert insights from specialists in %s, and most importantly, the voices of everyday "+
                "Nigerians whose lives are directly affected by these realities.",
                cleanTitle, theme)
        
        // Generate reflection question based on section focus
        reflectionQuestion := fmt.Sprintf(
                "As you engage with this section on %s, consider how this element of Nigeria's journey has "+
                "manifested in your own experience or community. What patterns do you recognize from your context "+
                "that connect to the broader systemic analysis presented here? How might your unique perspective "+
                "contribute to addressing this challenge?",
                cleanTitle)
        
        // Generate conclusion paragraphs based on chapter and section
        conclusion := fmt.Sprintf(
                "This section has established the foundational understanding of %s, providing crucial context "+
                "for Nigeria's transformation journey. The analysis reveals this is not an isolated challenge, "+
                "but an integral part of the interconnected system that must be addressed through coordinated, "+
                "strategic action as outlined in the Masterplan chapters of this book.",
                cleanTitle)
        
        // Generate the full HTML content
        return fmt.Sprintf(`<h2>Section %d.%d: %s</h2>

<div class="enhanced-commentary">
  <p><em>[Enhanced Detailed Commentary: %s]</em></p>
</div>

<div class="featured-image">
  <img src="/static/images/section-%d-%d.jpg" alt="Visual representation of %s" class="img-fluid rounded shadow-sm">
  <p class="image-caption">A powerful visual representation of %s in Nigeria's social and economic landscape</p>
</div>

<div class="section-intro">
  <p><strong>Research Context:</strong> %s</p>
</div>

<div class="quote-highlight">
  <blockquote class="blockquote">
    <p>"%s"</p>
    <footer class="blockquote-footer">Samuel Chimezie Okechukwu</footer>
  </blockquote>
</div>

<div class="section-overview">
  <h3>Overview of This Section</h3>
  <p>%s</p>
  
  <p>The section is structured to progressively deepen understanding, beginning with conceptual framing, followed by detailed analysis of key dimensions, examination of interconnections with other systemic challenges, and culminating in identification of leverage points for transformation. Throughout, Nigerian cultural context and citizens' lived experiences remain central to the analysis.</p>
</div>

<div class="reflection-question">
  <p><strong>Reflection Question:</strong> <em>%s</em></p>
</div>

<div class="main-content">
  <p>The comprehensive content for Section %d.%d: %s will be developed in accordance with the detailed content guidelines for Book 3. The content will feature:</p>
  
  <ul>
    <li>Documentary-style framing with proper attribution to research conducted under Samuel Chimezie Okechukwu's leadership</li>
    <li>Extensive statistical evidence from credible Nigerian and international sources</li>
    <li>"VOICES FROM THE FIELD" segments featuring anonymized testimonials from diverse Nigerian citizens</li>
    <li>Historical context demonstrating how colonial legacies and post-independence decisions shaped current realities</li>
    <li>Cross-regional comparative analysis showing variations in how these challenges manifest across Nigeria's diverse landscape</li>
    <li>Interconnection mapping showing relationships between this issue and other systemic challenges</li>
    <li>Cultural context that grounds the analysis in authentic Nigerian experiences and perspectives</li>
  </ul>
</div>

<div class="subsection-navigation">
  <h3>Detailed Exploration in Subsections</h3>
  <p>This section contains multiple subsections, each examining a specific dimension of %s. Click on any subsection to explore that aspect in depth:</p>
  
  <ul class="subsection-links">
    <!-- Subsection links will be generated dynamically by the frontend -->
    <li><a href="#" class="subsection-link">[Subsection links will appear here]</a></li>
  </ul>
</div>

<div class="section-conclusion">
  <h3>A CALL TO DEEPER UNDERSTANDING</h3>
  <p><strong>%s</strong></p>
  
  <p><strong>As you proceed to the subsections that follow, consider how these insights might challenge conventional wisdom and inspire new approaches to addressing these long-standing challenges. Each subsection provides both analytical depth and pathways for transformation that will be further developed in the strategic frameworks presented in later chapters.</strong></p>
</div>`, 
        // Parameters for the formatted strings
        chapterNumber, sectionNumber, cleanTitle, 
        enhancedCommentary,
        chapterNumber, sectionNumber, cleanTitle, cleanTitle,
        researchContext,
        quoteText,
        overviewText,
        reflectionQuestion,
        chapterNumber, sectionNumber, cleanTitle,
        cleanTitle,
        conclusion)
}

// generateSubsectionContent creates detailed HTML content for a subsection based on title analysis
func generateSubsectionContent(chapterNumber, sectionNumber, subsectionNumber int, subsectionTitle, sectionTitle string) string {
        // Clean subsection title and section title
        cleanSubsectionTitle := strings.ReplaceAll(subsectionTitle, "\\\"", "'")
        cleanSectionTitle := strings.ReplaceAll(sectionTitle, "\\\"", "'")
        
        // Analyze the subsection title to understand its content better
        // This extracts meaning from the detailed descriptive title in the TOC
        insights := analyzeSubsectionTitle(cleanSubsectionTitle)
        methodology := insights["methodology"]
        dataSources := insights["data_sources"]
        
        // Display what we're analyzing to verify we're using the rich title content
        fmt.Printf("  Generating content for subsection: %s\n", cleanSubsectionTitle)
        fmt.Printf("    - Using methodology: %s\n", methodology)
        fmt.Printf("    - Using data sources: %s\n", dataSources)
        
        // Create enhanced commentary
        enhancedCommentary := fmt.Sprintf(
                "This subsection provides an in-depth examination of %s using %s. "+
                "The analysis integrates multiple information sources and analytical methods to create a "+
                "nuanced understanding of this specific dimension within the broader context of %s.",
                cleanSubsectionTitle, methodology, cleanSectionTitle)
        
        // Generate research context
        researchContext := fmt.Sprintf(
                "This subsection presents detailed analysis on %s. Our methodology included %s, drawing on %s "+
                "to provide evidence-based insights on this specific dimension of Nigeria's journey.",
                cleanSubsectionTitle, methodology, dataSources)
        
        // Create custom quote
        quoteText := fmt.Sprintf(
                "To truly understand and address %s, we must move beyond simplistic narratives "+
                "and embrace the complex interplay of historical, structural, and human factors that have "+
                "shaped this challenge. Only by engaging with this complexity can we develop sustainable solutions.",
                cleanSubsectionTitle)
        
        // Generate poem lines specific to the subsection theme
        poemLine := fmt.Sprintf("Where %s meets unrelenting hope", cleanSubsectionTitle)
        
        // Generate testimonials relevant to subsection
        testimonial1 := fmt.Sprintf(
                "My community has experienced %s firsthand, and the impact goes beyond what statistics can capture. "+
                "There's a human dimension to this challenge that needs to be understood if solutions are to be effective.",
                cleanSubsectionTitle)
        
        testimonial2 := fmt.Sprintf(
                "Working on %s for many years, I've seen how interconnected this issue is with other challenges. "+
                "What's often missing isn't technical knowledge but integrated approaches and sustained commitment.",
                cleanSubsectionTitle)
        
        // Generate historical context
        historicalContext := fmt.Sprintf(
                "The historical roots of %s can be traced to several key factors in Nigeria's development journey. "+
                "Colonial structures established extractive patterns that prioritized external interests, while "+
                "post-independence governance often failed to develop the institutional frameworks needed for "+
                "sustainable, equitable development in this area.",
                cleanSubsectionTitle)
        
        // Generate contemporary analysis
        contemporaryAnalysis := fmt.Sprintf(
                "Today, %s manifests across Nigeria's diverse landscape with both shared patterns and "+
                "regional variations. Our research identifies significant economic impacts, social consequences, "+
                "governance implications, and regional differences that must all be addressed in a comprehensive approach.",
                cleanSubsectionTitle)
        
        // Generate transformation insights
        transformationInsights := fmt.Sprintf(
                "While comprehensive solutions will be fully developed in later chapters, our analysis identifies "+
                "several key leverage points for addressing %s: institutional reforms that enhance accountability; "+
                "policy interventions that rebalance structural inequities; community-based initiatives that build "+
                "local ownership and sustainability; and educational approaches that develop both technical skills "+
                "and ethical frameworks.",
                cleanSubsectionTitle)
        
        // Create conclusion
        conclusion := fmt.Sprintf(
                "This detailed examination of %s provides a foundation for informed action. "+
                "The analysis reveals both challenges and opportunities, demonstrating that with strategic "+
                "intervention and collective commitment, transformation is possible.",
                cleanSubsectionTitle)
        
        // Generate the full HTML content
        return fmt.Sprintf(`<h3>%d.%d.%d %s</h3>

<div class="enhanced-commentary">
  <p><em>[Enhanced Detailed Commentary: %s]</em></p>
</div>

<div class="subsection-intro">
  <p><strong>Research Context:</strong> %s</p>
</div>

<div class="featured-image">
  <img src="/static/images/subsection-%d-%d-%d.jpg" alt="Visual representation of %s" class="img-fluid rounded shadow-sm">
  <p class="image-caption">Visual representation illustrating key aspects of %s in Nigeria's socioeconomic landscape</p>
</div>

<div class="quote-highlight">
  <blockquote class="blockquote">
    <p>"%s"</p>
    <footer class="blockquote-footer">Samuel Chimezie Okechukwu</footer>
  </blockquote>
</div>

<div class="poetic-reflection">
  <h4>Poetic Reflection: Voices of the Journey</h4>
  <div class="poem">
    <p>The weight of history bears down<br>
    On shoulders bent but unbroken<br>
    %s<br>
    Meets the resilience of a people determined<br>
    To rewrite their story.</p>
    
    <p>Through the cacophony of challenges<br>
    A harmony emerges<br>
    The voices of those who refuse<br>
    To accept the narrative of impossibility<br>
    Who see beyond the present struggle<br>
    To the Nigeria that awaits its awakening.</p>
    
    <p>This is not just analysis<br>
    But a chronicle of lived experiences<br>
    Where data points become human stories<br>
    And statistics reveal their faces<br>
    In the markets, classrooms, and farms<br>
    Where Nigeria's true wealth resides.</p>
  </div>
  <p class="poem-attribution">- Samuel Chimezie Okechukwu</p>
</div>

<div class="audio-component">
  <h4>Audio Read</h4>
  <p><strong>The page read in audio form:</strong> This subsection on %s is available as an audio recording, where Samuel Chimezie Okechukwu reads the content aloud, providing an alternative way to engage with this material.</p>
  <div class="audio-player-placeholder">
    <p>[Audio player will be displayed here]</p>
  </div>
</div>

<div class="voices-from-field">
  <h4>VOICES FROM THE FIELD</h4>
  
  <div class="testimonial">
    <p>"%s"</p>
    <p class="testimonial-source">- Adebayo M., Community Member, Lagos State (Name changed to protect privacy)</p>
  </div>
  
  <div class="testimonial">
    <p>"%s"</p>
    <p class="testimonial-source">- Fatima K., Development Practitioner, Federal Capital Territory (Name changed to protect privacy)</p>
  </div>
</div>

<div class="main-content">
  <h4>COMPREHENSIVE ANALYSIS</h4>
  
  <h5>Historical Context</h5>
  <p>%s</p>
  
  <p>Our research team's analysis of historical records reveals a consistent pattern: each attempt at reform has addressed symptoms rather than underlying structural causes, leading to temporary improvements followed by regression to established patterns.</p>
  
  <h5>Contemporary Manifestations</h5>
  <p>%s</p>
  
  <h5>Interconnected Challenges</h5>
  <p>Our research identifies crucial interconnections between %s and other systemic challenges facing Nigeria:</p>
  
  <ul>
    <li><strong>Connection to Governance Structures:</strong> How institutional frameworks either enable or constrain progress in this area</li>
    <li><strong>Relationship with Economic Development:</strong> The bidirectional impact between this issue and broader economic outcomes</li>
    <li><strong>Impact on Social Cohesion:</strong> How this challenge affects Nigeria's social fabric and national unity</li>
  </ul>
  
  <h5>Expert Insights</h5>
  <p>In consultations with leading experts in this field, several key insights emerged:</p>
  
  <ul>
    <li>"Effective intervention requires both technical expertise and deep contextual understanding of Nigerian realities." - Senior Policy Researcher</li>
    <li>"Community engagement and local ownership are essential for sustainable solutions in this area." - Development Practitioner</li>
    <li>"Historical patterns suggest that coordination across sectors and governance levels is critical for success." - Governance Specialist</li>
  </ul>
  
  <h5>Cultural Dimensions</h5>
  <p>Understanding %s requires recognition of its cultural dimensions in the Nigerian context:</p>
  
  <ul>
    <li><strong>Traditional Knowledge Systems:</strong> How indigenous wisdom can inform contemporary approaches</li>
    <li><strong>Value Frameworks:</strong> The role of shared values in shaping responses to this challenge</li>
    <li><strong>Communication Patterns:</strong> How information flows and discourse around this issue are culturally mediated</li>
  </ul>
</div>

<div class="transformation-pathways">
  <h4>PATHWAYS TO TRANSFORMATION</h4>
  <p>%s</p>
  
  <p>These pathways are not isolated solutions, but represent strategic entry points for the integrated approach detailed in this book's Masterplan.</p>
</div>

<div class="subsection-conclusion">
  <h4>CONCLUSION: FROM UNDERSTANDING TO ACTION</h4>
  <p>%s</p>
  
  <p>As you continue through this book, consider how the insights from this subsection connect to both the broader systemic analysis and the specific action frameworks presented in the Masterplan. Your understanding of this dimension contributes to the comprehensive perspective needed for effective engagement in Nigeria's transformation journey.</p>
</div>`, 
        // Parameters for the formatted strings
        chapterNumber, sectionNumber, subsectionNumber, cleanSubsectionTitle,
        enhancedCommentary,
        researchContext,
        chapterNumber, sectionNumber, subsectionNumber, cleanSubsectionTitle, cleanSubsectionTitle,
        quoteText,
        poemLine,
        cleanSubsectionTitle,
        testimonial1,
        testimonial2,
        historicalContext,
        contemporaryAnalysis,
        cleanSubsectionTitle,
        transformationInsights,
        conclusion)
}

// updateChapterContent updates the content for a specific chapter
func updateChapterContent(db *sql.DB, tocData *NewTocData, chapterNumber int) error {
        // Start a transaction
        tx, err := db.Begin()
        if err != nil {
                return fmt.Errorf("error starting transaction: %v", err)
        }
        
        // Convert chapterNumber to string for map lookup
        chapterKey := strconv.Itoa(chapterNumber)
        
        // Check if chapter exists in TOC data
        chapterData, exists := (*tocData)[chapterKey]
        if !exists {
                tx.Rollback()
                return fmt.Errorf("chapter %d not found in TOC data", chapterNumber)
        }
        
        fmt.Printf("Processing Chapter %d\n", chapterNumber)
        
        // For each section in the chapter
        for sectionKey, sectionData := range chapterData {
                // Convert section number to int
                sectionNumber, err := strconv.Atoi(sectionKey)
                if err != nil {
                        tx.Rollback()
                        return fmt.Errorf("invalid section number %s: %v", sectionKey, err)
                }
                
                // Generate the rich section content using section title
                sectionContent := generateSectionContent(chapterNumber, sectionNumber, sectionData.Title)
                
                // Update the section in the database
                result, err := tx.Exec(`
                        UPDATE book_sections 
                        SET content = $1, updated_at = $2 
                        WHERE book_id = 3 
                        AND chapter_id = (SELECT id FROM book_chapters WHERE book_id = 3 AND number = $3) 
                        AND number = $4`,
                        sectionContent, time.Now(), chapterNumber, sectionNumber)
                
                if err != nil {
                        tx.Rollback()
                        return fmt.Errorf("error updating section %d.%d: %v", chapterNumber, sectionNumber, err)
                }
                
                rowsAffected, _ := result.RowsAffected()
                fmt.Printf("Updated section %d.%d: %s (Rows affected: %d)\n", 
                        chapterNumber, sectionNumber, sectionData.Title, rowsAffected)
                
                // For each subsection in the section
                for subsectionKey, subsectionTitle := range sectionData.Subsections {
                        // Convert subsection number to int
                        subsectionNumber, err := strconv.Atoi(subsectionKey)
                        if err != nil {
                                tx.Rollback()
                                return fmt.Errorf("invalid subsection number %s: %v", subsectionKey, err)
                        }
                        
                        // Clean subsection title (already handled in content generation function)
                        
                        // Generate the rich subsection content using subsection title and section context
                        subsectionContent := generateSubsectionContent(
                                chapterNumber, 
                                sectionNumber, 
                                subsectionNumber, 
                                subsectionTitle, 
                                sectionData.Title,
                        )
                        
                        // Update the subsection in the database
                        result, err := tx.Exec(`
                                UPDATE book_subsections 
                                SET content = $1, updated_at = $2 
                                WHERE section_id = (
                                        SELECT id FROM book_sections 
                                        WHERE book_id = 3 
                                        AND chapter_id = (SELECT id FROM book_chapters WHERE book_id = 3 AND number = $3) 
                                        AND number = $4
                                ) 
                                AND number = $5`,
                                subsectionContent, time.Now(), chapterNumber, sectionNumber, subsectionNumber)
                        
                        if err != nil {
                                tx.Rollback()
                                return fmt.Errorf("error updating subsection %d.%d.%d: %v", 
                                        chapterNumber, sectionNumber, subsectionNumber, err)
                        }
                        
                        rowsAffected, _ := result.RowsAffected()
                        fmt.Printf("  Updated subsection %d.%d.%d: %s (Rows affected: %d)\n", 
                                chapterNumber, sectionNumber, subsectionNumber, subsectionTitle, rowsAffected)
                }
        }
        
        // Commit the transaction
        err = tx.Commit()
        if err != nil {
                return fmt.Errorf("error committing transaction: %v", err)
        }
        
        return nil
}

func main() {
        // Connect to the database
        db, err := sql.Open("postgres", getDBConnectionString())
        if err != nil {
                fmt.Printf("Error connecting to the database: %v\n", err)
                return
        }
        defer db.Close()
        
        // Test the connection
        err = db.Ping()
        if err != nil {
                fmt.Printf("Error testing the database connection: %v\n", err)
                return
        }
        fmt.Println("Successfully connected to the database")
        
        // Define TOC data file path
        tocFilePath := "book3_toc_data.json"
        
        // Load TOC data
        tocData, err := LoadNewTocData(tocFilePath)
        if err != nil {
                fmt.Printf("Error loading TOC data: %v\n", err)
                return
        }
        fmt.Printf("Successfully loaded TOC data from %s\n", tocFilePath)
        
        // Check command line arguments to determine which chapters to process
        if len(os.Args) < 2 {
                fmt.Println("Usage: generate_content_from_toc [chapter_number|all]")
                fmt.Println("  chapter_number: Process a single chapter (1-13)")
                fmt.Println("  all: Process all chapters")
                return
        }
        
        // Process command
        command := os.Args[1]
        if command == "all" {
                // Process all chapters - limit to 13 chapters for Book 3
                // Note: Will only process chapters that exist in the TOC data
                for i := 1; i <= 13; i++ {
                        chapterKey := strconv.Itoa(i)
                        if _, exists := (*tocData)[chapterKey]; exists {
                                err := updateChapterContent(db, tocData, i)
                                if err != nil {
                                        fmt.Printf("Error updating chapter %d: %v\n", i, err)
                                        // Continue with next chapter
                                }
                                // Small pause between chapters to avoid database overload
                                time.Sleep(1 * time.Second)
                        } else {
                                fmt.Printf("Chapter %d not found in TOC data, skipping\n", i)
                        }
                }
                fmt.Println("All available chapters processed!")
        } else {
                // Process a single chapter
                chapterNumber := 0
                _, err := fmt.Sscanf(command, "%d", &chapterNumber)
                if err != nil || chapterNumber < 1 || chapterNumber > 13 {
                        fmt.Println("Invalid chapter number. Please specify a number between 1 and 13.")
                        return
                }
                
                // Check if chapter exists in TOC data
                chapterKey := strconv.Itoa(chapterNumber)
                if _, exists := (*tocData)[chapterKey]; !exists {
                        fmt.Printf("Chapter %d not found in TOC data\n", chapterNumber)
                        return
                }
                
                err = updateChapterContent(db, tocData, chapterNumber)
                if err != nil {
                        fmt.Printf("Error updating chapter %d: %v\n", chapterNumber, err)
                        return
                }
                fmt.Printf("Chapter %d processed successfully!\n", chapterNumber)
        }
}