package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
)

func main() {
	// Define the directory to serve
	staticDir := "./web/static"

	// Check if the directory exists
	if _, err := os.Stat(staticDir); os.IsNotExist(err) {
		log.Fatalf("Error: Directory %s does not exist", staticDir)
	}

	// Get absolute path for better logging
	absPath, err := filepath.Abs(staticDir)
	if err != nil {
		log.Fatalf("Error getting absolute path: %v", err)
	}

	// Create a file server handler
	fs := http.FileServer(http.Dir(staticDir))

	// Define the handler function
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// Log the request
		fmt.Printf("Request: %s %s\n", r.Method, r.URL.Path)
		
		// Serve the file
		fs.ServeHTTP(w, r)
	})

	// Define the port
	port := "5000"

	// Start the server
	fmt.Printf("Starting web server on http://localhost:%s\n", port)
	fmt.Printf("Serving files from: %s\n", absPath)
	fmt.Printf("Try opening: http://localhost:%s/index.html\n", port)
	
	log.Fatal(http.ListenAndServe(":"+port, nil))
}
