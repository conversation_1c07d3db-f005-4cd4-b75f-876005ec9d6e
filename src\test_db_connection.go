package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/lib/pq"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables from .env file
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file: ", err)
	}

	// Get database connection details from environment variables
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbName := os.Getenv("DB_NAME")

	// Create connection string
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Connect to database
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatal("Error connecting to database: ", err)
	}
	defer db.Close()

	// Test the connection
	err = db.<PERSON>()
	if err != nil {
		log.Fatal("Error pinging database: ", err)
	}

	fmt.Println("Successfully connected to the database!")

	// Query to get table names
	rows, err := db.Query(`
		SELECT table_name 
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		ORDER BY table_name
	`)
	if err != nil {
		log.Fatal("Error querying tables: ", err)
	}
	defer rows.Close()

	// Print table names
	fmt.Println("\nDatabase tables:")
	fmt.Println("----------------")
	
	var tableName string
	tableCount := 0
	
	for rows.Next() {
		err := rows.Scan(&tableName)
		if err != nil {
			log.Fatal("Error scanning row: ", err)
		}
		fmt.Println(tableName)
		tableCount++
	}
	
	fmt.Printf("\nTotal tables: %d\n", tableCount)
	
	fmt.Println("\nDatabase connection test completed successfully!")
}
