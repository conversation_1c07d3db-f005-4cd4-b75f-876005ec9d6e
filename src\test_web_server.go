package main

import (
	"database/sql"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"os"

	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
)

type Book struct {
	ID          int
	Title       string
	Author      string
	Description string
}

func main() {
	// Load environment variables from .env file
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file: ", err)
	}

	// Get database connection details from environment variables
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbName := os.Getenv("DB_NAME")

	// Create connection string
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// Connect to database
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatal("Error connecting to database: ", err)
	}
	defer db.Close()

	// Test the connection
	err = db.Ping()
	if err != nil {
		log.Fatal("Error pinging database: ", err)
	}

	fmt.Println("Successfully connected to the database!")

	// Define HTML template
	tmpl := template.Must(template.New("index").Parse(`
<!DOCTYPE html>
<html>
<head>
    <title>Great Nigeria Library</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #006600;
            text-align: center;
        }
        .book {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
        }
        .book h2 {
            margin-top: 0;
            color: #006600;
        }
        .book p {
            margin-bottom: 5px;
        }
        .status {
            background-color: #e6ffe6;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Great Nigeria Library</h1>

        <div class="status">
            <h2>System Status</h2>
            <p>Database Connection: <strong>Connected</strong></p>
            <p>Tables Found: <strong>{{.TableCount}}</strong></p>
            <p>Books Found: <strong>{{len .Books}}</strong></p>
        </div>

        <h2>Available Books</h2>
        {{range .Books}}
        <div class="book">
            <h2>{{.Title}}</h2>
            <p><strong>Author:</strong> {{.Author}}</p>
            <p>{{.Description}}</p>
        </div>
        {{else}}
        <p>No books found in the database.</p>
        {{end}}
    </div>
</body>
</html>
`))

	// Define handler for root path
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// Get table count
		var tableCount int
		err := db.QueryRow(`
			SELECT COUNT(*)
			FROM information_schema.tables
			WHERE table_schema = 'public'
		`).Scan(&tableCount)
		if err != nil {
			http.Error(w, "Error querying database", http.StatusInternalServerError)
			log.Println("Error querying table count:", err)
			return
		}

		// Get books from database
		rows, err := db.Query(`
			SELECT id, title, COALESCE(author, 'Unknown') as author, COALESCE(description, 'No description available.') as description
			FROM books
			LIMIT 10
		`)
		if err != nil {
			http.Error(w, "Error querying database", http.StatusInternalServerError)
			log.Println("Error querying books:", err)
			return
		}
		defer rows.Close()

		// Parse books
		var books []Book
		for rows.Next() {
			var book Book
			err := rows.Scan(&book.ID, &book.Title, &book.Author, &book.Description)
			if err != nil {
				http.Error(w, "Error scanning database results", http.StatusInternalServerError)
				log.Println("Error scanning book row:", err)
				return
			}
			books = append(books, book)
		}

		// Render template
		data := struct {
			TableCount int
			Books      []Book
		}{
			TableCount: tableCount,
			Books:      books,
		}

		err = tmpl.Execute(w, data)
		if err != nil {
			http.Error(w, "Error rendering template", http.StatusInternalServerError)
			log.Println("Error rendering template:", err)
			return
		}
	})

	// Start server
	port := "3000" // Use a fixed port that's likely to be available

	fmt.Printf("Starting web server on http://localhost:%s\n", port)
	log.Fatal(http.ListenAndServe(":"+port, nil))
}
